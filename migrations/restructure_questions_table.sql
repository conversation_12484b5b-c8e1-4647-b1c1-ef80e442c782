-- 重新架构题库表结构
-- 执行时间: 2024-01-01
-- 说明: 根据S1需求重新设计题库表，支持完整的题目管理功能

-- 备份现有数据
CREATE TABLE IF NOT EXISTS `questions_backup` AS SELECT * FROM `questions`;

-- 添加新字段到 questions 表
ALTER TABLE questions 
ADD COLUMN cache_key VARCHAR(128) COMMENT '缓存键，禁止修改，允许为空' AFTER hash,
ADD COLUMN question_doc TEXT COMMENT '题目内容' AFTER cache_key,
ADD COLUMN question_img TEXT COMMENT '题目图片' AFTER question_doc,
ADD COLUMN question_img_raw TEXT COMMENT '用户图片' AFTER question_img,
ADD COLUMN options_a TEXT COMMENT '选项A' AFTER question_img_raw,
ADD COLUMN options_b TEXT COMMENT '选项B' AFTER options_a,
ADD COLUMN options_c TEXT COMMENT '选项C' AFTER options_b,
ADD COLUMN options_d TEXT COMMENT '选项D' AFTER options_c,
ADD COLUMN options_y TEXT COMMENT '选项Y' AFTER options_d,
ADD COLUMN options_n TEXT COMMENT '选项N' AFTER options_y,
ADD COLUMN response INT DEFAULT 0 COMMENT '响应次数，不允许修改' AFTER analysis,
ADD COLUMN raw_qwen LONGTEXT COMMENT 'qwen原始返回的数据，不允许修改' AFTER response,
ADD COLUMN raw_deepseek LONGTEXT COMMENT 'deepseek原始返回的数据，不允许修改' AFTER raw_qwen,
ADD COLUMN associates TEXT COMMENT '关联键' AFTER raw_deepseek;

-- 修改现有字段注释
ALTER TABLE questions MODIFY COLUMN hash VARCHAR(64) COMMENT '题目哈希值（兼容）';
ALTER TABLE questions MODIFY COLUMN content TEXT COMMENT '题目内容（兼容）';
ALTER TABLE questions MODIFY COLUMN question_type VARCHAR(20) COMMENT '题目类型';
ALTER TABLE questions MODIFY COLUMN question_text TEXT COMMENT '结构化题目内容（兼容）';
ALTER TABLE questions MODIFY COLUMN options TEXT COMMENT '选项JSON格式（兼容）';
ALTER TABLE questions MODIFY COLUMN analysis TEXT COMMENT '答案解析';
ALTER TABLE questions MODIFY COLUMN answer TEXT COMMENT '正确答案 A,B/Y/N';
ALTER TABLE questions MODIFY COLUMN subject VARCHAR(20) COMMENT '学科';
ALTER TABLE questions MODIFY COLUMN grade VARCHAR(20) COMMENT '年级';
ALTER TABLE questions MODIFY COLUMN difficulty INT COMMENT '难度 1-5';
ALTER TABLE questions MODIFY COLUMN source_model VARCHAR(50) COMMENT '来源模型';
ALTER TABLE questions MODIFY COLUMN raw_content TEXT COMMENT '原始识别内容（兼容）';

-- 添加索引
CREATE INDEX idx_questions_cache_key ON questions(cache_key);
CREATE INDEX idx_questions_question_type_new ON questions(question_type);
CREATE INDEX idx_questions_associates ON questions(associates(100));
CREATE INDEX idx_questions_response ON questions(response);

-- 更新现有数据：将现有数据迁移到新字段
UPDATE questions 
SET 
    question_doc = COALESCE(question_text, content),
    cache_key = hash
WHERE question_doc IS NULL OR question_doc = '';

-- 验证数据迁移
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN cache_key IS NOT NULL THEN 1 END) as with_cache_key,
    COUNT(CASE WHEN question_doc IS NOT NULL THEN 1 END) as with_question_doc
FROM questions;

-- 添加注释说明新架构
ALTER TABLE questions COMMENT = '题库表 - 重新架构版本，支持完整的题目管理功能';
