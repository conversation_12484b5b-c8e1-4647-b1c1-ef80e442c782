-- 彻底优化questions表结构 - 移除所有冗余字段
-- 执行时间: 2024-01-01
-- 说明: 彻底清理历史遗留的冗余字段，保留核心业务字段

-- 1. 备份现有数据（包含所有字段）
CREATE TABLE IF NOT EXISTS `questions_backup_final` AS SELECT * FROM `questions`;

-- 2. 查看当前表结构（用于确认字段）
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'questions'
ORDER BY ORDINAL_POSITION;

-- 3. 数据迁移：统一内容字段到question_text
UPDATE questions
SET question_text = CASE
    WHEN question_text IS NOT NULL AND question_text != '' THEN question_text
    WHEN question_doc IS NOT NULL AND question_doc != '' THEN question_doc
    WHEN content IS NOT NULL AND content != '' THEN content
    ELSE ''
END
WHERE question_text IS NULL OR question_text = '';

-- 4. 数据迁移：统一缓存键到cache_key
UPDATE questions
SET cache_key = CASE
    WHEN cache_key IS NOT NULL AND cache_key != '' THEN cache_key
    WHEN hash IS NOT NULL AND hash != '' THEN hash
    ELSE MD5(CONCAT(COALESCE(question_type, ''), ':', COALESCE(question_text, '')))
END
WHERE cache_key IS NULL OR cache_key = '';

-- 5. 数据迁移：从JSON options提取到分散字段（如果需要）
UPDATE questions
SET
    options_a = COALESCE(NULLIF(options_a, ''),
        CASE WHEN JSON_VALID(options) THEN JSON_UNQUOTE(JSON_EXTRACT(options, '$.A')) ELSE '' END, ''),
    options_b = COALESCE(NULLIF(options_b, ''),
        CASE WHEN JSON_VALID(options) THEN JSON_UNQUOTE(JSON_EXTRACT(options, '$.B')) ELSE '' END, ''),
    options_c = COALESCE(NULLIF(options_c, ''),
        CASE WHEN JSON_VALID(options) THEN JSON_UNQUOTE(JSON_EXTRACT(options, '$.C')) ELSE '' END, ''),
    options_d = COALESCE(NULLIF(options_d, ''),
        CASE WHEN JSON_VALID(options) THEN JSON_UNQUOTE(JSON_EXTRACT(options, '$.D')) ELSE '' END, ''),
    options_y = COALESCE(NULLIF(options_y, ''),
        CASE WHEN JSON_VALID(options) THEN JSON_UNQUOTE(JSON_EXTRACT(options, '$.Y')) ELSE '' END, ''),
    options_n = COALESCE(NULLIF(options_n, ''),
        CASE WHEN JSON_VALID(options) THEN JSON_UNQUOTE(JSON_EXTRACT(options, '$.N')) ELSE '' END, '')
WHERE options IS NOT NULL AND options != '';

-- 6. 验证数据迁移
SELECT
    COUNT(*) as total_records,
    COUNT(CASE WHEN question_text IS NOT NULL AND question_text != '' THEN 1 END) as with_question_text,
    COUNT(CASE WHEN cache_key IS NOT NULL AND cache_key != '' THEN 1 END) as with_cache_key,
    COUNT(CASE WHEN options_a != '' OR options_b != '' OR options_c != '' OR options_d != '' THEN 1 END) as with_choice_options,
    COUNT(CASE WHEN options_y != '' OR options_n != '' THEN 1 END) as with_judgment_options
FROM questions;

-- 7. 彻底删除所有冗余字段（按批次执行，确保数据安全）

-- 第一批：删除重复的内容字段
ALTER TABLE questions DROP COLUMN IF EXISTS content;           -- 与question_text重复
ALTER TABLE questions DROP COLUMN IF EXISTS question_doc;      -- 与question_text重复

-- 第二批：删除重复的哈希/缓存字段
ALTER TABLE questions DROP COLUMN IF EXISTS hash;              -- 与cache_key重复

-- 第三批：删除重复的原始内容字段
ALTER TABLE questions DROP COLUMN IF EXISTS raw_content;       -- 与raw_qwen重复

-- 第四批：删除JSON选项字段（保留分散字段）
ALTER TABLE questions DROP COLUMN IF EXISTS options;           -- 删除JSON格式，保留分散字段

-- 第五批：删除很少使用的字段
ALTER TABLE questions DROP COLUMN IF EXISTS question_img;      -- 很少使用，保留question_img_raw
ALTER TABLE questions DROP COLUMN IF EXISTS associates;        -- 关联逻辑复杂，很少使用

-- 8. 清理和优化索引
-- 删除基于已删除字段的旧索引
DROP INDEX IF EXISTS idx_questions_hash ON questions;
DROP INDEX IF EXISTS idx_questions_question_type_new ON questions;
DROP INDEX IF EXISTS idx_questions_associates ON questions;
DROP INDEX IF EXISTS idx_questions_cache_key ON questions;  -- 删除旧的非唯一索引

-- 创建新的优化索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_questions_cache_key_unique ON questions(cache_key);
CREATE INDEX IF NOT EXISTS idx_questions_question_type ON questions(question_type);
CREATE INDEX IF NOT EXISTS idx_questions_subject_grade ON questions(subject, grade);
CREATE INDEX IF NOT EXISTS idx_questions_difficulty ON questions(difficulty);
CREATE INDEX IF NOT EXISTS idx_questions_response ON questions(response);
CREATE INDEX IF NOT EXISTS idx_questions_created_at ON questions(created_at);

-- 9. 优化字段约束和默认值
ALTER TABLE questions
MODIFY COLUMN cache_key VARCHAR(128) NOT NULL COMMENT '缓存键，基于题目内容生成',
MODIFY COLUMN question_type VARCHAR(20) NOT NULL COMMENT '题目类型',
MODIFY COLUMN question_text TEXT NOT NULL COMMENT '题目内容',
MODIFY COLUMN options_a TEXT COMMENT '选项A',
MODIFY COLUMN options_b TEXT COMMENT '选项B',
MODIFY COLUMN options_c TEXT COMMENT '选项C',
MODIFY COLUMN options_d TEXT COMMENT '选项D',
MODIFY COLUMN options_y TEXT COMMENT '选项Y（正确）',
MODIFY COLUMN options_n TEXT COMMENT '选项N（错误）',
MODIFY COLUMN answer TEXT NOT NULL COMMENT '正确答案',
MODIFY COLUMN analysis TEXT COMMENT '答案解析',
MODIFY COLUMN response INT DEFAULT 0 NOT NULL COMMENT '响应次数',
MODIFY COLUMN question_img_raw TEXT COMMENT '用户原始图片URL',
MODIFY COLUMN subject VARCHAR(20) DEFAULT '未知' COMMENT '学科',
MODIFY COLUMN grade VARCHAR(20) DEFAULT '未知' COMMENT '年级',
MODIFY COLUMN difficulty INT DEFAULT 3 COMMENT '难度 1-5',
MODIFY COLUMN source_model VARCHAR(50) DEFAULT 'unknown' COMMENT '来源模型',
MODIFY COLUMN raw_qwen LONGTEXT COMMENT 'Qwen原始返回数据',
MODIFY COLUMN raw_deepseek LONGTEXT COMMENT 'DeepSeek原始返回数据';

-- 10. 更新表注释
ALTER TABLE questions COMMENT = '题库表 - 彻底优化版本，移除所有冗余字段，保留核心业务字段';

-- 11. 最终验证 - 显示优化后的表结构
SELECT
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '允许空值',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '注释'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'questions'
ORDER BY ORDINAL_POSITION;

-- 12. 统计优化效果
SELECT
    '优化前字段数' as '指标',
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'questions_backup_final') as '数值'
UNION ALL
SELECT
    '优化后字段数' as '指标',
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'questions') as '数值'
UNION ALL
SELECT
    '减少字段数' as '指标',
    ((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'questions_backup_final') -
     (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'questions')) as '数值';

-- 13. 显示最终保留的字段列表
SELECT
    CONCAT('✅ ', COLUMN_NAME, ' - ', COLUMN_COMMENT) as '保留字段'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'questions'
ORDER BY ORDINAL_POSITION;
