-- 模型配置表结构优化：使用name作为主键
-- 基于qwen-vl-plus和deepseek-chat的唯一性特征

-- 1. 备份现有数据
CREATE TABLE IF NOT EXISTS `model_configs_backup` AS SELECT * FROM `model_configs`;

-- 2. 删除现有表（注意：这会删除所有数据）
DROP TABLE IF EXISTS `model_configs`;

-- 3. 创建新的模型配置表（使用name作为主键）
CREATE TABLE `model_configs` (
  `name` varchar(50) NOT NULL COMMENT '模型名称（主键）',
  `api_url` varchar(255) NOT NULL COMMENT 'API地址',
  `api_key` varchar(100) NOT NULL COMMENT 'API密钥',
  `params` text COMMENT 'JSON格式的参数配置',
  `status` int NOT NULL DEFAULT '1' COMMENT '1:启用 2:禁用',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模型配置表（优化版）';

-- 4. 插入固定的两个模型配置
INSERT INTO `model_configs` (`name`, `api_url`, `api_key`, `params`, `status`, `created_at`, `updated_at`) VALUES
('qwen-vl-plus', 'https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation', 'sk-placeholder', '{"temperature":0.3,"max_tokens":1500,"top_p":0.8,"response_format":{"type":"json_object"},"detail":"high"}', 1, NOW(3), NOW(3)),
('deepseek-chat', 'https://api.deepseek.com/v1/chat/completions', 'sk-placeholder', '{"temperature":0.3,"max_tokens":2500,"top_p":0.8,"response_format":{"type":"json_object"}}', 1, NOW(3), NOW(3));

-- 5. 验证数据
SELECT 
    name,
    api_url,
    CASE WHEN api_key = 'sk-placeholder' THEN '需要配置' ELSE '已配置' END as api_key_status,
    params,
    CASE WHEN status = 1 THEN '启用' ELSE '禁用' END as status_name,
    created_at,
    updated_at
FROM model_configs
ORDER BY name;

-- 6. 清理备份表（可选，建议保留一段时间）
-- DROP TABLE IF EXISTS `model_configs_backup`;
