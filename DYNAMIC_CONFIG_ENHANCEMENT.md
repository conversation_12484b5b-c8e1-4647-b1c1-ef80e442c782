# 动态配置增强文档

## 🎯 概述

基于您的需求，我们已经将以下关键参数作为动态配置添加到HTML管理界面中：

- ✅ **Model** - 模型名称
- ✅ **System Message** - 系统提示消息
- ✅ **User Message** - 用户提示消息  
- ✅ **Response Format** - 响应格式

## 🔧 新增动态配置

### 1. 模型配置 (🤖 Model Configuration)

| 参数 | 类型 | 说明 | Qwen默认值 | DeepSeek默认值 |
|------|------|------|------------|----------------|
| `model` | string | 模型标识符 | `qwen-vl-plus` | `deepseek-chat` |

**作用**: 指定要调用的具体模型，用于API请求中的model字段。

### 2. 消息配置 (💬 Message Configuration)

#### System Message (系统提示)
- **Qwen默认值**:
```
精准且完整的识别题目内容，严格标准返回json格式。示例{"question_type": "单选题or多选题or判断题","question_text": "完整的问题","options": {"A": "选项内容","B": "选项内容","C": "选项内容","D": "选项内容"}}
```

- **DeepSeek默认值**:
```
你是一个专业的题目解答助手。请根据提供的题目信息，给出详细的解答过程和答案。返回JSON格式：{"analysis": "解题分析", "steps": ["步骤1", "步骤2"], "answer": "最终答案", "explanation": "详细解释"}
```

#### User Message (用户提示)
- **Qwen默认值**: `question_text内的值不应该出现题目类型以及问题序号。`
- **DeepSeek默认值**: `请解答这道题目，提供详细的解题过程。`

**作用**: 定义AI的角色、任务和输出格式，直接影响模型的行为和响应质量。

### 3. 输出格式配置 (📄 Response Format)

| 选项 | 值 | 说明 |
|------|-----|------|
| JSON Object | `json_object` | 强制结构化JSON输出 |
| Text | `text` | 纯文本输出 |

**默认值**: 两个模型都使用 `json_object` 确保结构化输出。

## 🎨 界面优化

### 分组布局
界面现在按功能分为以下几个区域：

1. **🤖 模型配置** - 模型名称设置
2. **💬 消息配置** - System/User Message设置
3. **📄 输出格式** - Response Format选择
4. **🔧 技术参数** - 原有的温度、token等参数

### 用户体验改进
- ✅ **多行文本框**: System/User Message使用textarea，支持长文本编辑
- ✅ **实时提示**: 每个参数都有详细说明
- ✅ **分组标题**: 使用颜色和图标区分不同配置区域
- ✅ **自适应高度**: 文本框支持垂直调整大小

## 📊 完整请求体示例

### Qwen-VL-Plus 请求体
```json
{
  "model": "qwen-vl-plus",
  "input": {
    "messages": [
      {
        "role": "system",
        "content": "精准且完整的识别题目内容，严格标准返回json格式。示例{\"question_type\": \"单选题or多选题or判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\"}}"
      },
      {
        "role": "user",
        "content": [
          {"image": "http://solve.igmdns.com/img/23.jpg"},
          {"text": "question_text内的值不应该出现题目类型以及问题序号。"}
        ]
      }
    ]
  },
  "parameters": {
    "temperature": 0,
    "max_tokens": 1500,
    "top_p": 0.01,
    "top_k": 1,
    "do_sample": false,
    "frequency_penalty": -2,
    "presence_penalty": -2,
    "detail": "high",
    "response_format": {"type": "json_object"}
  }
}
```

### DeepSeek-Chat 请求体
```json
{
  "model": "deepseek-chat",
  "messages": [
    {
      "role": "system",
      "content": "你是一个专业的题目解答助手。请根据提供的题目信息，给出详细的解答过程和答案。返回JSON格式：{\"analysis\": \"解题分析\", \"steps\": [\"步骤1\", \"步骤2\"], \"answer\": \"最终答案\", \"explanation\": \"详细解释\"}"
    },
    {
      "role": "user",
      "content": "请解答这道题目，提供详细的解题过程。\n\n题目信息: {从Qwen识别的结果}"
    }
  ],
  "temperature": 0.3,
  "max_tokens": 2500,
  "top_p": 0.8,
  "response_format": {"type": "json_object"}
}
```

## 🚀 使用场景

### 1. 题目识别优化 (Qwen)
**调整System Message**:
```
针对数学题：强调公式识别和符号准确性
针对语文题：强调文字识别和标点符号
针对英语题：强调单词拼写和语法结构
```

### 2. 解题风格调整 (DeepSeek)
**调整System Message**:
```
详细解析：提供步骤详细的解题过程
简洁回答：直接给出答案和关键步骤
教学模式：包含知识点解释和举一反三
```

### 3. 输出格式控制
**JSON模式**: 适合程序处理，结构化数据
**Text模式**: 适合人类阅读，自然语言输出

## 🎯 配置建议

### 最佳实践
1. **保持System Message简洁明确**: 避免过长的提示影响性能
2. **User Message针对性强**: 根据具体业务需求调整
3. **Response Format统一**: 建议使用JSON确保数据一致性
4. **定期测试效果**: 通过界面实时调整并验证效果

### 性能优化
- 🎯 **精确的System Message**: 减少模型理解歧义
- 📝 **结构化输出**: JSON格式便于后续处理
- ⚡ **合理的参数组合**: 平衡准确性和响应速度

## 🌐 访问方式

- **管理界面**: http://localhost:3000/admin.html
- **默认账号**: 15688515913 / admin888

现在您可以通过Web界面完全控制模型的行为，包括角色定义、任务描述、输出格式等所有关键参数，实现真正的动态配置管理！
