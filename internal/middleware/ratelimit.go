package middleware

import (
	"context"
	"fmt"
	"solve_api/internal/database"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"solve_api/internal/utils"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// RateLimit 基于应用的限流中间件
func RateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过健康检查和管理员接口
		if strings.HasPrefix(c.Request.URL.Path, "/health") ||
			strings.HasPrefix(c.Request.URL.Path, "/api/v1/admin") ||
			strings.HasPrefix(c.Request.URL.Path, "/api/v1/user") {
			c.Next()
			return
		}

		// 如果Redis不可用，跳过限流
		rdb := database.GetRedis()
		if rdb == nil {
			c.Next()
			return
		}

		// 获取应用Key
		appKey := c.GetHeader("X-App-Key")
		if appKey == "" {
			// 从URL参数中获取app_key
			appKey = c.Query("app_key")
		}
		if appKey == "" {
			// 从表单中获取app_key
			appKey = c.PostForm("app_key")
		}

		if appKey == "" {
			utils.BadRequest(c, "缺少应用标识(app_key)")
			c.Abort()
			return
		}

		// 获取应用信息和限流配置
		appRepo := repository.NewApplicationRepository(database.GetDB())
		app, err := appRepo.GetByAppKey(appKey)
		if err != nil {
			utils.BadRequest(c, "无效的应用标识")
			c.Abort()
			return
		}

		// 检查应用状态
		if !app.IsNormal() {
			utils.Forbidden(c, "应用已被冻结")
			c.Abort()
			return
		}

		// 验证限流配置
		if err := app.ValidateRateLimitConfig(); err != nil {
			utils.BadRequest(c, fmt.Sprintf("应用限流配置无效: %v", err))
			c.Abort()
			return
		}

		// 执行限流检查
		allowed, remaining, resetTime, err := checkAppRateLimit(rdb, app)
		if err != nil {
			// Redis错误时不阻止请求，但记录错误
			fmt.Printf("⚠️ 限流检查失败: %v\n", err)
			c.Next()
			return
		}

		if !allowed {
			// 设置限流响应头
			c.Header("X-RateLimit-Limit", strconv.FormatInt(app.GetMaxRequestsPerWindow(), 10))
			c.Header("X-RateLimit-Remaining", "0")
			c.Header("X-RateLimit-Reset", strconv.FormatInt(resetTime, 10))
			c.Header("X-RateLimit-Window", strconv.Itoa(app.RateLimitWindow))

			utils.TooManyRequests(c, fmt.Sprintf("应用请求频率过高，%d秒内最多%d次请求",
				app.RateLimitWindow, app.GetMaxRequestsPerWindow()))
			c.Abort()
			return
		}

		// 设置成功的限流响应头
		c.Header("X-RateLimit-Limit", strconv.FormatInt(app.GetMaxRequestsPerWindow(), 10))
		c.Header("X-RateLimit-Remaining", strconv.FormatInt(remaining, 10))
		c.Header("X-RateLimit-Reset", strconv.FormatInt(resetTime, 10))
		c.Header("X-RateLimit-Window", strconv.Itoa(app.RateLimitWindow))
		c.Header("X-RateLimit-QPS", strconv.Itoa(app.RateLimitQPS))

		// 将应用信息存储到上下文中，供后续处理使用
		c.Set("app", app)
		c.Set("app_key", appKey)

		c.Next()
	}
}

// RateLimitByIP IP限流中间件（用于用户接口）
func RateLimitByIP(maxRequests int, window time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 如果Redis不可用，跳过限流
		rdb := database.GetRedis()
		if rdb == nil {
			c.Next()
			return
		}

		// 获取客户端IP
		clientIP := c.ClientIP()

		// 构造Redis键
		key := fmt.Sprintf("ip_rate_limit:%s", clientIP)
		ctx := context.Background()

		// 使用简单计数器限流
		current, err := rdb.Incr(ctx, key).Result()
		if err != nil {
			// Redis错误时不阻止请求
			c.Next()
			return
		}

		// 第一次请求时设置过期时间
		if current == 1 {
			rdb.Expire(ctx, key, window)
		}

		if current > int64(maxRequests) {
			utils.TooManyRequests(c, fmt.Sprintf("IP请求频率过高，%v内最多%d次请求", window, maxRequests))
			c.Abort()
			return
		}

		// 设置响应头显示限流信息
		c.Header("X-RateLimit-Limit", strconv.Itoa(maxRequests))
		c.Header("X-RateLimit-Remaining", strconv.FormatInt(int64(maxRequests)-current, 10))

		c.Next()
	}
}

// checkAppRateLimit 检查应用级别的限流
func checkAppRateLimit(rdb *redis.Client, app *model.Application) (allowed bool, remaining int64, resetTime int64, err error) {
	ctx := context.Background()
	key := app.GetRateLimitKey()
	now := time.Now().Unix()
	window := int64(app.RateLimitWindow)
	maxRequests := app.GetMaxRequestsPerWindow()

	// 使用Redis的ZSET实现滑动窗口限流
	pipe := rdb.Pipeline()

	// 1. 移除过期的请求记录
	pipe.ZRemRangeByScore(ctx, key, "0", strconv.FormatInt(now-window, 10))

	// 2. 获取当前窗口内的请求数
	pipe.ZCard(ctx, key)

	// 3. 添加当前请求（先检查，后添加）
	pipe.ZAdd(ctx, key, redis.Z{
		Score:  float64(now),
		Member: fmt.Sprintf("%d_%d", now, time.Now().UnixNano()),
	})

	// 4. 设置过期时间
	pipe.Expire(ctx, key, time.Duration(window+10)*time.Second) // 多10秒缓冲

	results, err := pipe.Exec(ctx)
	if err != nil {
		return false, 0, 0, fmt.Errorf("Redis操作失败: %w", err)
	}

	// 获取添加请求前的计数
	currentCount := results[1].(*redis.IntCmd).Val()

	// 计算重置时间（下一个窗口开始时间）
	resetTime = now + window

	// 检查是否超过限制
	if currentCount >= maxRequests {
		// 超过限制，移除刚才添加的请求
		rdb.ZRem(ctx, key, fmt.Sprintf("%d_%d", now, time.Now().UnixNano()))
		return false, 0, resetTime, nil
	}

	// 计算剩余请求数
	remaining = maxRequests - currentCount - 1 // -1是因为当前请求已经被计算

	return true, remaining, resetTime, nil
}

// GetAppRateLimitInfo 获取应用的限流信息
func GetAppRateLimitInfo(appKey string) (current int64, limit int64, remaining int64, resetTime int64, err error) {
	rdb := database.GetRedis()
	if rdb == nil {
		return 0, 0, 0, 0, fmt.Errorf("Redis不可用")
	}

	// 获取应用信息
	appRepo := repository.NewApplicationRepository(database.GetDB())
	app, err := appRepo.GetByAppKey(appKey)
	if err != nil {
		return 0, 0, 0, 0, fmt.Errorf("获取应用信息失败: %w", err)
	}

	ctx := context.Background()
	key := app.GetRateLimitKey()
	now := time.Now().Unix()
	window := int64(app.RateLimitWindow)
	maxRequests := app.GetMaxRequestsPerWindow()

	// 清理过期记录并获取当前计数
	pipe := rdb.Pipeline()
	pipe.ZRemRangeByScore(ctx, key, "0", strconv.FormatInt(now-window, 10))
	pipe.ZCard(ctx, key)

	results, err := pipe.Exec(ctx)
	if err != nil {
		return 0, 0, 0, 0, fmt.Errorf("Redis查询失败: %w", err)
	}

	current = results[1].(*redis.IntCmd).Val()
	limit = maxRequests
	remaining = maxRequests - current
	if remaining < 0 {
		remaining = 0
	}
	resetTime = now + window

	return current, limit, remaining, resetTime, nil
}
