package repository

import (
	"errors"
	"solve_api/internal/model"

	"gorm.io/gorm"
)

type StatsRepository struct {
	db *gorm.DB
}

// NewStatsRepository 创建统计仓库实例
func NewStatsRepository(db *gorm.DB) *StatsRepository {
	return &StatsRepository{db: db}
}

// CreateSystemStats 创建系统统计
func (r *StatsRepository) CreateSystemStats(stats *model.SystemStats) error {
	return r.db.Create(stats).Error
}

// UpdateSystemStats 更新系统统计
func (r *StatsRepository) UpdateSystemStats(stats *model.SystemStats) error {
	return r.db.Save(stats).Error
}

// GetSystemStatsByDate 根据日期获取系统统计
func (r *StatsRepository) GetSystemStatsByDate(date string) (*model.SystemStats, error) {
	var stats model.SystemStats
	err := r.db.Where("date = ?", date).First(&stats).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &stats, nil
}

// GetSystemStatsRange 获取日期范围内的系统统计
func (r *StatsRepository) GetSystemStatsRange(startDate, endDate string) ([]*model.SystemStats, error) {
	var stats []*model.SystemStats
	err := r.db.Where("date >= ? AND date <= ?", startDate, endDate).
		Order("date ASC").
		Find(&stats).Error
	if err != nil {
		return nil, err
	}
	return stats, nil
}

// CreateUserStats 创建用户统计
func (r *StatsRepository) CreateUserStats(stats *model.UserStats) error {
	return r.db.Create(stats).Error
}

// UpdateUserStats 更新用户统计
func (r *StatsRepository) UpdateUserStats(stats *model.UserStats) error {
	return r.db.Save(stats).Error
}

// GetUserStatsByDate 根据用户ID和日期获取用户统计
func (r *StatsRepository) GetUserStatsByDate(userID uint, date string) (*model.UserStats, error) {
	var stats model.UserStats
	err := r.db.Where("user_id = ? AND date = ?", userID, date).First(&stats).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &stats, nil
}

// GetUserStatsRange 获取用户在日期范围内的统计
func (r *StatsRepository) GetUserStatsRange(userID uint, startDate, endDate string) ([]*model.UserStats, error) {
	var stats []*model.UserStats
	err := r.db.Where("user_id = ? AND date >= ? AND date <= ?", userID, startDate, endDate).
		Order("date ASC").
		Find(&stats).Error
	if err != nil {
		return nil, err
	}
	return stats, nil
}

// GetTopUsersByAPICalls 获取API调用次数最多的用户
func (r *StatsRepository) GetTopUsersByAPICalls(date string, limit int) ([]*model.UserStats, error) {
	var stats []*model.UserStats
	err := r.db.Where("date = ?", date).
		Order("api_calls DESC").
		Limit(limit).
		Find(&stats).Error
	if err != nil {
		return nil, err
	}
	return stats, nil
}

// GetTopUsersByCost 获取费用最高的用户
func (r *StatsRepository) GetTopUsersByCost(date string, limit int) ([]*model.UserStats, error) {
	var stats []*model.UserStats
	err := r.db.Where("date = ?", date).
		Order("total_cost DESC").
		Limit(limit).
		Find(&stats).Error
	if err != nil {
		return nil, err
	}
	return stats, nil
}

// GetUserStatsTotal 获取用户总统计
func (r *StatsRepository) GetUserStatsTotal(userID uint) (map[string]interface{}, error) {
	var result struct {
		TotalAPICalls     int64   `json:"total_api_calls"`
		TotalSuccessCalls int64   `json:"total_success_calls"`
		TotalErrorCalls   int64   `json:"total_error_calls"`
		TotalCost         float64 `json:"total_cost"`
		AvgResponseTime   float64 `json:"avg_response_time"`
	}

	err := r.db.Model(&model.UserStats{}).
		Where("user_id = ?", userID).
		Select(`
			COALESCE(SUM(api_calls), 0) as total_api_calls,
			COALESCE(SUM(success_calls), 0) as total_success_calls,
			COALESCE(SUM(error_calls), 0) as total_error_calls,
			COALESCE(SUM(total_cost), 0) as total_cost,
			COALESCE(AVG(avg_response_time), 0) as avg_response_time
		`).
		Scan(&result).Error

	if err != nil {
		return nil, err
	}

	successRate := float64(0)
	if result.TotalAPICalls > 0 {
		successRate = float64(result.TotalSuccessCalls) / float64(result.TotalAPICalls) * 100
	}

	return map[string]interface{}{
		"total_api_calls":     result.TotalAPICalls,
		"total_success_calls": result.TotalSuccessCalls,
		"total_error_calls":   result.TotalErrorCalls,
		"total_cost":          result.TotalCost,
		"avg_response_time":   result.AvgResponseTime,
		"success_rate":        successRate,
	}, nil
}

// GetSystemStatsTotal 获取系统总统计
func (r *StatsRepository) GetSystemStatsTotal() (map[string]interface{}, error) {
	var result struct {
		TotalUsers      int64   `json:"total_users"`
		TotalApps       int64   `json:"total_apps"`
		TotalAPICalls   int64   `json:"total_api_calls"`
		TotalRevenue    float64 `json:"total_revenue"`
		TotalQuestions  int64   `json:"total_questions"`
		AvgCacheHitRate float64 `json:"avg_cache_hit_rate"`
		AvgResponseTime float64 `json:"avg_response_time"`
		AvgErrorRate    float64 `json:"avg_error_rate"`
	}

	err := r.db.Model(&model.SystemStats{}).
		Select(`
			MAX(total_users) as total_users,
			MAX(total_apps) as total_apps,
			COALESCE(SUM(total_api_calls), 0) as total_api_calls,
			COALESCE(SUM(total_revenue), 0) as total_revenue,
			MAX(total_questions) as total_questions,
			COALESCE(AVG(cache_hit_rate), 0) as avg_cache_hit_rate,
			COALESCE(AVG(avg_response_time), 0) as avg_response_time,
			COALESCE(AVG(error_rate), 0) as avg_error_rate
		`).
		Scan(&result).Error

	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total_users":        result.TotalUsers,
		"total_apps":         result.TotalApps,
		"total_api_calls":    result.TotalAPICalls,
		"total_revenue":      result.TotalRevenue,
		"total_questions":    result.TotalQuestions,
		"avg_cache_hit_rate": result.AvgCacheHitRate,
		"avg_response_time":  result.AvgResponseTime,
		"avg_error_rate":     result.AvgErrorRate,
	}, nil
}

// GetRecentSystemStats 获取最近的系统统计
func (r *StatsRepository) GetRecentSystemStats(days int) ([]*model.SystemStats, error) {
	var stats []*model.SystemStats
	err := r.db.Order("date DESC").Limit(days).Find(&stats).Error
	if err != nil {
		return nil, err
	}
	return stats, nil
}

// GetRecentUserStats 获取用户最近的统计
func (r *StatsRepository) GetRecentUserStats(userID uint, days int) ([]*model.UserStats, error) {
	var stats []*model.UserStats
	err := r.db.Where("user_id = ?", userID).
		Order("date DESC").
		Limit(days).
		Find(&stats).Error
	if err != nil {
		return nil, err
	}
	return stats, nil
}

// DeleteOldStats 删除旧统计数据
func (r *StatsRepository) DeleteOldStats(days int) error {
	// 计算截止日期
	cutoffDate := model.GetTodayDateString()
	// 这里需要计算days天前的日期，简化处理

	// 删除旧的系统统计
	if err := r.db.Where("date < ?", cutoffDate).Delete(&model.SystemStats{}).Error; err != nil {
		return err
	}

	// 删除旧的用户统计
	return r.db.Where("date < ?", cutoffDate).Delete(&model.UserStats{}).Error
}
