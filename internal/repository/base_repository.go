package repository

import (
	"errors"
	"fmt"
	"reflect"
	"time"

	"gorm.io/gorm"
)

// BaseRepository 基础Repository，提供通用的CRUD操作
type BaseRepository struct {
	db *gorm.DB
}

// NewBaseRepository 创建基础Repository实例
func NewBaseRepository(db *gorm.DB) *BaseRepository {
	return &BaseRepository{db: db}
}

// Create 通用创建方法
func (r *BaseRepository) Create(entity interface{}) error {
	if entity == nil {
		return errors.New("entity cannot be nil")
	}

	// 设置创建时间（如果实体有CreatedAt字段）
	r.setTimestamp(entity, "CreatedAt", time.Now())

	return r.db.Create(entity).Error
}

// Update 通用更新方法
func (r *BaseRepository) Update(entity interface{}) error {
	if entity == nil {
		return errors.New("entity cannot be nil")
	}

	// 设置更新时间（如果实体有UpdatedAt字段）
	r.setTimestamp(entity, "UpdatedAt", time.Now())

	return r.db.Save(entity).Error
}

// UpdateByID 根据ID更新指定字段
func (r *BaseRepository) UpdateByID(model interface{}, id uint, updates map[string]interface{}) error {
	if model == nil {
		return errors.New("model cannot be nil")
	}

	// 添加更新时间
	updates["updated_at"] = time.Now()

	return r.db.Model(model).Where("id = ?", id).Updates(updates).Error
}

// Delete 通用删除方法（软删除）
func (r *BaseRepository) Delete(model interface{}, id uint) error {
	if model == nil {
		return errors.New("model cannot be nil")
	}

	return r.db.Delete(model, id).Error
}

// HardDelete 硬删除方法
func (r *BaseRepository) HardDelete(model interface{}, id uint) error {
	if model == nil {
		return errors.New("model cannot be nil")
	}

	return r.db.Unscoped().Delete(model, id).Error
}

// GetByID 通用根据ID查询方法
func (r *BaseRepository) GetByID(dest interface{}, id uint) error {
	if dest == nil {
		return errors.New("destination cannot be nil")
	}

	err := r.db.First(dest, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil // 返回nil表示未找到，不是错误
		}
		return err
	}
	return nil
}

// GetByField 根据指定字段查询单条记录
func (r *BaseRepository) GetByField(dest interface{}, field string, value interface{}) error {
	if dest == nil {
		return errors.New("destination cannot be nil")
	}

	err := r.db.Where(fmt.Sprintf("%s = ?", field), value).First(dest).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}
	return nil
}

// List 通用分页查询方法
func (r *BaseRepository) List(dest interface{}, offset, limit int, orderBy ...string) (int64, error) {
	if dest == nil {
		return 0, errors.New("destination cannot be nil")
	}

	// 获取总数
	var total int64
	if err := r.db.Model(dest).Count(&total).Error; err != nil {
		return 0, err
	}

	// 构建查询
	query := r.db.Offset(offset).Limit(limit)

	// 添加排序
	if len(orderBy) > 0 {
		for _, order := range orderBy {
			query = query.Order(order)
		}
	} else {
		query = query.Order("id DESC") // 默认按ID倒序
	}

	// 执行查询
	if err := query.Find(dest).Error; err != nil {
		return 0, err
	}

	return total, nil
}

// ListByField 根据字段条件分页查询
func (r *BaseRepository) ListByField(dest interface{}, field string, value interface{}, offset, limit int, orderBy ...string) (int64, error) {
	if dest == nil {
		return 0, errors.New("destination cannot be nil")
	}

	// 构建基础查询条件
	baseQuery := r.db.Where(fmt.Sprintf("%s = ?", field), value)

	// 获取总数
	var total int64
	if err := baseQuery.Model(dest).Count(&total).Error; err != nil {
		return 0, err
	}

	// 构建分页查询
	query := baseQuery.Offset(offset).Limit(limit)

	// 添加排序
	if len(orderBy) > 0 {
		for _, order := range orderBy {
			query = query.Order(order)
		}
	} else {
		query = query.Order("id DESC")
	}

	// 执行查询
	if err := query.Find(dest).Error; err != nil {
		return 0, err
	}

	return total, nil
}

// Search 通用搜索方法（支持多字段模糊查询）
func (r *BaseRepository) Search(dest interface{}, keyword string, fields []string, offset, limit int, orderBy ...string) (int64, error) {
	if dest == nil {
		return 0, errors.New("destination cannot be nil")
	}

	if keyword == "" || len(fields) == 0 {
		return r.List(dest, offset, limit, orderBy...)
	}

	// 构建搜索条件
	query := r.db
	for i, field := range fields {
		if i == 0 {
			query = query.Where(fmt.Sprintf("%s LIKE ?", field), "%"+keyword+"%")
		} else {
			query = query.Or(fmt.Sprintf("%s LIKE ?", field), "%"+keyword+"%")
		}
	}

	// 获取总数
	var total int64
	if err := query.Model(dest).Count(&total).Error; err != nil {
		return 0, err
	}

	// 构建分页查询
	query = query.Offset(offset).Limit(limit)

	// 添加排序
	if len(orderBy) > 0 {
		for _, order := range orderBy {
			query = query.Order(order)
		}
	} else {
		query = query.Order("id DESC")
	}

	// 执行查询
	if err := query.Find(dest).Error; err != nil {
		return 0, err
	}

	return total, nil
}

// BatchCreate 批量创建
func (r *BaseRepository) BatchCreate(entities interface{}) error {
	if entities == nil {
		return errors.New("entities cannot be nil")
	}

	// 使用反射设置批量创建时间
	r.setBatchTimestamp(entities, "CreatedAt", time.Now())

	return r.db.CreateInBatches(entities, 100).Error // 每批100条
}

// BatchUpdate 批量更新
func (r *BaseRepository) BatchUpdate(model interface{}, updates map[string]interface{}, whereConditions map[string]interface{}) error {
	if model == nil {
		return errors.New("model cannot be nil")
	}

	// 添加更新时间
	updates["updated_at"] = time.Now()

	query := r.db.Model(model)

	// 添加where条件
	for field, value := range whereConditions {
		query = query.Where(fmt.Sprintf("%s = ?", field), value)
	}

	return query.Updates(updates).Error
}

// Exists 检查记录是否存在
func (r *BaseRepository) Exists(model interface{}, field string, value interface{}) (bool, error) {
	if model == nil {
		return false, errors.New("model cannot be nil")
	}

	var count int64
	err := r.db.Model(model).Where(fmt.Sprintf("%s = ?", field), value).Count(&count).Error
	return count > 0, err
}

// Count 统计记录数量
func (r *BaseRepository) Count(model interface{}, whereConditions map[string]interface{}) (int64, error) {
	if model == nil {
		return 0, errors.New("model cannot be nil")
	}

	query := r.db.Model(model)

	// 添加where条件
	for field, value := range whereConditions {
		query = query.Where(fmt.Sprintf("%s = ?", field), value)
	}

	var count int64
	err := query.Count(&count).Error
	return count, err
}

// Transaction 执行事务
func (r *BaseRepository) Transaction(fn func(*gorm.DB) error) error {
	return r.db.Transaction(fn)
}

// GetDB 获取数据库连接（用于复杂查询）
func (r *BaseRepository) GetDB() *gorm.DB {
	return r.db
}

// setTimestamp 设置时间戳字段
func (r *BaseRepository) setTimestamp(entity interface{}, fieldName string, value time.Time) {
	v := reflect.ValueOf(entity)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() != reflect.Struct {
		return
	}

	field := v.FieldByName(fieldName)
	if field.IsValid() && field.CanSet() && field.Type() == reflect.TypeOf(time.Time{}) {
		field.Set(reflect.ValueOf(value))
	}
}

// setBatchTimestamp 批量设置时间戳字段
func (r *BaseRepository) setBatchTimestamp(entities interface{}, fieldName string, value time.Time) {
	v := reflect.ValueOf(entities)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() != reflect.Slice {
		return
	}

	for i := 0; i < v.Len(); i++ {
		entity := v.Index(i)
		if entity.Kind() == reflect.Ptr {
			entity = entity.Elem()
		}

		if entity.Kind() == reflect.Struct {
			field := entity.FieldByName(fieldName)
			if field.IsValid() && field.CanSet() && field.Type() == reflect.TypeOf(time.Time{}) {
				field.Set(reflect.ValueOf(value))
			}
		}
	}
}
