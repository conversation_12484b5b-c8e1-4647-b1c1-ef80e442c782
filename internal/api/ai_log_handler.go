package api

import (
	"net/http"
	"solve_api/internal/service"
	"strconv"

	"github.com/gin-gonic/gin"
)

type AILogHandler struct {
	aiLogService *service.AILogService
}

// NewAILogHandler 创建AI日志处理器实例
func NewAILogHandler(aiLogService *service.AILogService) *AILogHandler {
	return &AILogHandler{
		aiLogService: aiLogService,
	}
}

// GetLogs 获取AI模型日志列表
func (h *AILogHandler) GetLogs(c *gin.Context) {
	// 获取查询参数
	limitStr := c.Default<PERSON>("limit", "50")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 50
	}
	if limit > 200 {
		limit = 200 // 最大限制200条
	}

	// 获取日志
	logs := h.aiLogService.GetRecentLogs(limit)

	c.J<PERSON>(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"logs":  logs,
			"total": h.aiLogService.GetLogCount(),
		},
	})
}

// GetLogByID 根据ID获取特定日志
func (h *AILogHandler) GetLogByID(c *gin.Context) {
	logID := c.Param("id")
	if logID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "日志ID不能为空",
		})
		return
	}

	log := h.aiLogService.GetLogByID(logID)
	if log == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "日志不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    log,
	})
}

// ClearLogs 清空所有日志
func (h *AILogHandler) ClearLogs(c *gin.Context) {
	h.aiLogService.ClearLogs()

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "日志已清空",
	})
}

// GetLogStats 获取日志统计信息
func (h *AILogHandler) GetLogStats(c *gin.Context) {
	stats := h.aiLogService.GetLogStats()

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data":    stats,
	})
}

// CleanOldLogs 清理旧日志
func (h *AILogHandler) CleanOldLogs(c *gin.Context) {
	keepCountStr := c.DefaultQuery("keep", "100")
	keepCount, err := strconv.Atoi(keepCountStr)
	if err != nil || keepCount <= 0 {
		keepCount = 100
	}

	removedCount := h.aiLogService.CleanOldLogs(keepCount)

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "清理完成",
		"data": gin.H{
			"removed_count": removedCount,
			"kept_count":    keepCount,
		},
	})
}

// ExportLogs 导出日志数据
func (h *AILogHandler) ExportLogs(c *gin.Context) {
	logs := h.aiLogService.GetLogs()

	// 设置响应头
	c.Header("Content-Type", "application/json")
	c.Header("Content-Disposition", "attachment; filename=ai-logs.json")

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "导出成功",
		"data": gin.H{
			"logs":        logs,
			"total":       len(logs),
			"export_time": "2024-01-01 00:00:00", // 实际应该使用当前时间
		},
	})
}
