package api

import (
	"solve_api/internal/model"
	"solve_api/internal/service"
	"solve_api/internal/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

type ModelConfigHandler struct {
	modelConfigService *service.ModelConfigService
}

// NewModelConfigHandler 创建模型配置处理器实例
func NewModelConfigHandler(modelConfigService *service.ModelConfigService) *ModelConfigHandler {
	return &ModelConfigHandler{
		modelConfigService: modelConfigService,
	}
}

// Create 创建模型配置
// @Summary 创建模型配置
// @Description 创建新的AI模型配置
// @Tags 模型配置管理
// @Accept json
// @Produce json
// @Param request body model.ModelConfigCreateRequest true "创建模型配置请求参数"
// @Success 200 {object} utils.Response{data=model.ModelConfigResponse} "创建成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 409 {object} utils.Response "模型名称已存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/model [post]
func (h *ModelConfigHandler) Create(c *gin.Context) {
	var req model.ModelConfigCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	modelConfig, err := h.modelConfigService.Create(&req)
	if err != nil {
		if err.Error() == "模型名称已存在" {
			utils.Conflict(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "模型配置创建成功", modelConfig)
}

// 注意：GetByID方法已移除，请使用GetByName方法
// 基于模型名称的唯一性，使用name作为主键更加合理

// 注意：基于ID的Update方法已移除
// 请使用基于名称的UpdateParams和UpdateApiKey方法

// 注意：删除模型配置功能已移除
// 模型配置不支持删除操作，以保持系统稳定性和兼容性

// GetList 获取模型配置列表
// @Summary 获取模型配置列表
// @Description 分页获取模型配置列表
// @Tags 模型配置管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} utils.Response{data=[]model.ModelConfigListResponse} "获取成功"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/model [get]
func (h *ModelConfigHandler) GetList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	modelConfigs, total, err := h.modelConfigService.GetList(page, pageSize)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	result := gin.H{
		"list":      modelConfigs,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	}

	utils.SuccessWithMessage(c, "获取模型配置列表成功", result)
}

// GetEnabled 获取启用的模型配置列表
// @Summary 获取启用的模型配置列表
// @Description 获取所有启用状态的模型配置
// @Tags 模型配置管理
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response{data=[]model.ModelConfigListResponse} "获取成功"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/model/enabled [get]
func (h *ModelConfigHandler) GetEnabled(c *gin.Context) {
	modelConfigs, err := h.modelConfigService.GetEnabled()
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取启用的模型配置成功", modelConfigs)
}

// GetByName 根据名称获取模型配置
// @Summary 根据名称获取模型配置
// @Description 根据模型名称获取配置详情
// @Tags 模型配置管理
// @Accept json
// @Produce json
// @Param name path string true "模型名称" Enums(qwen-vl-plus,deepseek-chat)
// @Success 200 {object} utils.Response{data=model.ModelConfigResponse} "获取成功"
// @Failure 404 {object} utils.Response "模型配置不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/model/name/{name} [get]
func (h *ModelConfigHandler) GetByName(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		utils.BadRequest(c, "模型名称不能为空")
		return
	}

	// 验证模型名称是否为允许的值
	if !isValidModelName(name) {
		utils.BadRequest(c, "无效的模型名称，只支持: qwen-vl-plus, deepseek-chat")
		return
	}

	modelConfig, err := h.modelConfigService.GetByName(name)
	if err != nil {
		if err.Error() == "模型配置不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取模型配置成功", modelConfig)
}

// UpdateParams 更新模型参数
// @Summary 更新模型参数
// @Description 根据模型名称更新参数配置
// @Tags 模型配置管理
// @Accept json
// @Produce json
// @Param name path string true "模型名称" Enums(qwen-vl-plus,deepseek-chat)
// @Param request body model.ModelConfigParamsUpdateRequest true "参数更新请求"
// @Success 200 {object} utils.Response{data=model.ModelConfigResponse} "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "模型配置不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/model/name/{name}/params [put]
func (h *ModelConfigHandler) UpdateParams(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		utils.BadRequest(c, "模型名称不能为空")
		return
	}

	// 验证模型名称是否为允许的值
	if !isValidModelName(name) {
		utils.BadRequest(c, "无效的模型名称，只支持: qwen-vl-plus, deepseek-chat")
		return
	}

	var req model.ModelConfigParamsUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	modelConfig, err := h.modelConfigService.UpdateParamsByName(name, &req)
	if err != nil {
		if err.Error() == "模型配置不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "模型参数更新成功", modelConfig)
}

// UpdateApiKey 更新模型API密钥
// @Summary 更新模型API密钥
// @Description 根据模型名称更新API密钥
// @Tags 模型配置管理
// @Accept json
// @Produce json
// @Param name path string true "模型名称" Enums(qwen-vl-plus,deepseek-chat)
// @Param request body model.ModelConfigApiKeyUpdateRequest true "API密钥更新请求"
// @Success 200 {object} utils.Response{data=model.ModelConfigResponse} "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "模型配置不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/model/name/{name}/apikey [put]
func (h *ModelConfigHandler) UpdateApiKey(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		utils.BadRequest(c, "模型名称不能为空")
		return
	}

	// 验证模型名称是否为允许的值
	if !isValidModelName(name) {
		utils.BadRequest(c, "无效的模型名称，只支持: qwen-vl-plus, deepseek-chat")
		return
	}

	var req model.ModelConfigApiKeyUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	modelConfig, err := h.modelConfigService.UpdateApiKeyByName(name, &req)
	if err != nil {
		if err.Error() == "模型配置不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "模型API密钥更新成功", modelConfig)
}

// GetFixedModels 获取固定的两个模型配置
// @Summary 获取固定的两个模型配置
// @Description 获取Qwen和DeepSeek两个固定模型的配置
// @Tags 模型配置管理
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response{data=model.FixedModelsResponse} "获取成功"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/model/fixed [get]
func (h *ModelConfigHandler) GetFixedModels(c *gin.Context) {
	models, err := h.modelConfigService.GetFixedModels()
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取固定模型配置成功", models)
}

// isValidModelName 验证模型名称是否有效
func isValidModelName(name string) bool {
	validNames := []string{
		model.ModelNameQwenVLPlus,
		model.ModelNameDeepseekChat,
	}

	for _, validName := range validNames {
		if name == validName {
			return true
		}
	}

	return false
}

// UpdateStatusByName 根据名称更新模型状态
// @Summary 根据名称更新模型状态
// @Description 根据模型名称启用或禁用模型配置
// @Tags 模型配置管理
// @Accept json
// @Produce json
// @Param name path string true "模型名称" Enums(qwen-vl-plus,deepseek-chat)
// @Param request body model.ModelConfigStatusUpdateRequest true "更新状态请求参数"
// @Success 200 {object} utils.Response{data=model.ModelConfigResponse} "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "模型配置不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/model/name/{name}/status [put]
func (h *ModelConfigHandler) UpdateStatusByName(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		utils.BadRequest(c, "模型名称不能为空")
		return
	}

	// 验证模型名称是否为允许的值
	if !isValidModelName(name) {
		utils.BadRequest(c, "无效的模型名称，只支持: qwen-vl-plus, deepseek-chat")
		return
	}

	var req model.ModelConfigStatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	modelConfig, err := h.modelConfigService.UpdateStatusByName(name, &req)
	if err != nil {
		if err.Error() == "模型配置不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "模型状态更新成功", modelConfig)
}
