package api

import (
	"solve_api/internal/model"
	"solve_api/internal/service"
	"solve_api/internal/utils"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type StatsHandler struct {
	statsService  *service.StatsService
	apiLogService *service.APILogService
}

// NewStatsHandler 创建统计处理器实例
func NewStatsHandler(statsService *service.StatsService, apiLogService *service.APILogService) *StatsHandler {
	return &StatsHandler{
		statsService:  statsService,
		apiLogService: apiLogService,
	}
}

// GetSystemStats 获取系统统计
func (h *StatsHandler) GetSystemStats(c *gin.Context) {
	date := c.DefaultQuery("date", model.GetTodayDateString())

	stats, err := h.statsService.GenerateSystemStats(date)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取系统统计成功", stats)
}

// GetSystemStatsRange 获取系统统计范围
func (h *StatsHandler) GetSystemStatsRange(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	if startDate == "" || endDate == "" {
		utils.BadRequest(c, "开始日期和结束日期不能为空")
		return
	}

	stats, err := h.statsService.GetSystemStatsRange(startDate, endDate)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取系统统计范围成功", stats)
}

// GetSystemStatsTotal 获取系统总统计
func (h *StatsHandler) GetSystemStatsTotal(c *gin.Context) {
	stats, err := h.statsService.GetSystemStatsTotal()
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取系统总统计成功", stats)
}

// GetUserStats 获取用户统计
func (h *StatsHandler) GetUserStats(c *gin.Context) {
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	date := c.DefaultQuery("date", model.GetTodayDateString())

	stats, err := h.statsService.GenerateUserStats(uint(userID), date)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取用户统计成功", stats)
}

// GetUserStatsRange 获取用户统计范围
func (h *StatsHandler) GetUserStatsRange(c *gin.Context) {
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	if startDate == "" || endDate == "" {
		utils.BadRequest(c, "开始日期和结束日期不能为空")
		return
	}

	stats, err := h.statsService.GetUserStatsRange(uint(userID), startDate, endDate)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取用户统计范围成功", stats)
}

// GetUserStatsTotal 获取用户总统计
func (h *StatsHandler) GetUserStatsTotal(c *gin.Context) {
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	stats, err := h.statsService.GetUserStatsTotal(uint(userID))
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取用户总统计成功", stats)
}

// GetTopUsers 获取排行榜用户
func (h *StatsHandler) GetTopUsers(c *gin.Context) {
	date := c.DefaultQuery("date", model.GetTodayDateString())
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	stats, err := h.statsService.GetTopUsers(date, limit)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取排行榜成功", stats)
}

// GetRecentStats 获取最近统计
func (h *StatsHandler) GetRecentStats(c *gin.Context) {
	days, _ := strconv.Atoi(c.DefaultQuery("days", "7"))

	stats, err := h.statsService.GetRecentStats(days)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取最近统计成功", stats)
}

// GetAPILogs 获取API调用日志
func (h *StatsHandler) GetAPILogs(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	logs, total, err := h.apiLogService.GetAPILogs(page, pageSize)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	result := gin.H{
		"list":      logs,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	}

	utils.SuccessWithMessage(c, "获取API日志成功", result)
}

// GetUserAPILogs 获取用户API调用日志
func (h *StatsHandler) GetUserAPILogs(c *gin.Context) {
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	logs, total, err := h.apiLogService.GetUserAPILogs(uint(userID), page, pageSize)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	result := gin.H{
		"list":      logs,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	}

	utils.SuccessWithMessage(c, "获取用户API日志成功", result)
}

// GetAPILogsByDateRange 根据日期范围获取API日志
func (h *StatsHandler) GetAPILogsByDateRange(c *gin.Context) {
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	if startDateStr == "" || endDateStr == "" {
		utils.BadRequest(c, "开始日期和结束日期不能为空")
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		utils.BadRequest(c, "开始日期格式错误")
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		utils.BadRequest(c, "结束日期格式错误")
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	logs, total, err := h.apiLogService.GetAPILogsByDateRange(startDate, endDate, page, pageSize)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	result := gin.H{
		"list":       logs,
		"total":      total,
		"page":       page,
		"page_size":  pageSize,
		"start_date": startDateStr,
		"end_date":   endDateStr,
	}

	utils.SuccessWithMessage(c, "获取日期范围API日志成功", result)
}

// GenerateDailyStats 生成每日统计
func (h *StatsHandler) GenerateDailyStats(c *gin.Context) {
	err := h.statsService.GenerateDailyStats()
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "生成每日统计成功", nil)
}
