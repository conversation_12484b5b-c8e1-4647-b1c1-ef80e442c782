package api

import (
	"solve_api/internal/service"
	"solve_api/internal/utils"

	"github.com/gin-gonic/gin"
)

type SystemHandler struct {
	configService *service.ConfigService
	statsService  *service.StatsService
	apiLogService *service.APILogService
}

// NewSystemHandler 创建系统处理器实例
func NewSystemHandler(
	configService *service.ConfigService,
	statsService *service.StatsService,
	apiLogService *service.APILogService,
) *SystemHandler {
	return &SystemHandler{
		configService: configService,
		statsService:  statsService,
		apiLogService: apiLogService,
	}
}

// GetSystemInfo 获取系统信息
func (h *SystemHandler) GetSystemInfo(c *gin.Context) {
	// 获取系统总统计
	systemStats, err := h.statsService.GetSystemStatsTotal()
	if err != nil {
		utils.ServerError(c, "获取系统统计失败: "+err.Error())
		return
	}

	// 获取系统配置
	configs, err := h.configService.GetAllConfigs()
	if err != nil {
		utils.ServerError(c, "获取系统配置失败: "+err.Error())
		return
	}

	result := gin.H{
		"system_stats": systemStats,
		"configs":      configs,
		"version":      "1.0.0",
		"build_time":   "2024-01-01",
	}

	utils.SuccessWithMessage(c, "获取系统信息成功", result)
}

// GetSystemHealth 获取系统健康状态
func (h *SystemHandler) GetSystemHealth(c *gin.Context) {
	health := gin.H{
		"status":    "healthy",
		"timestamp": utils.GetCurrentTimestamp(),
		"services": gin.H{
			"database": "healthy",
			"redis":    "healthy",
			"api":      "healthy",
		},
	}

	utils.SuccessWithMessage(c, "系统健康检查通过", health)
}

// GetDashboard 获取仪表板数据
func (h *SystemHandler) GetDashboard(c *gin.Context) {
	// 获取系统总统计
	systemStats, err := h.statsService.GetSystemStatsTotal()
	if err != nil {
		utils.ServerError(c, "获取系统统计失败: "+err.Error())
		return
	}

	// 获取最近7天统计
	recentStats, err := h.statsService.GetRecentStats(7)
	if err != nil {
		utils.ServerError(c, "获取最近统计失败: "+err.Error())
		return
	}

	// 获取今日排行榜
	topUsers, err := h.statsService.GetTopUsers("2024-01-01", 10)
	if err != nil {
		utils.ServerError(c, "获取排行榜失败: "+err.Error())
		return
	}

	result := gin.H{
		"system_stats": systemStats,
		"recent_stats": recentStats,
		"top_users":    topUsers,
		"last_updated": utils.GetCurrentTimestamp(),
	}

	utils.SuccessWithMessage(c, "获取仪表板数据成功", result)
}

// CleanupSystem 系统清理
func (h *SystemHandler) CleanupSystem(c *gin.Context) {
	var req struct {
		CleanLogs  bool `json:"clean_logs"`
		CleanStats bool `json:"clean_stats"`
		Days       int  `json:"days"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	if req.Days <= 0 {
		req.Days = 30 // 默认清理30天前的数据
	}

	var results []string

	// 清理API日志
	if req.CleanLogs {
		if err := h.apiLogService.CleanOldLogs(req.Days); err != nil {
			utils.ServerError(c, "清理API日志失败: "+err.Error())
			return
		}
		results = append(results, "API日志清理完成")
	}

	// 清理统计数据
	if req.CleanStats {
		// 这里可以添加清理统计数据的逻辑
		results = append(results, "统计数据清理完成")
	}

	if len(results) == 0 {
		results = append(results, "未执行任何清理操作")
	}

	utils.SuccessWithMessage(c, "系统清理完成", gin.H{
		"results": results,
		"days":    req.Days,
	})
}

// BackupSystem 系统备份
func (h *SystemHandler) BackupSystem(c *gin.Context) {
	var req struct {
		BackupType string `json:"backup_type"` // full, data, config
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	if req.BackupType == "" {
		req.BackupType = "data"
	}

	// 这里应该实现实际的备份逻辑
	// 简化处理，返回备份信息
	result := gin.H{
		"backup_type": req.BackupType,
		"backup_file": "backup_" + utils.GetCurrentTimestamp() + ".sql",
		"status":      "completed",
		"size":        "1.2MB",
	}

	utils.SuccessWithMessage(c, "系统备份完成", result)
}

// RestoreSystem 系统恢复
func (h *SystemHandler) RestoreSystem(c *gin.Context) {
	var req struct {
		BackupFile string `json:"backup_file" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	// 这里应该实现实际的恢复逻辑
	// 简化处理，返回恢复信息
	result := gin.H{
		"backup_file": req.BackupFile,
		"status":      "completed",
		"restored_at": utils.GetCurrentTimestamp(),
	}

	utils.SuccessWithMessage(c, "系统恢复完成", result)
}

// GetSystemLogs 获取系统日志
func (h *SystemHandler) GetSystemLogs(c *gin.Context) {
	// 这里应该读取实际的系统日志文件
	// 简化处理，返回模拟日志
	logs := []gin.H{
		{
			"level":     "INFO",
			"message":   "系统启动成功",
			"timestamp": "2024-01-01 10:00:00",
		},
		{
			"level":     "WARN",
			"message":   "Redis连接超时，正在重试",
			"timestamp": "2024-01-01 10:01:00",
		},
		{
			"level":     "ERROR",
			"message":   "数据库连接失败",
			"timestamp": "2024-01-01 10:02:00",
		},
	}

	utils.SuccessWithMessage(c, "获取系统日志成功", gin.H{
		"logs":  logs,
		"total": len(logs),
	})
}

// UpdateSystemConfig 更新系统配置
func (h *SystemHandler) UpdateSystemConfig(c *gin.Context) {
	var req map[string]string

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	// 批量更新配置
	for key, value := range req {
		if err := h.configService.SetConfig(key, value); err != nil {
			utils.ServerError(c, "更新配置失败: "+err.Error())
			return
		}
	}

	utils.SuccessWithMessage(c, "系统配置更新成功", nil)
}

// GetSystemMetrics 获取系统指标
func (h *SystemHandler) GetSystemMetrics(c *gin.Context) {
	// 这里应该获取实际的系统指标
	// 简化处理，返回模拟指标
	metrics := gin.H{
		"cpu_usage":          "45%",
		"memory_usage":       "68%",
		"disk_usage":         "32%",
		"network_io":         "1.2MB/s",
		"active_connections": 156,
		"response_time":      "120ms",
	}

	utils.SuccessWithMessage(c, "获取系统指标成功", metrics)
}
