package api

import (
	"solve_api/internal/model"
	"solve_api/internal/service"
	"solve_api/internal/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

type ApplicationHandler struct {
	appService *service.ApplicationService
}

// NewApplicationHandler 创建应用处理器实例
func NewApplicationHandler(appService *service.ApplicationService) *ApplicationHandler {
	return &ApplicationHandler{
		appService: appService,
	}
}

// Create 创建应用
// @Summary 创建应用
// @Description 用户创建新的应用，自动生成app_key和secret_key
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param user_id path uint true "用户ID"
// @Param request body model.ApplicationCreateRequest true "创建应用请求参数"
// @Success 200 {object} utils.Response{data=model.ApplicationResponse} "创建成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "账户已被冻结"
// @Failure 409 {object} utils.Response "应用数量超限"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/app [post]
func (h *ApplicationHandler) Create(c *gin.Context) {
	// 1. 获取用户ID
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	// 2. 解析请求参数
	var req model.ApplicationCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	// 3. 创建应用
	app, err := h.appService.Create(uint(userID), &req)
	if err != nil {
		if err.Error() == "账户已被冻结，无法创建应用" {
			utils.Forbidden(c, err.Error())
			return
		}
		if err.Error() == "每个用户最多只能创建5个应用" {
			utils.Conflict(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "应用创建成功", app)
}

// GetList 获取应用列表
// @Summary 获取应用列表
// @Description 获取用户的所有应用列表
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param user_id path uint true "用户ID"
// @Success 200 {object} utils.Response{data=[]model.ApplicationListResponse} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "用户不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/app [get]
func (h *ApplicationHandler) GetList(c *gin.Context) {
	// 1. 获取用户ID
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	// 2. 获取应用列表
	apps, err := h.appService.GetList(uint(userID))
	if err != nil {
		if err.Error() == "用户不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取应用列表成功", apps)
}

// GetByID 获取应用详情
// @Summary 获取应用详情
// @Description 获取指定应用的详细信息，包含secret_key
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param user_id path uint true "用户ID"
// @Param app_id path uint true "应用ID"
// @Success 200 {object} utils.Response{data=model.ApplicationResponse} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "应用不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/app/{app_id} [get]
func (h *ApplicationHandler) GetByID(c *gin.Context) {
	// 1. 获取用户ID
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	// 2. 获取应用ID
	appIDStr := c.Param("app_id")
	if appIDStr == "" {
		utils.BadRequest(c, "应用ID不能为空")
		return
	}

	appID, err := strconv.ParseUint(appIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "应用ID格式错误")
		return
	}

	// 3. 获取应用详情
	app, err := h.appService.GetByID(uint(userID), uint(appID))
	if err != nil {
		if err.Error() == "用户不存在" || err.Error() == "应用不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取应用详情成功", app)
}

// Update 更新应用信息
// @Summary 更新应用信息
// @Description 更新应用名称（不能修改类型）
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param user_id path uint true "用户ID"
// @Param app_id path uint true "应用ID"
// @Param request body model.ApplicationUpdateRequest true "更新应用请求参数"
// @Success 200 {object} utils.Response{data=model.ApplicationResponse} "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "账户已被冻结"
// @Failure 404 {object} utils.Response "应用不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/app/{app_id} [put]
func (h *ApplicationHandler) Update(c *gin.Context) {
	// 1. 获取用户ID
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	// 2. 获取应用ID
	appIDStr := c.Param("app_id")
	if appIDStr == "" {
		utils.BadRequest(c, "应用ID不能为空")
		return
	}

	appID, err := strconv.ParseUint(appIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "应用ID格式错误")
		return
	}

	// 3. 解析请求参数
	var req model.ApplicationUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	// 4. 更新应用
	app, err := h.appService.Update(uint(userID), uint(appID), &req)
	if err != nil {
		if err.Error() == "账户已被冻结，无法修改应用" {
			utils.Forbidden(c, err.Error())
			return
		}
		if err.Error() == "用户不存在" || err.Error() == "应用不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "应用更新成功", app)
}

// ResetSecretKey 重置SecretKey
// @Summary 重置SecretKey
// @Description 重置应用的SecretKey，生成新的密钥
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param user_id path uint true "用户ID"
// @Param app_id path uint true "应用ID"
// @Success 200 {object} utils.Response{data=model.ApplicationResponse} "重置成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "账户已被冻结"
// @Failure 404 {object} utils.Response "应用不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/app/{app_id}/reset-secret [put]
func (h *ApplicationHandler) ResetSecretKey(c *gin.Context) {
	// 1. 获取用户ID
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	// 2. 获取应用ID
	appIDStr := c.Param("app_id")
	if appIDStr == "" {
		utils.BadRequest(c, "应用ID不能为空")
		return
	}

	appID, err := strconv.ParseUint(appIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "应用ID格式错误")
		return
	}

	// 3. 重置SecretKey
	app, err := h.appService.ResetSecretKey(uint(userID), uint(appID))
	if err != nil {
		if err.Error() == "账户已被冻结，无法重置密钥" {
			utils.Forbidden(c, err.Error())
			return
		}
		if err.Error() == "用户不存在" || err.Error() == "应用不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "SecretKey重置成功", app)
}

// UpdateStatus 更新应用状态
// @Summary 更新应用状态
// @Description 更新应用状态（正常/冻结）
// @Tags 应用管理
// @Accept json
// @Produce json
// @Param user_id path uint true "用户ID"
// @Param app_id path uint true "应用ID"
// @Param request body model.ApplicationStatusUpdateRequest true "更新状态请求参数"
// @Success 200 {object} utils.Response{data=model.ApplicationResponse} "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 403 {object} utils.Response "账户已被冻结"
// @Failure 404 {object} utils.Response "应用不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/app/{app_id}/status [put]
func (h *ApplicationHandler) UpdateStatus(c *gin.Context) {
	// 1. 获取用户ID
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	// 2. 获取应用ID
	appIDStr := c.Param("app_id")
	if appIDStr == "" {
		utils.BadRequest(c, "应用ID不能为空")
		return
	}

	appID, err := strconv.ParseUint(appIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "应用ID格式错误")
		return
	}

	// 3. 解析请求参数
	var req model.ApplicationStatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	// 4. 更新应用状态
	app, err := h.appService.UpdateStatus(uint(userID), uint(appID), &req)
	if err != nil {
		if err.Error() == "账户已被冻结，无法修改应用状态" {
			utils.Forbidden(c, err.Error())
			return
		}
		if err.Error() == "用户不存在" || err.Error() == "应用不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "应用状态更新成功", app)
}
