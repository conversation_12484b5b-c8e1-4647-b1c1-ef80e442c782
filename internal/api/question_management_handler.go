package api

import (
	"solve_api/internal/model"
	"solve_api/internal/service"
	"solve_api/internal/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

type QuestionManagementHandler struct {
	questionService *service.QuestionService
}

// NewQuestionManagementHandler 创建题库管理处理器实例
func NewQuestionManagementHandler(questionService *service.QuestionService) *QuestionManagementHandler {
	return &QuestionManagementHandler{
		questionService: questionService,
	}
}

// CreateQuestion 新增题目
// @Summary 新增题目
// @Description 管理员新增题目到题库
// @Tags 题库管理
// @Accept json
// @Produce json
// @Param request body model.QuestionManagementRequest true "题目信息"
// @Success 200 {object} utils.Response{data=map[string]interface{}} "创建成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/question-management [post]
func (h *QuestionManagementHandler) CreateQuestion(c *gin.Context) {
	var req model.QuestionManagementRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	question, err := h.questionService.CreateManagementQuestion(&req)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "题目创建成功", question.ToManagementResponse())
}

// UpdateQuestion 更新题目
// @Summary 更新题目
// @Description 管理员更新题目信息
// @Tags 题库管理
// @Accept json
// @Produce json
// @Param id path int true "题目ID"
// @Param request body model.QuestionUpdateRequest true "更新信息"
// @Success 200 {object} utils.Response{data=map[string]interface{}} "更新成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "题目不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/question-management/{id} [put]
func (h *QuestionManagementHandler) UpdateQuestion(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "题目ID格式错误")
		return
	}

	var req model.QuestionUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	question, err := h.questionService.UpdateManagementQuestion(uint(id), &req)
	if err != nil {
		if err.Error() == "题目不存在" {
			utils.NotFound(c, "题目不存在")
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "题目更新成功", question.ToManagementResponse())
}

// GetQuestion 获取题目详情
// @Summary 获取题目详情
// @Description 根据ID获取题目详细信息
// @Tags 题库管理
// @Accept json
// @Produce json
// @Param id path int true "题目ID"
// @Success 200 {object} utils.Response{data=map[string]interface{}} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "题目不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/question-management/{id} [get]
func (h *QuestionManagementHandler) GetQuestion(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "题目ID格式错误")
		return
	}

	question, err := h.questionService.GetQuestionByID(uint(id))
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	if question == nil {
		utils.NotFound(c, "题目不存在")
		return
	}

	utils.Success(c, question.ToManagementResponse())
}

// GetQuestionList 获取题目列表
// @Summary 获取题目列表
// @Description 分页获取题目列表
// @Tags 题库管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Param question_type query string false "题目类型"
// @Param keyword query string false "关键词搜索"
// @Success 200 {object} utils.Response{data=map[string]interface{}} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/question-management [get]
func (h *QuestionManagementHandler) GetQuestionList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	questionType := c.Query("question_type")
	keyword := c.Query("keyword")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	questions, total, err := h.questionService.GetManagementQuestionList(page, limit, questionType, keyword)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	// 转换为管理响应格式
	var questionList []map[string]interface{}
	for _, question := range questions {
		questionList = append(questionList, question.ToManagementResponse())
	}

	utils.Success(c, map[string]interface{}{
		"questions": questionList,
		"total":     total,
		"page":      page,
		"limit":     limit,
	})
}

// DeleteQuestion 删除题目
// @Summary 删除题目
// @Description 根据ID删除题目
// @Tags 题库管理
// @Accept json
// @Produce json
// @Param id path int true "题目ID"
// @Success 200 {object} utils.Response "删除成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "题目不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/question-management/{id} [delete]
func (h *QuestionManagementHandler) DeleteQuestion(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "题目ID格式错误")
		return
	}

	err = h.questionService.DeleteQuestion(uint(id))
	if err != nil {
		if err.Error() == "题目不存在" {
			utils.NotFound(c, "题目不存在")
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "题目删除成功", nil)
}
