#!/bin/bash

# 日志系统测试脚本

BASE_URL="http://localhost:8080"
ADMIN_TOKEN=""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# 检查服务器状态
check_server() {
    print_header "检查服务器状态"
    
    response=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL/health")
    
    if [ "$response" = "200" ]; then
        print_success "服务器运行正常"
        return 0
    else
        print_error "服务器未运行或无法访问 (HTTP $response)"
        return 1
    fi
}

# 管理员登录
admin_login() {
    print_header "管理员登录"
    
    response=$(curl -s -X POST "$BASE_URL/api/v1/admin/login" \
        -H "Content-Type: application/json" \
        -d '{
            "username": "15688515913",
            "password": "admin888"
        }')
    
    echo "登录响应: $response"
    
    # 提取token
    ADMIN_TOKEN=$(echo "$response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    
    if [ -n "$ADMIN_TOKEN" ]; then
        print_success "管理员登录成功"
        print_info "Token: ${ADMIN_TOKEN:0:20}..."
        return 0
    else
        print_error "管理员登录失败"
        return 1
    fi
}

# 测试API日志查询
test_api_logs() {
    print_header "测试API日志查询"
    
    if [ -z "$ADMIN_TOKEN" ]; then
        print_error "需要先登录管理员账户"
        return 1
    fi
    
    # 获取API日志列表
    print_info "获取API日志列表..."
    response=$(curl -s -X GET "$BASE_URL/api/v1/admin/logs/api?page=1&page_size=5" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json")
    
    echo "API日志响应: $response"
    
    # 检查响应
    if echo "$response" | grep -q '"code":200'; then
        print_success "API日志查询成功"
        
        # 提取日志数量
        total=$(echo "$response" | grep -o '"total":[0-9]*' | cut -d':' -f2)
        print_info "总日志数量: $total"
    else
        print_error "API日志查询失败"
        return 1
    fi
}

# 测试API日志统计
test_api_stats() {
    print_header "测试API日志统计"
    
    if [ -z "$ADMIN_TOKEN" ]; then
        print_error "需要先登录管理员账户"
        return 1
    fi
    
    # 获取API日志统计
    print_info "获取API日志统计..."
    response=$(curl -s -X GET "$BASE_URL/api/v1/admin/logs/api/stats" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json")
    
    echo "API统计响应: $response"
    
    # 检查响应
    if echo "$response" | grep -q '"code":200'; then
        print_success "API日志统计查询成功"
        
        # 提取统计信息
        total_calls=$(echo "$response" | grep -o '"total_calls":[0-9]*' | cut -d':' -f2)
        success_calls=$(echo "$response" | grep -o '"success_calls":[0-9]*' | cut -d':' -f2)
        error_calls=$(echo "$response" | grep -o '"error_calls":[0-9]*' | cut -d':' -f2)
        
        print_info "总调用次数: $total_calls"
        print_info "成功调用: $success_calls"
        print_info "错误调用: $error_calls"
    else
        print_error "API日志统计查询失败"
        return 1
    fi
}

# 测试系统日志查询
test_system_logs() {
    print_header "测试系统日志查询"
    
    if [ -z "$ADMIN_TOKEN" ]; then
        print_error "需要先登录管理员账户"
        return 1
    fi
    
    # 获取系统日志
    print_info "获取系统日志..."
    response=$(curl -s -X GET "$BASE_URL/api/v1/admin/logs/system?lines=10" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json")
    
    echo "系统日志响应: $response"
    
    # 检查响应
    if echo "$response" | grep -q '"code":200'; then
        print_success "系统日志查询成功"
        
        # 提取日志行数
        total=$(echo "$response" | grep -o '"total":[0-9]*' | cut -d':' -f2)
        print_info "日志行数: $total"
    else
        print_error "系统日志查询失败"
        return 1
    fi
}

# 测试日志界面访问
test_log_viewer() {
    print_header "测试日志查看界面"
    
    # 访问日志查看页面
    print_info "访问日志查看页面..."
    response=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL/logs")
    
    if [ "$response" = "200" ]; then
        print_success "日志查看界面访问成功"
        print_info "可以通过浏览器访问: $BASE_URL/logs"
    else
        print_error "日志查看界面访问失败 (HTTP $response)"
        return 1
    fi
    
    # 测试静态资源
    print_info "测试CSS资源..."
    response=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL/static/css/log-viewer.css")
    
    if [ "$response" = "200" ]; then
        print_success "CSS资源访问成功"
    else
        print_warning "CSS资源访问失败 (HTTP $response)"
    fi
    
    print_info "测试JS资源..."
    response=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL/static/js/log-viewer.js")
    
    if [ "$response" = "200" ]; then
        print_success "JS资源访问成功"
    else
        print_warning "JS资源访问失败 (HTTP $response)"
    fi
}

# 生成一些测试API调用
generate_test_logs() {
    print_header "生成测试日志"
    
    print_info "生成一些API调用来创建日志..."
    
    # 健康检查调用
    for i in {1..3}; do
        curl -s "$BASE_URL/health" > /dev/null
        print_info "健康检查调用 $i"
    done
    
    # 无效API调用（会产生错误日志）
    for i in {1..2}; do
        curl -s "$BASE_URL/api/v1/invalid" > /dev/null
        print_info "无效API调用 $i"
    done
    
    print_success "测试日志生成完成"
}

# 主函数
main() {
    print_header "日志系统测试开始"
    
    # 检查服务器
    if ! check_server; then
        print_error "请先启动服务器: ./solve_api"
        exit 1
    fi
    
    # 管理员登录
    if ! admin_login; then
        print_error "管理员登录失败，请检查账户信息"
        exit 1
    fi
    
    # 生成测试日志
    generate_test_logs
    
    # 等待日志写入
    print_info "等待日志写入..."
    sleep 2
    
    # 测试API日志功能
    test_api_logs
    test_api_stats
    
    # 测试系统日志功能
    test_system_logs
    
    # 测试日志界面
    test_log_viewer
    
    print_header "测试完成"
    print_success "所有测试已完成"
    print_info "日志管理界面: $BASE_URL/logs"
    print_info "管理员Token: $ADMIN_TOKEN"
    
    echo ""
    print_info "使用说明:"
    echo "1. 在浏览器中访问 $BASE_URL/logs"
    echo "2. 输入管理员Token: $ADMIN_TOKEN"
    echo "3. 即可查看和管理系统日志"
}

# 运行主函数
main "$@"
