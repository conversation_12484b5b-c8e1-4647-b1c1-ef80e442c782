# S1需求实施指南

## 概述

本文档描述了S1需求的完整实施方案，包括QWEN数据预处理、缓存优化、数据库重构和题库管理功能。

## 实施的功能

### 1. QWEN数据预处理逻辑

#### 实现的预处理步骤：
1. **题目内容验证**：检查题目值是否为空，为空则返回"图片不标准，请重新拍摄"
2. **题目类型前缀移除**：移除题目内容中可能存在的类型前缀（如"（单选题）"、"（多选题）"等）
3. **选项完整性检测**：
   - 单选题/多选题：检测ABCD四个选项是否完整，有空值则返回错误
   - 判断题：强制设置Y:正确，N:错误，丢弃ABCD选项
4. **数据标准化**：根据题目类型标准化选项格式

#### 关键代码位置：
- `internal/model/question.go` - `PreprocessQwenResult()` 函数
- `internal/service/question_service.go` - 更新的 `Search()` 方法

### 2. 缓存和查询逻辑优化

#### 新的查询流程：
1. **基于预处理内容生成缓存键**
2. **Redis查询** → 检查关联键 → 返回相关题目
3. **MySQL降级查询** → 回传Redis → 返回结果
4. **DeepSeek调用** → 保存到数据库和缓存

#### 关联键支持：
- 支持题目间的关联关系
- 查询时自动返回关联题目
- Redis缓存包含关联信息

#### 关键代码位置：
- `internal/repository/question_cache_repository.go` - `GetWithAssociates()` 方法
- `internal/repository/question_repository.go` - 关联查询方法

### 3. 数据库表重构

#### 新增字段：
```sql
cache_key VARCHAR(128)           -- 缓存键
question_doc TEXT               -- 题目内容  
question_img TEXT               -- 题目图片
question_img_raw TEXT           -- 用户图片
options_a TEXT                  -- 选项A
options_b TEXT                  -- 选项B  
options_c TEXT                  -- 选项C
options_d TEXT                  -- 选项D
options_y TEXT                  -- 选项Y
options_n TEXT                  -- 选项N
response INT DEFAULT 0          -- 响应次数
raw_qwen LONGTEXT              -- qwen原始数据
raw_deepseek LONGTEXT          -- deepseek原始数据
associates TEXT                -- 关联键
```

#### 字段权限：
- **禁止修改**：cache_key, response, raw_qwen, raw_deepseek, created_at
- **允许修改**：其他所有字段

### 4. 题库管理控制器

#### API接口：
- `POST /api/v1/admin/question-management` - 创建题目
- `GET /api/v1/admin/question-management` - 获取题目列表
- `GET /api/v1/admin/question-management/{id}` - 获取题目详情
- `PUT /api/v1/admin/question-management/{id}` - 更新题目
- `DELETE /api/v1/admin/question-management/{id}` - 删除题目

#### 功能特性：
- 支持分页查询
- 支持按题目类型过滤
- 支持关键词搜索
- 完整的CRUD操作
- 字段权限控制

## 部署步骤

### 1. 数据库迁移

```bash
# 执行数据库迁移脚本
mysql -u username -p database_name < migrations/restructure_questions_table.sql
```

### 2. 代码部署

```bash
# 编译项目
go build -o solve_api cmd/main.go

# 运行服务
./solve_api
```

### 3. 功能测试

```bash
# 运行测试脚本
chmod +x test_s1_features.sh
./test_s1_features.sh
```

## 测试用例

### 1. 预处理逻辑测试

#### 测试用例1：题目内容为空
```json
{
  "question_text": "",
  "question_type": "单选题"
}
```
**期望结果**：返回"图片不标准，请重新拍摄"

#### 测试用例2：题目内容包含类型前缀
```json
{
  "question_text": "（单选题）以下哪个是正确的？",
  "question_type": "单选题"
}
```
**期望结果**：题目内容变为"以下哪个是正确的？"

#### 测试用例3：单选题选项不完整
```json
{
  "question_type": "单选题",
  "options": {"A": "选项A", "B": "", "C": "选项C", "D": "选项D"}
}
```
**期望结果**：返回"图片不标准，请重新拍摄"

#### 测试用例4：判断题选项处理
```json
{
  "question_type": "判断题",
  "options": {"A": "选项A", "B": "选项B"}
}
```
**期望结果**：选项变为{"Y": "正确", "N": "错误"}

### 2. 缓存功能测试

#### 测试缓存键生成
- 相同内容应生成相同缓存键
- 不同内容应生成不同缓存键
- 选项顺序不影响缓存键

#### 测试关联键功能
- 创建带关联键的题目
- 查询时返回所有关联题目
- Redis缓存包含关联信息

### 3. 题库管理测试

#### 创建题目测试
```bash
curl -X POST http://localhost:8080/api/v1/admin/question-management \
  -H "Content-Type: application/json" \
  -d '{
    "question_type": "单选题",
    "question_doc": "测试题目",
    "options_a": "选项A",
    "options_b": "选项B", 
    "options_c": "选项C",
    "options_d": "选项D",
    "answer": "A",
    "analysis": "解析内容"
  }'
```

#### 更新题目测试
```bash
curl -X PUT http://localhost:8080/api/v1/admin/question-management/1 \
  -H "Content-Type: application/json" \
  -d '{
    "question_doc": "更新后的题目",
    "analysis": "更新后的解析"
  }'
```

## 注意事项

### 1. 兼容性
- 保留了所有原有字段，确保向后兼容
- 新旧API可以并存使用
- 缓存键生成支持新旧格式

### 2. 性能优化
- 预处理逻辑在内存中执行，性能影响最小
- 缓存查询优先级：Redis → MySQL → DeepSeek
- 关联查询使用索引优化

### 3. 错误处理
- 预处理失败时返回明确错误信息
- 缓存失败不影响主流程
- 数据库操作有完整的错误处理

### 4. 安全考虑
- 题库管理需要管理员权限
- 字段更新权限控制
- SQL注入防护

## 监控和日志

### 1. 关键指标
- 预处理成功率
- 缓存命中率
- 关联查询性能
- API响应时间

### 2. 日志记录
- 预处理错误日志
- 缓存操作日志
- 数据库查询日志
- API调用日志

## 后续优化建议

1. **性能优化**：
   - 实现缓存预热
   - 优化关联查询算法
   - 添加查询结果缓存

2. **功能扩展**：
   - 支持批量导入题目
   - 添加题目审核流程
   - 实现题目版本管理

3. **监控完善**：
   - 添加性能监控
   - 实现告警机制
   - 完善日志分析
