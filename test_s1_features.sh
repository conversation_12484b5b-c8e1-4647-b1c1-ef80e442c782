#!/bin/bash

# S1功能测试脚本
# 测试新增的预处理逻辑、缓存功能和题库管理功能

BASE_URL="http://localhost:8080/api/v1"
ADMIN_ID="1"  # 假设管理员ID为1

echo "=== S1功能测试脚本 ==="
echo "测试服务器: $BASE_URL"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local method=$1
    local url=$2
    local data=$3
    local description=$4
    
    echo -e "${YELLOW}测试: $description${NC}"
    echo "请求: $method $url"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" "$url")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" -H "Content-Type: application/json" -d "$data" "$url")
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" -eq 200 ]; then
        echo -e "${GREEN}✅ 成功 (HTTP $http_code)${NC}"
        echo "响应: $body" | jq . 2>/dev/null || echo "响应: $body"
    else
        echo -e "${RED}❌ 失败 (HTTP $http_code)${NC}"
        echo "响应: $body"
    fi
    echo "----------------------------------------"
}

# 1. 测试题库管理 - 创建题目
echo -e "${YELLOW}1. 测试题库管理功能${NC}"

# 创建单选题
single_choice_data='{
    "question_type": "单选题",
    "question_doc": "以下哪个是正确的？",
    "options_a": "选项A",
    "options_b": "选项B", 
    "options_c": "选项C",
    "options_d": "选项D",
    "answer": "A",
    "analysis": "这是解析内容"
}'

test_api "POST" "$BASE_URL/admin/question-management" "$single_choice_data" "创建单选题"

# 创建判断题
judgment_data='{
    "question_type": "判断题",
    "question_doc": "地球是圆的。",
    "answer": "Y",
    "analysis": "地球确实是圆的"
}'

test_api "POST" "$BASE_URL/admin/question-management" "$judgment_data" "创建判断题"

# 创建多选题
multi_choice_data='{
    "question_type": "多选题",
    "question_doc": "以下哪些是正确的？",
    "options_a": "选项A",
    "options_b": "选项B",
    "options_c": "选项C", 
    "options_d": "选项D",
    "answer": "A,B",
    "analysis": "A和B都是正确的"
}'

test_api "POST" "$BASE_URL/admin/question-management" "$multi_choice_data" "创建多选题"

# 2. 测试题目列表查询
echo -e "${YELLOW}2. 测试题目列表查询${NC}"
test_api "GET" "$BASE_URL/admin/question-management?page=1&limit=10" "" "获取题目列表"

# 3. 测试题目详情查询
echo -e "${YELLOW}3. 测试题目详情查询${NC}"
test_api "GET" "$BASE_URL/admin/question-management/1" "" "获取题目详情"

# 4. 测试题目更新
echo -e "${YELLOW}4. 测试题目更新${NC}"
update_data='{
    "question_doc": "更新后的题目内容",
    "analysis": "更新后的解析"
}'

test_api "PUT" "$BASE_URL/admin/question-management/1" "$update_data" "更新题目"

# 5. 测试预处理逻辑（通过拍照搜题接口）
echo -e "${YELLOW}5. 测试预处理逻辑${NC}"

# 注意：这个测试需要有效的API密钥，这里只是示例
search_data='{
    "image_url": "https://example.com/test-image.jpg",
    "app_key": "test_app_key",
    "secret_key": "test_secret_key"
}'

echo "注意：拍照搜题测试需要有效的API密钥和图片URL"
echo "测试数据: $search_data"

# 6. 测试关联键功能
echo -e "${YELLOW}6. 测试关联键功能${NC}"

# 创建带关联键的题目
associated_data='{
    "question_type": "单选题",
    "question_doc": "关联题目测试",
    "options_a": "选项A",
    "options_b": "选项B",
    "options_c": "选项C",
    "options_d": "选项D", 
    "answer": "A",
    "analysis": "关联题目解析",
    "associates": "math_basic_001"
}'

test_api "POST" "$BASE_URL/admin/question-management" "$associated_data" "创建带关联键的题目"

# 7. 测试题目类型过滤
echo -e "${YELLOW}7. 测试题目类型过滤${NC}"
test_api "GET" "$BASE_URL/admin/question-management?question_type=单选题" "" "按题目类型过滤"

# 8. 测试关键词搜索
echo -e "${YELLOW}8. 测试关键词搜索${NC}"
test_api "GET" "$BASE_URL/admin/question-management?keyword=测试" "" "关键词搜索"

echo -e "${GREEN}=== S1功能测试完成 ===${NC}"
echo
echo "测试说明："
echo "1. 题库管理功能包括创建、查询、更新、删除题目"
echo "2. 支持单选题、多选题、判断题等不同类型"
echo "3. 预处理逻辑会自动处理题目内容中的类型前缀"
echo "4. 关联键功能支持题目之间的关联"
echo "5. 缓存功能基于预处理后的内容生成键值"
echo
echo "注意事项："
echo "- 拍照搜题功能需要配置有效的API密钥"
echo "- 需要先运行数据库迁移脚本"
echo "- 确保Redis和MySQL服务正常运行"
