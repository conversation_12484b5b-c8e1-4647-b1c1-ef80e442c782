package main

import (
	"fmt"
	"gorm.io/gorm"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/model"
)

func main() {
	fmt.Println("=== 创建数据库表 ===")

	// 1. 加载配置
	fmt.Println("1. 加载配置...")
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		fmt.Printf("❌ 配置加载失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 配置加载成功\n")

	// 2. 初始化MySQL（跳过Redis）
	fmt.Println("2. 初始化MySQL...")
	err = database.InitMySQL(&cfg.Database.MySQL)
	if err != nil {
		fmt.Printf("❌ MySQL初始化失败: %v\n", err)
		return
	}
	defer database.CloseMySQL()
	fmt.Printf("✅ MySQL连接成功\n")

	// 3. 执行数据库迁移
	fmt.Println("3. 执行数据库迁移...")
	db := database.GetDB()

	err = db.AutoMigrate(
		&model.User{},
		&model.SystemConfig{},
		&model.Admin{},
	)
	if err != nil {
		fmt.Printf("❌ 数据库迁移失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 数据库迁移成功\n")

	// 4. 验证表创建
	fmt.Println("4. 验证表创建...")
	tables := []string{"users", "system_configs", "admins"}

	for _, table := range tables {
		var count int64
		err := db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?", table).Scan(&count).Error
		if err != nil {
			fmt.Printf("   ❌ 检查表 %s 失败: %v\n", table, err)
		} else if count > 0 {
			fmt.Printf("   ✅ 表 %s 创建成功\n", table)

			// 检查表结构
			var recordCount int64
			err = db.Table(table).Count(&recordCount).Error
			if err != nil {
				fmt.Printf("      ❌ 查询记录数失败: %v\n", err)
			} else {
				fmt.Printf("      记录数: %d\n", recordCount)
			}
		} else {
			fmt.Printf("   ❌ 表 %s 不存在\n", table)
		}
	}

	// 5. 初始化系统配置
	fmt.Println("5. 初始化系统配置...")
	err = initSystemConfig(db, cfg)
	if err != nil {
		fmt.Printf("❌ 系统配置初始化失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 系统配置初始化成功\n")

	// 6. 创建默认管理员
	fmt.Println("6. 创建默认管理员...")
	err = createDefaultAdmin(db)
	if err != nil {
		fmt.Printf("❌ 默认管理员创建失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 默认管理员创建成功\n")

	fmt.Println("\n=== 数据库初始化完成 ===")
	fmt.Println("MySQL连接正常，所有表已创建")
	fmt.Println("注意：Redis连接需要正确的密码才能正常工作")
}

func initSystemConfig(db *gorm.DB, cfg *config.Config) error {
	// 检查配置是否已存在
	var count int64
	db.Model(&model.SystemConfig{}).Where("key = ?", model.ConfigKeyInviteCode).Count(&count)

	if count == 0 {
		configs := []model.SystemConfig{
			{
				Key:         model.ConfigKeyInviteCode,
				Value:       cfg.App.InviteCode,
				Description: "系统邀请码",
			},
			{
				Key:         model.ConfigKeyRateLimit,
				Value:       fmt.Sprintf("%d", cfg.App.RateLimit),
				Description: "API限流配置（次/秒）",
			},
			{
				Key:         model.ConfigKeyCacheTTL,
				Value:       fmt.Sprintf("%d", cfg.App.CacheTTL),
				Description: "缓存TTL配置（秒）",
			},
		}

		for _, config := range configs {
			if err := db.Create(&config).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

func createDefaultAdmin(db *gorm.DB) error {
	// 检查默认管理员是否已存在
	var count int64
	db.Model(&model.Admin{}).Where("username = ?", "admin").Count(&count)

	if count == 0 {
		// 创建默认管理员
		admin := model.Admin{
			Username: "admin",
			Password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // admin123
			Role:     model.AdminRoleSuperAdmin,
		}

		return db.Create(&admin).Error
	}

	return nil
}
