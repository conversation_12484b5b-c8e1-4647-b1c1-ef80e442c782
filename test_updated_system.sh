#!/bin/bash

# 更新后系统测试脚本

BASE_URL="http://localhost:8080"
ADMIN_TOKEN=""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# 检查服务器状态
check_server() {
    print_header "检查服务器状态"
    
    response=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL/health")
    
    if [ "$response" = "200" ]; then
        print_success "服务器运行正常"
        return 0
    else
        print_error "服务器未运行或无法访问 (HTTP $response)"
        return 1
    fi
}

# 管理员登录
admin_login() {
    print_header "管理员登录"
    
    response=$(curl -s -X POST "$BASE_URL/api/v1/admin/login" \
        -H "Content-Type: application/json" \
        -d '{
            "phone": "15688515913",
            "password": "admin888"
        }')
    
    echo "登录响应: $response"
    
    if echo "$response" | grep -q '"code":200'; then
        print_success "管理员登录成功"
        return 0
    else
        print_error "管理员登录失败"
        return 1
    fi
}

# 初始化模型配置
init_models() {
    print_header "初始化模型配置"
    
    print_info "运行模型初始化脚本..."
    cd /Users/<USER>/go_lang/solve_api
    go run scripts/init_models.go
    
    if [ $? -eq 0 ]; then
        print_success "模型初始化完成"
    else
        print_error "模型初始化失败"
        return 1
    fi
}

# 迁移API日志表
migrate_api_logs() {
    print_header "迁移API日志表"
    
    print_info "运行API日志表迁移脚本..."
    cd /Users/<USER>/go_lang/solve_api
    go run scripts/migrate_api_logs.go
    
    if [ $? -eq 0 ]; then
        print_success "API日志表迁移完成"
    else
        print_error "API日志表迁移失败"
        return 1
    fi
}

# 测试固定模型配置接口
test_fixed_models() {
    print_header "测试固定模型配置接口"
    
    # 获取固定模型配置
    print_info "获取固定模型配置..."
    response=$(curl -s -X GET "$BASE_URL/api/v1/admin/model/fixed")
    
    echo "固定模型响应: $response"
    
    if echo "$response" | grep -q '"code":200'; then
        print_success "固定模型配置查询成功"
        
        # 检查是否包含两个模型
        if echo "$response" | grep -q '"qwen"' && echo "$response" | grep -q '"deepseek"'; then
            print_success "包含Qwen和DeepSeek两个模型"
        else
            print_warning "模型配置可能不完整"
        fi
    else
        print_error "固定模型配置查询失败"
        return 1
    fi
}

# 测试按名称获取模型
test_get_model_by_name() {
    print_header "测试按名称获取模型"
    
    # 测试获取Qwen模型
    print_info "获取Qwen模型配置..."
    response=$(curl -s -X GET "$BASE_URL/api/v1/admin/model/name/qwen-vl-plus")
    
    echo "Qwen模型响应: $response"
    
    if echo "$response" | grep -q '"code":200'; then
        print_success "Qwen模型配置查询成功"
    else
        print_error "Qwen模型配置查询失败"
    fi
    
    # 测试获取DeepSeek模型
    print_info "获取DeepSeek模型配置..."
    response=$(curl -s -X GET "$BASE_URL/api/v1/admin/model/name/deepseek-chat")
    
    echo "DeepSeek模型响应: $response"
    
    if echo "$response" | grep -q '"code":200'; then
        print_success "DeepSeek模型配置查询成功"
    else
        print_error "DeepSeek模型配置查询失败"
    fi
}

# 测试更新模型参数
test_update_model_params() {
    print_header "测试更新模型参数"
    
    # 更新Qwen模型参数
    print_info "更新Qwen模型参数..."
    response=$(curl -s -X PUT "$BASE_URL/api/v1/admin/model/name/qwen-vl-plus/params" \
        -H "Content-Type: application/json" \
        -d '{
            "params": {
                "temperature": 0.5,
                "max_tokens": 2000,
                "top_p": 0.9,
                "response_format": {"type": "json_object"},
                "detail": "high"
            }
        }')
    
    echo "参数更新响应: $response"
    
    if echo "$response" | grep -q '"code":200'; then
        print_success "Qwen模型参数更新成功"
    else
        print_error "Qwen模型参数更新失败"
    fi
}

# 测试API日志接口
test_api_logs() {
    print_header "测试API日志接口"
    
    # 获取API日志列表
    print_info "获取API日志列表..."
    response=$(curl -s -X GET "$BASE_URL/api/v1/admin/logs/api?page=1&page_size=5")
    
    echo "API日志响应: $response"
    
    if echo "$response" | grep -q '"code":200'; then
        print_success "API日志查询成功"
        
        # 检查是否包含新字段
        if echo "$response" | grep -q '"model_name"'; then
            print_success "包含模型名称字段"
        else
            print_info "暂无模型名称数据（正常，需要新的API调用才会有）"
        fi
    else
        print_error "API日志查询失败"
        return 1
    fi
}

# 测试API日志统计
test_api_log_stats() {
    print_header "测试API日志统计"
    
    # 获取API日志统计
    print_info "获取API日志统计..."
    response=$(curl -s -X GET "$BASE_URL/api/v1/admin/logs/api/stats")
    
    echo "API统计响应: $response"
    
    if echo "$response" | grep -q '"code":200'; then
        print_success "API日志统计查询成功"
        
        # 提取统计信息
        total_calls=$(echo "$response" | grep -o '"total_calls":[0-9]*' | cut -d':' -f2)
        success_calls=$(echo "$response" | grep -o '"success_calls":[0-9]*' | cut -d':' -f2)
        error_calls=$(echo "$response" | grep -o '"error_calls":[0-9]*' | cut -d':' -f2)
        
        print_info "总调用次数: $total_calls"
        print_info "成功调用: $success_calls"
        print_info "错误调用: $error_calls"
    else
        print_error "API日志统计查询失败"
        return 1
    fi
}

# 测试无效模型名称
test_invalid_model_name() {
    print_header "测试无效模型名称"
    
    # 测试无效模型名称
    print_info "测试无效模型名称..."
    response=$(curl -s -X GET "$BASE_URL/api/v1/admin/model/name/invalid-model")
    
    echo "无效模型响应: $response"
    
    if echo "$response" | grep -q '"code":400'; then
        print_success "正确拒绝了无效模型名称"
    else
        print_warning "无效模型名称处理可能有问题"
    fi
}

# 主函数
main() {
    print_header "更新后系统测试开始"
    
    # 检查服务器
    if ! check_server; then
        print_error "请先启动服务器: ./solve_api"
        exit 1
    fi
    
    # 管理员登录
    admin_login
    
    # 初始化模型配置
    init_models
    
    # 迁移API日志表
    migrate_api_logs
    
    # 等待数据库操作完成
    print_info "等待数据库操作完成..."
    sleep 2
    
    # 测试模型配置功能
    test_fixed_models
    test_get_model_by_name
    test_update_model_params
    test_invalid_model_name
    
    # 测试日志功能
    test_api_logs
    test_api_log_stats
    
    print_header "测试完成"
    print_success "所有测试已完成"
    
    echo ""
    print_info "新功能说明:"
    echo "1. 固定模型配置: $BASE_URL/api/v1/admin/model/fixed"
    echo "2. 按名称获取模型: $BASE_URL/api/v1/admin/model/name/{name}"
    echo "3. 更新模型参数: PUT $BASE_URL/api/v1/admin/model/name/{name}/params"
    echo "4. 更新API密钥: PUT $BASE_URL/api/v1/admin/model/name/{name}/apikey"
    echo "5. API日志增强: 包含模型请求和返回信息"
    
    echo ""
    print_info "支持的模型名称:"
    echo "- qwen-vl-plus (识图大模型)"
    echo "- deepseek-chat (解题大模型)"
}

# 运行主函数
main "$@"
