package service

import (
	"fmt"
	"solve_api/internal/model"
	"solve_api/internal/repository"
)

type ConfigService struct {
	configRepo *repository.ConfigRepository
}

// NewConfigService 创建配置服务实例
func NewConfigService(configRepo *repository.ConfigRepository) *ConfigService {
	return &ConfigService{
		configRepo: configRepo,
	}
}

// GetConfig 获取配置值
func (s *ConfigService) GetConfig(key string) (string, error) {
	return s.configRepo.GetValue(key)
}

// SetConfig 设置配置值
func (s *ConfigService) SetConfig(key, value string) error {
	return s.configRepo.SetValue(key, value, "")
}

// GetAllConfigs 获取所有配置
func (s *ConfigService) GetAllConfigs() (map[string]string, error) {
	configs, err := s.configRepo.GetAll()
	if err != nil {
		return nil, fmt.Errorf("获取配置失败: %w", err)
	}

	result := make(map[string]string)
	for _, config := range configs {
		result[config.Key] = config.Value
	}

	return result, nil
}

// GetConfigsByKeys 根据键列表获取配置
func (s *ConfigService) GetConfigsByKeys(keys []string) (map[string]string, error) {
	result := make(map[string]string)

	for _, key := range keys {
		value, err := s.configRepo.GetValue(key)
		if err != nil {
			// 如果配置不存在，使用默认值
			result[key] = s.getDefaultValue(key)
		} else {
			result[key] = value
		}
	}

	return result, nil
}

// getDefaultValue 获取默认配置值
func (s *ConfigService) getDefaultValue(key string) string {
	switch key {
	case model.ConfigKeyInviteCode:
		return "SOLVE2024"
	case model.ConfigKeyRateLimit:
		return "10"
	case model.ConfigKeyCacheTTL:
		return "3600"
	default:
		return ""
	}
}

// UpdateConfigs 批量更新配置
func (s *ConfigService) UpdateConfigs(configs map[string]string) error {
	for key, value := range configs {
		if err := s.configRepo.SetValue(key, value, ""); err != nil {
			return fmt.Errorf("更新配置 %s 失败: %w", key, err)
		}
	}
	return nil
}

// DeleteConfig 删除配置
func (s *ConfigService) DeleteConfig(key string) error {
	return s.configRepo.DeleteByKey(key)
}

// GetSystemSettings 获取系统设置
func (s *ConfigService) GetSystemSettings() (map[string]interface{}, error) {
	configs, err := s.GetAllConfigs()
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"invite_code": configs[model.ConfigKeyInviteCode],
		"rate_limit":  configs[model.ConfigKeyRateLimit],
		"cache_ttl":   configs[model.ConfigKeyCacheTTL],
	}, nil
}
