package service

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"
)

// AILogEntry AI模型日志条目
type AILogEntry struct {
	ID        string       `json:"id"`
	Timestamp string       `json:"timestamp"`
	Qwen      *AIModelData `json:"qwen,omitempty"`
	DeepSeek  *AIModelData `json:"deepseek,omitempty"`
}

// AIModelData AI模型数据
type AIModelData struct {
	Request  string `json:"request"`  // 原始请求数据
	Response string `json:"response"` // 原始响应Content数据
}

// AILogService AI日志服务
type AILogService struct {
	logs  []AILogEntry
	mutex sync.RWMutex
}

// NewAILogService 创建AI日志服务实例
func NewAILogService() *AILogService {
	return &AILogService{
		logs: make([]AILogEntry, 0),
	}
}

// CreateLogEntry 创建新的日志条目
func (s *AILogService) CreateLogEntry() string {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	logID := fmt.Sprintf("log_%d", time.Now().UnixNano())
	entry := AILogEntry{
		ID:        logID,
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
	}

	s.logs = append(s.logs, entry)
	return logID
}

// LogQwenRequest 记录Qwen请求数据
func (s *AILogService) LogQwenRequest(logID string, requestData interface{}) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for i := range s.logs {
		if s.logs[i].ID == logID {
			if s.logs[i].Qwen == nil {
				s.logs[i].Qwen = &AIModelData{}
			}

			// 将请求数据序列化为JSON字符串
			if requestBytes, err := json.MarshalIndent(requestData, "", "  "); err == nil {
				s.logs[i].Qwen.Request = string(requestBytes)
			} else {
				s.logs[i].Qwen.Request = fmt.Sprintf("序列化失败: %v", err)
			}
			break
		}
	}
}

// LogQwenResponse 记录Qwen响应数据
func (s *AILogService) LogQwenResponse(logID string, responseContent string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for i := range s.logs {
		if s.logs[i].ID == logID {
			if s.logs[i].Qwen == nil {
				s.logs[i].Qwen = &AIModelData{}
			}
			s.logs[i].Qwen.Response = responseContent
			break
		}
	}
}

// LogDeepSeekRequest 记录DeepSeek请求数据
func (s *AILogService) LogDeepSeekRequest(logID string, requestData interface{}) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for i := range s.logs {
		if s.logs[i].ID == logID {
			if s.logs[i].DeepSeek == nil {
				s.logs[i].DeepSeek = &AIModelData{}
			}

			// 将请求数据序列化为JSON字符串
			if requestBytes, err := json.MarshalIndent(requestData, "", "  "); err == nil {
				s.logs[i].DeepSeek.Request = string(requestBytes)
			} else {
				s.logs[i].DeepSeek.Request = fmt.Sprintf("序列化失败: %v", err)
			}
			break
		}
	}
}

// LogDeepSeekResponse 记录DeepSeek响应数据
func (s *AILogService) LogDeepSeekResponse(logID string, responseContent string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	for i := range s.logs {
		if s.logs[i].ID == logID {
			if s.logs[i].DeepSeek == nil {
				s.logs[i].DeepSeek = &AIModelData{}
			}
			s.logs[i].DeepSeek.Response = responseContent
			break
		}
	}
}

// GetLogs 获取所有日志
func (s *AILogService) GetLogs() []AILogEntry {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// 返回副本，按时间倒序
	logs := make([]AILogEntry, len(s.logs))
	for i, j := 0, len(s.logs)-1; j >= 0; i, j = i+1, j-1 {
		logs[i] = s.logs[j]
	}
	return logs
}

// GetLogByID 根据ID获取日志
func (s *AILogService) GetLogByID(logID string) *AILogEntry {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	for i := range s.logs {
		if s.logs[i].ID == logID {
			return &s.logs[i]
		}
	}
	return nil
}

// ClearLogs 清空所有日志
func (s *AILogService) ClearLogs() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.logs = make([]AILogEntry, 0)
}

// GetLogCount 获取日志数量
func (s *AILogService) GetLogCount() int {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	return len(s.logs)
}

// GetRecentLogs 获取最近的N条日志
func (s *AILogService) GetRecentLogs(limit int) []AILogEntry {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if limit <= 0 || limit > len(s.logs) {
		limit = len(s.logs)
	}

	// 返回最近的日志（倒序）
	logs := make([]AILogEntry, limit)
	for i := 0; i < limit; i++ {
		logs[i] = s.logs[len(s.logs)-1-i]
	}
	return logs
}

// CleanOldLogs 清理旧日志（保留最近的N条）
func (s *AILogService) CleanOldLogs(keepCount int) int {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if keepCount <= 0 || len(s.logs) <= keepCount {
		return 0
	}

	removedCount := len(s.logs) - keepCount
	s.logs = s.logs[removedCount:]
	return removedCount
}

// GetLogStats 获取日志统计信息
func (s *AILogService) GetLogStats() map[string]interface{} {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_logs":    len(s.logs),
		"qwen_logs":     0,
		"deepseek_logs": 0,
		"complete_logs": 0, // 同时包含Qwen和DeepSeek数据的日志
	}

	for _, log := range s.logs {
		if log.Qwen != nil {
			stats["qwen_logs"] = stats["qwen_logs"].(int) + 1
		}
		if log.DeepSeek != nil {
			stats["deepseek_logs"] = stats["deepseek_logs"].(int) + 1
		}
		if log.Qwen != nil && log.DeepSeek != nil {
			stats["complete_logs"] = stats["complete_logs"].(int) + 1
		}
	}

	return stats
}
