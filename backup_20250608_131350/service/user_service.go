package service

import (
	"context"
	"fmt"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"solve_api/internal/utils"
	"time"

	"github.com/redis/go-redis/v9"
)

type UserService struct {
	userRepo   *repository.UserRepository
	configRepo *repository.ConfigRepository
	redis      *redis.Client
	smsService utils.SMSService
}

// NewUserService 创建用户服务实例
func NewUserService(userRepo *repository.UserRepository, configRepo *repository.ConfigRepository, smsService utils.SMSService) *UserService {
	redis := database.GetRedis()
	return &UserService{
		userRepo:   userRepo,
		configRepo: configRepo,
		redis:      redis,
		smsService: smsService,
	}
}

// Register 用户注册
func (s *UserService) Register(req *model.UserRegisterRequest) (*model.UserResponse, error) {
	// 1. 验证手机号格式
	if !utils.ValidatePhone(req.Phone) {
		return nil, fmt.Errorf("手机号格式不正确")
	}

	// 2. 验证密码强度
	if err := utils.ValidatePassword(req.Password); err != nil {
		return nil, err
	}

	// 3. 验证验证码
	if err := s.verifyCode(req.Phone, req.Code); err != nil {
		return nil, err
	}

	// 4. 验证邀请码
	if err := s.verifyInviteCode(req.InviteCode); err != nil {
		return nil, err
	}

	// 5. 检查手机号是否已注册
	exists, err := s.userRepo.ExistsByPhone(req.Phone)
	if err != nil {
		return nil, fmt.Errorf("检查手机号失败: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("手机号已注册")
	}

	// 6. 加密密码
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %w", err)
	}

	// 7. 创建用户
	user := &model.User{
		Phone:    req.Phone,
		Password: hashedPassword,
		Balance:  0,
		Status:   model.UserStatusNormal,
	}

	if err := s.userRepo.Create(user); err != nil {
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	// 8. 删除验证码缓存
	s.deleteCodeCache(req.Phone)

	return user.ToResponse(), nil
}

// Login 用户登录
func (s *UserService) Login(req *model.UserLoginRequest) (*model.UserResponse, error) {
	// 1. 验证手机号格式
	if !utils.ValidatePhone(req.Phone) {
		return nil, fmt.Errorf("手机号格式不正确")
	}

	// 2. 查找用户
	user, err := s.userRepo.GetByPhone(req.Phone)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 3. 检查用户状态
	if user.IsFrozen() {
		return nil, fmt.Errorf("账户已被冻结")
	}

	// 4. 验证密码
	if !utils.CheckPassword(req.Password, user.Password) {
		return nil, fmt.Errorf("密码错误")
	}

	return user.ToResponse(), nil
}

// GetProfile 获取用户信息
func (s *UserService) GetProfile(userID uint) (*model.UserResponse, error) {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	return user.ToResponse(), nil
}

// UpdateProfile 更新用户信息
func (s *UserService) UpdateProfile(userID uint, req *model.UserProfileUpdateRequest) (*model.UserResponse, error) {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 更新密码
	if req.Password != "" {
		if err := utils.ValidatePassword(req.Password); err != nil {
			return nil, err
		}
		hashedPassword, err := utils.HashPassword(req.Password)
		if err != nil {
			return nil, fmt.Errorf("密码加密失败: %w", err)
		}
		if err := s.userRepo.UpdatePassword(userID, hashedPassword); err != nil {
			return nil, fmt.Errorf("更新密码失败: %w", err)
		}
	}

	// 重新获取用户信息
	user, err = s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	return user.ToResponse(), nil
}

// SendCode 发送验证码
func (s *UserService) SendCode(req *model.SendCodeRequest) error {
	// 1. 验证手机号格式
	if !utils.ValidatePhone(req.Phone) {
		return fmt.Errorf("手机号格式不正确")
	}

	// 2. 检查发送频率限制
	if err := s.checkCodeSendLimit(req.Phone); err != nil {
		return err
	}

	// 3. 生成验证码
	code := utils.GenerateVerificationCode()

	// 4. 存储验证码到Redis（5分钟过期）
	if s.redis != nil {
		ctx := context.Background()
		codeKey := s.getCodeCacheKey(req.Phone)
		if err := s.redis.Set(ctx, codeKey, code, 5*time.Minute).Err(); err != nil {
			return fmt.Errorf("存储验证码失败: %w", err)
		}

		// 5. 设置发送频率限制（60秒内不能重复发送）
		limitKey := s.getCodeLimitKey(req.Phone)
		if err := s.redis.Set(ctx, limitKey, "1", time.Minute).Err(); err != nil {
			return fmt.Errorf("设置发送限制失败: %w", err)
		}
	} else {
		fmt.Printf("⚠️  Redis不可用，验证码: %s\n", code)
	}

	// 6. 发送短信（这里模拟发送）
	if err := s.sendSMS(req.Phone, code); err != nil {
		return fmt.Errorf("发送短信失败: %w", err)
	}

	return nil
}

// verifyCode 验证验证码
func (s *UserService) verifyCode(phone, code string) error {
	// 如果Redis不可用，跳过验证码检查（开发模式）
	if s.redis == nil {
		fmt.Printf("⚠️  Redis不可用，跳过验证码验证\n")
		return nil
	}

	ctx := context.Background()
	codeKey := s.getCodeCacheKey(phone)

	storedCode, err := s.redis.Get(ctx, codeKey).Result()
	if err != nil {
		if err == redis.Nil {
			return fmt.Errorf("验证码已过期或不存在")
		}
		return fmt.Errorf("获取验证码失败: %w", err)
	}

	if storedCode != code {
		return fmt.Errorf("验证码错误")
	}

	return nil
}

// verifyInviteCode 验证邀请码
func (s *UserService) verifyInviteCode(inviteCode string) error {
	// 从配置中获取邀请码
	configInviteCode, err := s.configRepo.GetValueByKey(model.ConfigKeyInviteCode)
	if err != nil {
		return fmt.Errorf("获取邀请码配置失败: %w", err)
	}

	// 如果配置中没有邀请码，使用默认值
	if configInviteCode == "" {
		configInviteCode = config.GlobalConfig.App.InviteCode
	}

	if inviteCode != configInviteCode {
		return fmt.Errorf("邀请码错误")
	}

	return nil
}

// checkCodeSendLimit 检查验证码发送频率限制
func (s *UserService) checkCodeSendLimit(phone string) error {
	// 如果Redis不可用，跳过频率限制检查
	if s.redis == nil {
		fmt.Printf("⚠️  Redis不可用，跳过发送频率限制检查\n")
		return nil
	}

	ctx := context.Background()
	limitKey := s.getCodeLimitKey(phone)

	exists, err := s.redis.Exists(ctx, limitKey).Result()
	if err != nil {
		return fmt.Errorf("检查发送限制失败: %w", err)
	}

	if exists > 0 {
		return fmt.Errorf("验证码发送过于频繁，请稍后再试")
	}

	return nil
}

// sendSMS 发送短信
func (s *UserService) sendSMS(phone, code string) error {
	return s.smsService.SendCode(phone, code)
}

// deleteCodeCache 删除验证码缓存
func (s *UserService) deleteCodeCache(phone string) {
	if s.redis != nil {
		ctx := context.Background()
		codeKey := s.getCodeCacheKey(phone)
		s.redis.Del(ctx, codeKey)
	}
}

// getCodeCacheKey 获取验证码缓存键
func (s *UserService) getCodeCacheKey(phone string) string {
	return fmt.Sprintf("sms_code:%s", phone)
}

// getCodeLimitKey 获取验证码发送限制键
func (s *UserService) getCodeLimitKey(phone string) string {
	return fmt.Sprintf("sms_limit:%s", phone)
}

// ChangePassword 用户修改密码（使用原密码）
func (s *UserService) ChangePassword(userID uint, req *model.UserChangePasswordRequest) (*model.UserResponse, error) {
	// 1. 获取用户信息
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 2. 检查用户状态
	if user.IsFrozen() {
		return nil, fmt.Errorf("账户已被冻结，无法修改密码")
	}

	// 3. 验证原密码
	if !utils.CheckPassword(req.OldPassword, user.Password) {
		return nil, fmt.Errorf("原密码错误")
	}

	// 4. 验证新密码强度
	if err := utils.ValidatePassword(req.NewPassword); err != nil {
		return nil, err
	}

	// 5. 加密新密码
	hashedPassword, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %w", err)
	}

	// 6. 更新密码
	if err := s.userRepo.UpdatePassword(userID, hashedPassword); err != nil {
		return nil, fmt.Errorf("更新密码失败: %w", err)
	}

	// 7. 重新获取用户信息
	user, err = s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	return user.ToResponse(), nil
}

// SendForgotPasswordCode 发送忘记密码验证码
func (s *UserService) SendForgotPasswordCode(req *model.UserForgotPasswordRequest) error {
	// 1. 验证手机号格式
	if !utils.ValidatePhone(req.Phone) {
		return fmt.Errorf("手机号格式不正确")
	}

	// 2. 验证用户是否存在
	user, err := s.userRepo.GetByPhone(req.Phone)
	if err != nil {
		return fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return fmt.Errorf("手机号未注册")
	}

	// 3. 检查用户状态
	if user.IsFrozen() {
		return fmt.Errorf("账户已被冻结，无法重置密码")
	}

	// 4. 检查发送频率限制
	if err := s.checkCodeSendLimit(req.Phone); err != nil {
		return err
	}

	// 5. 生成验证码
	code := utils.GenerateVerificationCode()

	// 6. 存储验证码到Redis（5分钟过期）
	if s.redis != nil {
		ctx := context.Background()
		codeKey := s.getForgotPasswordCodeCacheKey(req.Phone)
		if err := s.redis.Set(ctx, codeKey, code, 5*time.Minute).Err(); err != nil {
			return fmt.Errorf("存储验证码失败: %w", err)
		}

		// 7. 设置发送频率限制（60秒内不能重复发送）
		limitKey := s.getCodeLimitKey(req.Phone)
		if err := s.redis.Set(ctx, limitKey, "1", time.Minute).Err(); err != nil {
			return fmt.Errorf("设置发送限制失败: %w", err)
		}
	} else {
		fmt.Printf("⚠️  Redis不可用，忘记密码验证码: %s\n", code)
	}

	// 8. 发送短信
	if err := s.sendSMS(req.Phone, code); err != nil {
		return fmt.Errorf("发送短信失败: %w", err)
	}

	return nil
}

// ResetPassword 用户重置密码（使用验证码）
func (s *UserService) ResetPassword(req *model.UserResetPasswordRequest) (*model.UserResponse, error) {
	// 1. 验证手机号格式
	if !utils.ValidatePhone(req.Phone) {
		return nil, fmt.Errorf("手机号格式不正确")
	}

	// 2. 验证用户是否存在
	user, err := s.userRepo.GetByPhone(req.Phone)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("手机号未注册")
	}

	// 3. 检查用户状态
	if user.IsFrozen() {
		return nil, fmt.Errorf("账户已被冻结，无法重置密码")
	}

	// 4. 验证验证码
	if err := s.verifyForgotPasswordCode(req.Phone, req.Code); err != nil {
		return nil, err
	}

	// 5. 验证新密码强度
	if err := utils.ValidatePassword(req.NewPassword); err != nil {
		return nil, err
	}

	// 6. 加密新密码
	hashedPassword, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %w", err)
	}

	// 7. 更新密码
	if err := s.userRepo.UpdatePasswordByPhone(req.Phone, hashedPassword); err != nil {
		return nil, fmt.Errorf("更新密码失败: %w", err)
	}

	// 8. 删除验证码缓存
	s.deleteForgotPasswordCodeCache(req.Phone)

	// 9. 重新获取用户信息
	user, err = s.userRepo.GetByPhone(req.Phone)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	return user.ToResponse(), nil
}

// verifyForgotPasswordCode 验证忘记密码验证码
func (s *UserService) verifyForgotPasswordCode(phone, code string) error {
	if s.redis == nil {
		// Redis不可用时，使用固定验证码进行测试
		if code == "123456" {
			return nil
		}
		return fmt.Errorf("验证码错误")
	}

	ctx := context.Background()
	codeKey := s.getForgotPasswordCodeCacheKey(phone)

	cachedCode, err := s.redis.Get(ctx, codeKey).Result()
	if err != nil {
		if err == redis.Nil {
			return fmt.Errorf("验证码已过期或不存在")
		}
		return fmt.Errorf("验证码验证失败: %w", err)
	}

	if cachedCode != code {
		return fmt.Errorf("验证码错误")
	}

	return nil
}

// deleteForgotPasswordCodeCache 删除忘记密码验证码缓存
func (s *UserService) deleteForgotPasswordCodeCache(phone string) {
	if s.redis == nil {
		return
	}

	ctx := context.Background()
	codeKey := s.getForgotPasswordCodeCacheKey(phone)
	s.redis.Del(ctx, codeKey)
}

// getForgotPasswordCodeCacheKey 获取忘记密码验证码缓存键
func (s *UserService) getForgotPasswordCodeCacheKey(phone string) string {
	return fmt.Sprintf("forgot_password_code:%s", phone)
}
