package service

import (
	"fmt"
	"solve_api/internal/config"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"solve_api/internal/utils"
)

type ApplicationService struct {
	appRepo  *repository.ApplicationRepository
	userRepo *repository.UserRepository
}

// NewApplicationService 创建应用服务实例
func NewApplicationService(appRepo *repository.ApplicationRepository, userRepo *repository.UserRepository) *ApplicationService {
	return &ApplicationService{
		appRepo:  appRepo,
		userRepo: userRepo,
	}
}

// Create 创建应用
func (s *ApplicationService) Create(userID uint, req *model.ApplicationCreateRequest) (*model.ApplicationResponse, error) {
	// 1. 验证用户是否存在
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 2. 检查用户状态
	if user.IsFrozen() {
		return nil, fmt.Errorf("账户已被冻结，无法创建应用")
	}

	// 3. 检查用户应用数量限制
	count, err := s.appRepo.CountByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户应用数量失败: %w", err)
	}

	maxApps := config.GlobalConfig.App.MaxAppsPerUser
	if count >= int64(maxApps) {
		return nil, fmt.Errorf("每个用户最多只能创建%d个应用", maxApps)
	}

	// 4. 生成唯一的AppKey
	var appKey string
	for {
		appKey = utils.GenerateAppKey()
		exists, err := s.appRepo.ExistsByAppKey(appKey)
		if err != nil {
			return nil, fmt.Errorf("检查AppKey唯一性失败: %w", err)
		}
		if !exists {
			break
		}
	}

	// 5. 生成SecretKey
	secretKey := utils.GenerateSecretKey()

	// 6. 创建应用
	app := &model.Application{
		UserID:    userID,
		Name:      req.Name,
		Type:      req.Type,
		AppKey:    appKey,
		SecretKey: secretKey,
		Status:    model.ApplicationStatusNormal,
	}

	if err := s.appRepo.Create(app); err != nil {
		return nil, fmt.Errorf("创建应用失败: %w", err)
	}

	return app.ToResponse(), nil
}

// GetList 获取用户应用列表
func (s *ApplicationService) GetList(userID uint) ([]*model.ApplicationListResponse, error) {
	// 1. 验证用户是否存在
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 2. 获取用户应用列表
	apps, err := s.appRepo.GetByUserID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询应用列表失败: %w", err)
	}

	// 3. 转换为响应格式
	var result []*model.ApplicationListResponse
	for _, app := range apps {
		result = append(result, app.ToListResponse())
	}

	return result, nil
}

// GetByID 获取应用详情
func (s *ApplicationService) GetByID(userID, appID uint) (*model.ApplicationResponse, error) {
	// 1. 验证用户是否存在
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 2. 获取应用（确保用户只能访问自己的应用）
	app, err := s.appRepo.GetByUserIDAndID(userID, appID)
	if err != nil {
		return nil, fmt.Errorf("查询应用失败: %w", err)
	}
	if app == nil {
		return nil, fmt.Errorf("应用不存在")
	}

	return app.ToResponse(), nil
}

// Update 更新应用信息
func (s *ApplicationService) Update(userID, appID uint, req *model.ApplicationUpdateRequest) (*model.ApplicationResponse, error) {
	// 1. 验证用户是否存在
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 2. 检查用户状态
	if user.IsFrozen() {
		return nil, fmt.Errorf("账户已被冻结，无法修改应用")
	}

	// 3. 获取应用（确保用户只能修改自己的应用）
	app, err := s.appRepo.GetByUserIDAndID(userID, appID)
	if err != nil {
		return nil, fmt.Errorf("查询应用失败: %w", err)
	}
	if app == nil {
		return nil, fmt.Errorf("应用不存在")
	}

	// 4. 更新应用名称
	app.Name = req.Name
	if err := s.appRepo.Update(app); err != nil {
		return nil, fmt.Errorf("更新应用失败: %w", err)
	}

	return app.ToResponse(), nil
}

// ResetSecretKey 重置SecretKey
func (s *ApplicationService) ResetSecretKey(userID, appID uint) (*model.ApplicationResponse, error) {
	// 1. 验证用户是否存在
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 2. 检查用户状态
	if user.IsFrozen() {
		return nil, fmt.Errorf("账户已被冻结，无法重置密钥")
	}

	// 3. 获取应用（确保用户只能重置自己的应用）
	app, err := s.appRepo.GetByUserIDAndID(userID, appID)
	if err != nil {
		return nil, fmt.Errorf("查询应用失败: %w", err)
	}
	if app == nil {
		return nil, fmt.Errorf("应用不存在")
	}

	// 4. 生成新的SecretKey
	newSecretKey := utils.GenerateSecretKey()
	app.SecretKey = newSecretKey

	if err := s.appRepo.Update(app); err != nil {
		return nil, fmt.Errorf("重置密钥失败: %w", err)
	}

	return app.ToResponse(), nil
}

// UpdateStatus 更新应用状态
func (s *ApplicationService) UpdateStatus(userID, appID uint, req *model.ApplicationStatusUpdateRequest) (*model.ApplicationResponse, error) {
	// 1. 验证用户是否存在
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 2. 检查用户状态
	if user.IsFrozen() {
		return nil, fmt.Errorf("账户已被冻结，无法修改应用状态")
	}

	// 3. 获取应用（确保用户只能修改自己的应用）
	app, err := s.appRepo.GetByUserIDAndID(userID, appID)
	if err != nil {
		return nil, fmt.Errorf("查询应用失败: %w", err)
	}
	if app == nil {
		return nil, fmt.Errorf("应用不存在")
	}

	// 4. 更新应用状态
	app.Status = req.Status
	if err := s.appRepo.Update(app); err != nil {
		return nil, fmt.Errorf("更新应用状态失败: %w", err)
	}

	return app.ToResponse(), nil
}

// GetByAppKey 根据AppKey获取应用（用于API认证）
func (s *ApplicationService) GetByAppKey(appKey string) (*model.Application, error) {
	app, err := s.appRepo.GetByAppKey(appKey)
	if err != nil {
		return nil, fmt.Errorf("查询应用失败: %w", err)
	}
	if app == nil {
		return nil, fmt.Errorf("应用不存在")
	}
	return app, nil
}
