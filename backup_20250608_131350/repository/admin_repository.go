package repository

import (
	"errors"
	"solve_api/internal/model"

	"gorm.io/gorm"
)

type AdminRepository struct {
	db *gorm.DB
}

// NewAdminRepository 创建管理员仓库实例
func NewAdminRepository(db *gorm.DB) *AdminRepository {
	return &AdminRepository{db: db}
}

// Create 创建管理员
func (r *AdminRepository) Create(admin *model.Admin) error {
	return r.db.Create(admin).Error
}

// GetByID 根据ID获取管理员
func (r *AdminRepository) GetByID(id uint) (*model.Admin, error) {
	var admin model.Admin
	err := r.db.First(&admin, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &admin, nil
}

// GetByPhone 根据手机号获取管理员
func (r *AdminRepository) GetByPhone(phone string) (*model.Admin, error) {
	var admin model.Admin
	err := r.db.Where("phone = ?", phone).First(&admin).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &admin, nil
}

// Update 更新管理员信息
func (r *AdminRepository) Update(admin *model.Admin) error {
	return r.db.Save(admin).Error
}

// UpdatePassword 更新管理员密码
func (r *AdminRepository) UpdatePassword(adminID uint, hashedPassword string) error {
	return r.db.Model(&model.Admin{}).Where("id = ?", adminID).Update("password", hashedPassword).Error
}

// Delete 软删除管理员
func (r *AdminRepository) Delete(adminID uint) error {
	return r.db.Delete(&model.Admin{}, adminID).Error
}

// List 获取管理员列表（分页）
func (r *AdminRepository) List(offset, limit int) ([]*model.Admin, int64, error) {
	var admins []*model.Admin
	var total int64

	// 获取总数
	if err := r.db.Model(&model.Admin{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Offset(offset).Limit(limit).Order("created_at DESC").Find(&admins).Error
	if err != nil {
		return nil, 0, err
	}

	return admins, total, nil
}

// ExistsByPhone 检查手机号是否已存在
func (r *AdminRepository) ExistsByPhone(phone string) (bool, error) {
	var count int64
	err := r.db.Model(&model.Admin{}).Where("phone = ?", phone).Count(&count).Error
	return count > 0, err
}

// GetAdminCount 获取管理员总数
func (r *AdminRepository) GetAdminCount() (int64, error) {
	var count int64
	err := r.db.Model(&model.Admin{}).Count(&count).Error
	return count, err
}

// GetAdminCountByRole 根据角色统计管理员数量
func (r *AdminRepository) GetAdminCountByRole(role int) (int64, error) {
	var count int64
	err := r.db.Model(&model.Admin{}).Where("role = ?", role).Count(&count).Error
	return count, err
}
