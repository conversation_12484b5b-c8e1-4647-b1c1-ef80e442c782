package repository

import (
	"errors"
	"fmt"
	"solve_api/internal/model"
	"time"

	"gorm.io/gorm"
)

type QuestionRepository struct {
	db *gorm.DB
}

// NewQuestionRepository 创建题目仓库实例
func NewQuestionRepository(db *gorm.DB) *QuestionRepository {
	return &QuestionRepository{db: db}
}

// Create 创建题目
func (r *QuestionRepository) Create(question *model.Question) error {
	createStart := time.Now()
	fmt.Printf("💾 [MySQL性能分析] 开始Create题目操作\n")

	// 只有在缓存键为空时才生成缓存键
	cacheKeyStart := time.Now()
	if question.CacheKey == "" {
		fmt.Printf("🔑 [MySQL性能分析] 开始生成缓存键\n")
		question.GenerateCacheKey()
		cacheKeyDuration := time.Since(cacheKeyStart)
		fmt.Printf("✅ [MySQL性能分析] 缓存键生成完成: %v, 缓存键: %s\n", cacheKeyDuration, question.CacheKey)
	} else {
		fmt.Printf("ℹ️ [MySQL性能分析] 使用现有缓存键: %s\n", question.CacheKey)
	}

	dbCreateStart := time.Now()
	fmt.Printf("📝 [MySQL性能分析] 开始数据库插入操作\n")
	err := r.db.Create(question).Error
	dbCreateDuration := time.Since(dbCreateStart)

	if err != nil {
		fmt.Printf("❌ [MySQL性能分析] 数据库插入失败: %v, 耗时: %v\n", err, dbCreateDuration)
	} else {
		fmt.Printf("✅ [MySQL性能分析] 数据库插入完成: %v, 题目ID: %d\n", dbCreateDuration, question.ID)
	}

	totalDuration := time.Since(createStart)
	fmt.Printf("🏁 [MySQL性能分析] Create操作完成: %v\n", totalDuration)

	return err
}

// GetByID 根据ID获取题目
func (r *QuestionRepository) GetByID(id uint) (*model.Question, error) {
	var question model.Question
	err := r.db.First(&question, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &question, nil
}

// GetByCacheKey 根据缓存键获取题目
func (r *QuestionRepository) GetByCacheKey(cacheKey string) (*model.Question, error) {
	var question model.Question
	err := r.db.Where("cache_key = ?", cacheKey).First(&question).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &question, nil
}

// GetByHash 根据哈希值获取题目（已废弃，使用GetByCacheKey）
// Deprecated: 使用GetByCacheKey替代
func (r *QuestionRepository) GetByHash(hash string) (*model.Question, error) {
	// 为了向后兼容，将hash作为cache_key查询
	return r.GetByCacheKey(hash)
}

// GetByAssociates 根据关联键获取相关题目
func (r *QuestionRepository) GetByAssociates(associates string) ([]*model.Question, error) {
	var questions []*model.Question
	err := r.db.Where("associates LIKE ?", "%"+associates+"%").Find(&questions).Error
	if err != nil {
		return nil, err
	}
	return questions, nil
}

// UpdateQuestion 更新题目（支持新架构字段）
func (r *QuestionRepository) UpdateQuestion(id uint, updates map[string]interface{}) error {
	// 过滤不允许修改的字段
	forbiddenFields := []string{"id", "cache_key", "response", "raw_qwen", "raw_deepseek", "created_at"}

	filteredUpdates := make(map[string]interface{})
	for key, value := range updates {
		allowed := true
		for _, forbidden := range forbiddenFields {
			if key == forbidden {
				allowed = false
				break
			}
		}
		if allowed {
			filteredUpdates[key] = value
		}
	}

	return r.db.Model(&model.Question{}).Where("id = ?", id).Updates(filteredUpdates).Error
}

// CreateManagementQuestion 创建题库管理题目
func (r *QuestionRepository) CreateManagementQuestion(req *model.QuestionManagementRequest) (*model.Question, error) {
	question := &model.Question{
		QuestionType:   req.QuestionType,
		QuestionText:   req.QuestionText,
		QuestionImgRaw: req.QuestionImgRaw,
		OptionsA:       req.OptionsA,
		OptionsB:       req.OptionsB,
		OptionsC:       req.OptionsC,
		OptionsD:       req.OptionsD,
		OptionsY:       req.OptionsY,
		OptionsN:       req.OptionsN,
		Answer:         req.Answer,
		Analysis:       req.Analysis,
		Response:       0,
		SourceModel:    "manual",
	}

	// 生成缓存键
	preprocessed := &model.PreprocessedQuestion{
		QuestionType: req.QuestionType,
		QuestionText: req.QuestionText,
		OptionsA:     req.OptionsA,
		OptionsB:     req.OptionsB,
		OptionsC:     req.OptionsC,
		OptionsD:     req.OptionsD,
		OptionsY:     req.OptionsY,
		OptionsN:     req.OptionsN,
	}
	question.CacheKey = model.GenerateCacheKeyFromPreprocessed(preprocessed)

	err := r.db.Create(question).Error
	if err != nil {
		return nil, err
	}

	return question, nil
}

// IncrementResponse 增加响应次数
func (r *QuestionRepository) IncrementResponse(id uint) error {
	return r.db.Model(&model.Question{}).Where("id = ?", id).UpdateColumn("response", gorm.Expr("response + 1")).Error
}

// Delete 删除题目（软删除）
func (r *QuestionRepository) Delete(id uint) error {
	return r.db.Delete(&model.Question{}, id).Error
}

// GetWithAssociates 获取题目及其关联题目
func (r *QuestionRepository) GetWithAssociates(cacheKey string) ([]*model.Question, error) {
	getAssociatesStart := time.Now()
	fmt.Printf("🗄️ [MySQL性能分析] 开始GetWithAssociates - 缓存键: %s\n", cacheKey)

	// 首先获取主题目
	mainQueryStart := time.Now()
	fmt.Printf("📋 [MySQL性能分析] 开始查询主题目\n")
	mainQuestion, err := r.GetByCacheKey(cacheKey)
	if err != nil || mainQuestion == nil {
		mainQueryDuration := time.Since(mainQueryStart)
		fmt.Printf("❌ [MySQL性能分析] 主题目查询失败: %v, 耗时: %v\n", err, mainQueryDuration)
		return nil, err
	}
	mainQueryDuration := time.Since(mainQueryStart)
	fmt.Printf("✅ [MySQL性能分析] 主题目查询完成: %v\n", mainQueryDuration)

	var allQuestions []*model.Question
	allQuestions = append(allQuestions, mainQuestion)

	// 移除关联功能，直接返回主题目
	// 关联功能已被移除以简化系统

	totalDuration := time.Since(getAssociatesStart)
	fmt.Printf("🏁 [MySQL性能分析] GetWithAssociates完成: %v, 总题目数: %d\n", totalDuration, len(allQuestions))

	return allQuestions, nil
}

// Update 更新题目
func (r *QuestionRepository) Update(question *model.Question) error {
	return r.db.Save(question).Error
}

// List 获取题目列表
func (r *QuestionRepository) List(offset, limit int) ([]*model.Question, int64, error) {
	var questions []*model.Question
	var total int64

	// 获取总数
	if err := r.db.Model(&model.Question{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Offset(offset).Limit(limit).Order("created_at DESC").Find(&questions).Error
	if err != nil {
		return nil, 0, err
	}

	return questions, total, nil
}

// GetBySubject 根据学科获取题目列表
func (r *QuestionRepository) GetBySubject(subject string, offset, limit int) ([]*model.Question, int64, error) {
	var questions []*model.Question
	var total int64

	// 获取总数
	if err := r.db.Model(&model.Question{}).Where("subject = ?", subject).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Where("subject = ?", subject).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&questions).Error
	if err != nil {
		return nil, 0, err
	}

	return questions, total, nil
}

// GetByGrade 根据年级获取题目列表
func (r *QuestionRepository) GetByGrade(grade string, offset, limit int) ([]*model.Question, int64, error) {
	var questions []*model.Question
	var total int64

	// 获取总数
	if err := r.db.Model(&model.Question{}).Where("grade = ?", grade).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Where("grade = ?", grade).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&questions).Error
	if err != nil {
		return nil, 0, err
	}

	return questions, total, nil
}

// GetByDifficulty 根据难度获取题目列表
func (r *QuestionRepository) GetByDifficulty(difficulty int, offset, limit int) ([]*model.Question, int64, error) {
	var questions []*model.Question
	var total int64

	// 获取总数
	if err := r.db.Model(&model.Question{}).Where("difficulty = ?", difficulty).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Where("difficulty = ?", difficulty).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&questions).Error
	if err != nil {
		return nil, 0, err
	}

	return questions, total, nil
}

// Search 搜索题目
func (r *QuestionRepository) Search(keyword string, offset, limit int) ([]*model.Question, int64, error) {
	var questions []*model.Question
	var total int64

	searchPattern := "%" + keyword + "%"

	// 获取总数
	if err := r.db.Model(&model.Question{}).
		Where("question_text LIKE ? OR analysis LIKE ? OR answer LIKE ?", searchPattern, searchPattern, searchPattern).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Where("question_text LIKE ? OR analysis LIKE ? OR answer LIKE ?", searchPattern, searchPattern, searchPattern).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&questions).Error
	if err != nil {
		return nil, 0, err
	}

	return questions, total, nil
}

// GetTotalCount 获取题目总数
func (r *QuestionRepository) GetTotalCount() (int64, error) {
	var count int64
	err := r.db.Model(&model.Question{}).Count(&count).Error
	return count, err
}

// GetCountBySubject 获取各学科题目数量
func (r *QuestionRepository) GetCountBySubject() (map[string]int64, error) {
	type SubjectCount struct {
		Subject string `json:"subject"`
		Count   int64  `json:"count"`
	}

	var results []SubjectCount
	err := r.db.Model(&model.Question{}).
		Select("subject, COUNT(*) as count").
		Group("subject").
		Find(&results).Error
	if err != nil {
		return nil, err
	}

	countMap := make(map[string]int64)
	for _, result := range results {
		countMap[result.Subject] = result.Count
	}

	return countMap, nil
}

// GetCountByGrade 获取各年级题目数量
func (r *QuestionRepository) GetCountByGrade() (map[string]int64, error) {
	type GradeCount struct {
		Grade string `json:"grade"`
		Count int64  `json:"count"`
	}

	var results []GradeCount
	err := r.db.Model(&model.Question{}).
		Select("grade, COUNT(*) as count").
		Group("grade").
		Find(&results).Error
	if err != nil {
		return nil, err
	}

	countMap := make(map[string]int64)
	for _, result := range results {
		countMap[result.Grade] = result.Count
	}

	return countMap, nil
}

// GetCountByDifficulty 获取各难度题目数量
func (r *QuestionRepository) GetCountByDifficulty() (map[int]int64, error) {
	type DifficultyCount struct {
		Difficulty int   `json:"difficulty"`
		Count      int64 `json:"count"`
	}

	var results []DifficultyCount
	err := r.db.Model(&model.Question{}).
		Select("difficulty, COUNT(*) as count").
		Group("difficulty").
		Find(&results).Error
	if err != nil {
		return nil, err
	}

	countMap := make(map[int]int64)
	for _, result := range results {
		countMap[result.Difficulty] = result.Count
	}

	return countMap, nil
}

// ExistsByHash 检查哈希值是否已存在（已废弃，使用ExistsByCacheKey）
// Deprecated: 使用ExistsByCacheKey替代
func (r *QuestionRepository) ExistsByHash(hash string) (bool, error) {
	return r.ExistsByCacheKey(hash)
}

// ExistsByCacheKey 检查缓存键是否已存在
func (r *QuestionRepository) ExistsByCacheKey(cacheKey string) (bool, error) {
	var count int64
	err := r.db.Model(&model.Question{}).Where("cache_key = ?", cacheKey).Count(&count).Error
	return count > 0, err
}
