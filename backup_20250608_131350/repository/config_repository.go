package repository

import (
	"errors"
	"solve_api/internal/model"

	"gorm.io/gorm"
)

type ConfigRepository struct {
	db *gorm.DB
}

// NewConfigRepository 创建配置仓库实例
func NewConfigRepository(db *gorm.DB) *ConfigRepository {
	return &ConfigRepository{db: db}
}

// Create 创建配置
func (r *ConfigRepository) Create(config *model.SystemConfig) error {
	return r.db.Create(config).Error
}

// GetByKey 根据键获取配置
func (r *ConfigRepository) GetByKey(key string) (*model.SystemConfig, error) {
	var config model.SystemConfig
	err := r.db.Where("`key` = ?", key).First(&config).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &config, nil
}

// GetByID 根据ID获取配置
func (r *ConfigRepository) GetByID(id uint) (*model.SystemConfig, error) {
	var config model.SystemConfig
	err := r.db.First(&config, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &config, nil
}

// Update 更新配置
func (r *ConfigRepository) Update(config *model.SystemConfig) error {
	return r.db.Save(config).Error
}

// UpdateValue 更新配置值
func (r *ConfigRepository) UpdateValue(key, value string) error {
	return r.db.Model(&model.SystemConfig{}).Where("`key` = ?", key).Update("value", value).Error
}

// DeleteByID 根据ID删除配置
func (r *ConfigRepository) DeleteByID(id uint) error {
	return r.db.Delete(&model.SystemConfig{}, id).Error
}

// List 获取配置列表
func (r *ConfigRepository) List() ([]*model.SystemConfig, error) {
	var configs []*model.SystemConfig
	err := r.db.Find(&configs).Error
	return configs, err
}

// ExistsByKey 检查键是否已存在
func (r *ConfigRepository) ExistsByKey(key string) (bool, error) {
	var count int64
	err := r.db.Model(&model.SystemConfig{}).Where("`key` = ?", key).Count(&count).Error
	return count > 0, err
}

// GetValueByKey 根据键获取配置值
func (r *ConfigRepository) GetValueByKey(key string) (string, error) {
	config, err := r.GetByKey(key)
	if err != nil {
		return "", err
	}
	if config == nil {
		return "", nil
	}
	return config.Value, nil
}

// SetValue 设置配置值（如果不存在则创建）
func (r *ConfigRepository) SetValue(key, value, description string) error {
	config, err := r.GetByKey(key)
	if err != nil {
		return err
	}

	if config == nil {
		// 创建新配置
		newConfig := &model.SystemConfig{
			Key:         key,
			Value:       value,
			Description: description,
		}
		return r.Create(newConfig)
	} else {
		// 更新现有配置
		config.Value = value
		if description != "" {
			config.Description = description
		}
		return r.Update(config)
	}
}

// GetValue 根据键获取配置值
func (r *ConfigRepository) GetValue(key string) (string, error) {
	return r.GetValueByKey(key)
}

// GetAll 获取所有配置
func (r *ConfigRepository) GetAll() ([]*model.SystemConfig, error) {
	return r.List()
}

// DeleteByKey 根据键删除配置
func (r *ConfigRepository) DeleteByKey(key string) error {
	return r.db.Where("`key` = ?", key).Delete(&model.SystemConfig{}).Error
}
