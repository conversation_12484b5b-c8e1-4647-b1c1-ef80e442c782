package middleware

import (
	"solve_api/internal/database"
	"solve_api/internal/repository"
	"solve_api/internal/utils"
	"strings"

	"github.com/gin-gonic/gin"
)

// BalanceCheck 余额检查中间件
func BalanceCheck() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过不需要扣费的接口
		if strings.HasPrefix(c.Request.URL.Path, "/health") ||
			strings.HasPrefix(c.Request.URL.Path, "/api/v1/admin") ||
			strings.HasPrefix(c.Request.URL.Path, "/api/v1/user") {
			c.Next()
			return
		}

		// 获取用户信息
		user, exists := GetUserFromContext(c)
		if !exists {
			utils.Unauthorized(c, "用户信息不存在")
			c.Abort()
			return
		}

		// 获取应用类型
		appType, exists := GetAppTypeFromContext(c)
		if !exists {
			utils.ServerError(c, "应用类型不存在")
			c.Abort()
			return
		}

		// 获取服务价格
		priceRepo := repository.NewPriceConfigRepository(database.GetDB())
		price, err := priceRepo.GetPrice(appType, user.ID)
		if err != nil {
			utils.ServerError(c, "获取服务价格失败")
			c.Abort()
			return
		}

		// 检查余额是否足够
		if user.Balance < price {
			utils.PaymentRequired(c, "账户余额不足，请先充值")
			c.Abort()
			return
		}

		// 将价格信息存储到上下文中，供后续扣费使用
		c.Set("service_price", price)

		c.Next()
	}
}

// GetServicePriceFromContext 从上下文获取服务价格
func GetServicePriceFromContext(c *gin.Context) (float64, bool) {
	price, exists := c.Get("service_price")
	if !exists {
		return 0, false
	}
	return price.(float64), true
}
