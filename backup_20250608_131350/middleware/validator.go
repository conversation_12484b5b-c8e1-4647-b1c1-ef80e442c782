package middleware

import (
	"solve_api/internal/model"
	"solve_api/internal/utils"
	"strings"

	"github.com/gin-gonic/gin"
)

// ParamValidator 参数验证中间件
func ParamValidator() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过不需要验证的接口
		if strings.HasPrefix(c.Request.URL.Path, "/health") ||
			strings.HasPrefix(c.Request.URL.Path, "/api/v1/admin") ||
			strings.HasPrefix(c.Request.URL.Path, "/api/v1/user") {
			c.Next()
			return
		}

		// 获取应用类型
		appType, exists := GetAppTypeFromContext(c)
		if !exists {
			utils.ServerError(c, "应用类型不存在")
			c.Abort()
			return
		}

		// 根据应用类型验证参数
		switch appType {
		case model.ApplicationTypePhotoSearch:
			if err := validatePhotoSearchParams(c); err != nil {
				utils.BadRequest(c, err.Error())
				c.Abort()
				return
			}
		default:
			utils.BadRequest(c, "不支持的应用类型")
			c.Abort()
			return
		}

		c.Next()
	}
}

// validatePhotoSearchParams 验证拍照搜题参数
func validatePhotoSearchParams(c *gin.Context) error {
	// 只对搜题接口进行验证
	if !strings.HasSuffix(c.Request.URL.Path, "/search") {
		return nil
	}

	// 跳过参数验证，让handler自己处理
	// 避免重复读取请求体导致EOF错误
	return nil
}

// isValidImageURL 验证图片URL是否有效
func isValidImageURL(url string) bool {
	// 检查URL是否以支持的图片格式结尾
	supportedFormats := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"}

	urlLower := strings.ToLower(url)
	for _, format := range supportedFormats {
		if strings.HasSuffix(urlLower, format) {
			return true
		}
	}

	// 检查是否包含常见的图片服务域名
	imageServices := []string{
		"qiniu.com",
		"aliyuncs.com",
		"amazonaws.com",
		"cloudfront.net",
		"imgur.com",
		"photobucket.com",
		"igmdns.com", // 添加测试域名
	}

	for _, service := range imageServices {
		if strings.Contains(urlLower, service) {
			return true
		}
	}

	// 如果URL包含图片格式，即使不在域名白名单中也认为有效
	for _, format := range supportedFormats {
		if strings.Contains(urlLower, format) {
			return true
		}
	}

	return false
}

// GetImageURLFromContext 从上下文获取图片URL
func GetImageURLFromContext(c *gin.Context) (string, bool) {
	imageURL, exists := c.Get("image_url")
	if !exists {
		return "", false
	}
	return imageURL.(string), true
}
