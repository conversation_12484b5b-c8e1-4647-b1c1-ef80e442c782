package cache

import (
	"fmt"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"sync"
	"time"

	"gorm.io/gorm"
)

// ConfigCache 配置缓存管理器
type ConfigCache struct {
	// 模型配置缓存
	modelConfigs map[string]*model.ModelConfig
	modelMutex   sync.RWMutex

	// 价格配置缓存
	priceConfigs map[string]*model.PriceConfig
	priceMutex   sync.RWMutex

	// 系统配置缓存
	systemConfigs map[string]string
	systemMutex   sync.RWMutex

	// Repository实例
	modelConfigRepo  *repository.ModelConfigRepository
	priceConfigRepo  *repository.PriceConfigRepository
	systemConfigRepo *repository.ConfigRepository

	// 缓存更新时间
	lastUpdate  map[string]time.Time
	updateMutex sync.RWMutex

	// 缓存TTL
	cacheTTL time.Duration
}

// NewConfigCache 创建配置缓存管理器
func NewConfigCache(db *gorm.DB) *ConfigCache {
	cache := &ConfigCache{
		modelConfigs:     make(map[string]*model.ModelConfig),
		priceConfigs:     make(map[string]*model.PriceConfig),
		systemConfigs:    make(map[string]string),
		lastUpdate:       make(map[string]time.Time),
		cacheTTL:         5 * time.Minute, // 配置缓存5分钟
		modelConfigRepo:  repository.NewModelConfigRepository(db),
		priceConfigRepo:  repository.NewPriceConfigRepository(db),
		systemConfigRepo: repository.NewConfigRepository(db),
	}

	// 预加载配置
	cache.preloadConfigs()

	return cache
}

// GetModelConfig 获取模型配置（带缓存）
func (c *ConfigCache) GetModelConfig(name string) (*model.ModelConfig, error) {
	c.modelMutex.RLock()

	// 检查缓存是否过期
	if config, exists := c.modelConfigs[name]; exists && !c.isExpired("model_"+name) {
		c.modelMutex.RUnlock()
		return config, nil
	}
	c.modelMutex.RUnlock()

	// 缓存未命中或过期，从数据库获取
	return c.refreshModelConfig(name)
}

// GetEnabledModelConfig 获取启用的模型配置
func (c *ConfigCache) GetEnabledModelConfig(name string) (*model.ModelConfig, error) {
	config, err := c.GetModelConfig(name)
	if err != nil {
		return nil, err
	}

	if config == nil {
		return nil, fmt.Errorf("模型配置不存在: %s", name)
	}

	if config.Status != model.ModelConfigStatusEnabled {
		return nil, fmt.Errorf("模型未启用: %s", name)
	}

	return config, nil
}

// GetPriceConfig 获取价格配置（带缓存）
func (c *ConfigCache) GetPriceConfig(userID uint, appType int) (*model.PriceConfig, error) {
	key := fmt.Sprintf("%d_%d", userID, appType)

	c.priceMutex.RLock()
	if config, exists := c.priceConfigs[key]; exists && !c.isExpired("price_"+key) {
		c.priceMutex.RUnlock()
		return config, nil
	}
	c.priceMutex.RUnlock()

	// 缓存未命中或过期，从数据库获取
	return c.refreshPriceConfig(userID, appType)
}

// GetPrice 获取服务价格
func (c *ConfigCache) GetPrice(userID uint, appType int) (float64, error) {
	config, err := c.GetPriceConfig(userID, appType)
	if err != nil {
		return 0, err
	}

	if config == nil {
		// 返回默认价格
		return 0.01, nil
	}

	return config.Price, nil
}

// GetSystemConfig 获取系统配置（带缓存）
func (c *ConfigCache) GetSystemConfig(key string) (string, error) {
	c.systemMutex.RLock()
	if value, exists := c.systemConfigs[key]; exists && !c.isExpired("system_"+key) {
		c.systemMutex.RUnlock()
		return value, nil
	}
	c.systemMutex.RUnlock()

	// 缓存未命中或过期，从数据库获取
	return c.refreshSystemConfig(key)
}

// UpdateModelConfig 更新模型配置并刷新缓存
func (c *ConfigCache) UpdateModelConfig(name string, config *model.ModelConfig) error {
	// 更新数据库
	if err := c.modelConfigRepo.Update(config); err != nil {
		return err
	}

	// 更新缓存
	c.modelMutex.Lock()
	c.modelConfigs[name] = config
	c.setUpdateTime("model_" + name)
	c.modelMutex.Unlock()

	return nil
}

// UpdatePriceConfig 更新价格配置并刷新缓存
func (c *ConfigCache) UpdatePriceConfig(userID uint, appType int, config *model.PriceConfig) error {
	// 更新数据库
	if err := c.priceConfigRepo.Update(config); err != nil {
		return err
	}

	// 更新缓存
	key := fmt.Sprintf("%d_%d", userID, appType)
	c.priceMutex.Lock()
	c.priceConfigs[key] = config
	c.setUpdateTime("price_" + key)
	c.priceMutex.Unlock()

	return nil
}

// UpdateSystemConfig 更新系统配置并刷新缓存
func (c *ConfigCache) UpdateSystemConfig(key, value string) error {
	// 更新数据库
	if err := c.systemConfigRepo.SetValue(key, value, ""); err != nil {
		return err
	}

	// 更新缓存
	c.systemMutex.Lock()
	c.systemConfigs[key] = value
	c.setUpdateTime("system_" + key)
	c.systemMutex.Unlock()

	return nil
}

// InvalidateModelConfig 使模型配置缓存失效
func (c *ConfigCache) InvalidateModelConfig(name string) {
	c.modelMutex.Lock()
	delete(c.modelConfigs, name)
	c.deleteUpdateTime("model_" + name)
	c.modelMutex.Unlock()
}

// InvalidatePriceConfig 使价格配置缓存失效
func (c *ConfigCache) InvalidatePriceConfig(userID uint, appType int) {
	key := fmt.Sprintf("%d_%d", userID, appType)
	c.priceMutex.Lock()
	delete(c.priceConfigs, key)
	c.deleteUpdateTime("price_" + key)
	c.priceMutex.Unlock()
}

// InvalidateSystemConfig 使系统配置缓存失效
func (c *ConfigCache) InvalidateSystemConfig(key string) {
	c.systemMutex.Lock()
	delete(c.systemConfigs, key)
	c.deleteUpdateTime("system_" + key)
	c.systemMutex.Unlock()
}

// ClearAll 清空所有缓存
func (c *ConfigCache) ClearAll() {
	c.modelMutex.Lock()
	c.modelConfigs = make(map[string]*model.ModelConfig)
	c.modelMutex.Unlock()

	c.priceMutex.Lock()
	c.priceConfigs = make(map[string]*model.PriceConfig)
	c.priceMutex.Unlock()

	c.systemMutex.Lock()
	c.systemConfigs = make(map[string]string)
	c.systemMutex.Unlock()

	c.updateMutex.Lock()
	c.lastUpdate = make(map[string]time.Time)
	c.updateMutex.Unlock()
}

// GetStats 获取缓存统计信息
func (c *ConfigCache) GetStats() map[string]interface{} {
	c.modelMutex.RLock()
	modelCount := len(c.modelConfigs)
	c.modelMutex.RUnlock()

	c.priceMutex.RLock()
	priceCount := len(c.priceConfigs)
	c.priceMutex.RUnlock()

	c.systemMutex.RLock()
	systemCount := len(c.systemConfigs)
	c.systemMutex.RUnlock()

	return map[string]interface{}{
		"model_configs":  modelCount,
		"price_configs":  priceCount,
		"system_configs": systemCount,
		"cache_ttl":      c.cacheTTL.String(),
	}
}

// 内部方法

// preloadConfigs 预加载配置
func (c *ConfigCache) preloadConfigs() {
	// 预加载模型配置
	if configs, err := c.modelConfigRepo.GetAll(); err == nil {
		c.modelMutex.Lock()
		for _, config := range configs {
			c.modelConfigs[config.Name] = config
			c.setUpdateTime("model_" + config.Name)
		}
		c.modelMutex.Unlock()
	}

	// 预加载常用系统配置
	commonKeys := []string{"invite_code", "max_apps_per_user", "rate_limit"}
	for _, key := range commonKeys {
		if value, err := c.systemConfigRepo.GetValue(key); err == nil {
			c.systemMutex.Lock()
			c.systemConfigs[key] = value
			c.setUpdateTime("system_" + key)
			c.systemMutex.Unlock()
		}
	}
}

// refreshModelConfig 刷新模型配置
func (c *ConfigCache) refreshModelConfig(name string) (*model.ModelConfig, error) {
	config, err := c.modelConfigRepo.GetByName(name)
	if err != nil {
		return nil, err
	}

	c.modelMutex.Lock()
	if config != nil {
		c.modelConfigs[name] = config
	} else {
		delete(c.modelConfigs, name)
	}
	c.setUpdateTime("model_" + name)
	c.modelMutex.Unlock()

	return config, nil
}

// refreshPriceConfig 刷新价格配置
func (c *ConfigCache) refreshPriceConfig(userID uint, appType int) (*model.PriceConfig, error) {
	config, err := c.priceConfigRepo.GetPrice(appType, userID)
	if err != nil {
		return nil, err
	}

	key := fmt.Sprintf("%d_%d", userID, appType)

	// 创建价格配置对象
	priceConfig := &model.PriceConfig{
		UserID:      userID,
		ServiceType: appType,
		Price:       config,
	}

	c.priceMutex.Lock()
	c.priceConfigs[key] = priceConfig
	c.setUpdateTime("price_" + key)
	c.priceMutex.Unlock()

	return priceConfig, nil
}

// refreshSystemConfig 刷新系统配置
func (c *ConfigCache) refreshSystemConfig(key string) (string, error) {
	value, err := c.systemConfigRepo.GetValue(key)
	if err != nil {
		return "", err
	}

	c.systemMutex.Lock()
	c.systemConfigs[key] = value
	c.setUpdateTime("system_" + key)
	c.systemMutex.Unlock()

	return value, nil
}

// isExpired 检查缓存是否过期
func (c *ConfigCache) isExpired(key string) bool {
	c.updateMutex.RLock()
	updateTime, exists := c.lastUpdate[key]
	c.updateMutex.RUnlock()

	if !exists {
		return true
	}

	return time.Since(updateTime) > c.cacheTTL
}

// setUpdateTime 设置更新时间
func (c *ConfigCache) setUpdateTime(key string) {
	c.updateMutex.Lock()
	c.lastUpdate[key] = time.Now()
	c.updateMutex.Unlock()
}

// deleteUpdateTime 删除更新时间
func (c *ConfigCache) deleteUpdateTime(key string) {
	c.updateMutex.Lock()
	delete(c.lastUpdate, key)
	c.updateMutex.Unlock()
}
