package model

import (
	"time"

	"gorm.io/gorm"
)

// APILog API调用日志表
type APILog struct {
	ID            uint           `gorm:"primaryKey" json:"id"`
	UserID        uint           `gorm:"index;not null" json:"user_id"`
	AppID         uint           `gorm:"index;not null" json:"app_id"`
	Method        string         `gorm:"size:10;not null" json:"method"`
	Path          string         `gorm:"size:255;not null" json:"path"`
	UserAgent     string         `gorm:"size:500" json:"user_agent"`
	ClientIP      string         `gorm:"size:45" json:"client_ip"`
	StatusCode    int            `gorm:"not null" json:"status_code"`
	ResponseTime  int64          `gorm:"comment:'响应时间(毫秒)'" json:"response_time"`
	RequestSize   int64          `gorm:"comment:'请求大小(字节)'" json:"request_size"`
	ResponseSize  int64          `gorm:"comment:'响应大小(字节)'" json:"response_size"`
	ErrorMsg      string         `gorm:"size:1000" json:"error_msg"`
	ServiceType   int            `gorm:"comment:'服务类型'" json:"service_type"`
	Cost          float64        `gorm:"comment:'本次调用费用'" json:"cost"`
	ModelName     string         `gorm:"size:50;comment:'使用的模型名称'" json:"model_name"`
	ModelRequest  string         `gorm:"type:longtext;comment:'模型请求内容'" json:"model_request"`
	ModelResponse string         `gorm:"type:longtext;comment:'模型返回内容'" json:"model_response"`
	ModelCost     float64        `gorm:"type:decimal(10,4);default:0;comment:'模型调用费用(元)'" json:"model_cost"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (APILog) TableName() string {
	return "api_logs"
}

// APILogResponse API日志响应
type APILogResponse struct {
	ID            uint      `json:"id"`
	UserID        uint      `json:"user_id"`
	AppID         uint      `json:"app_id"`
	Method        string    `json:"method"`
	Path          string    `json:"path"`
	UserAgent     string    `json:"user_agent"`
	ClientIP      string    `json:"client_ip"`
	StatusCode    int       `json:"status_code"`
	ResponseTime  int64     `json:"response_time"`
	RequestSize   int64     `json:"request_size"`
	ResponseSize  int64     `json:"response_size"`
	ErrorMsg      string    `json:"error_msg"`
	ServiceType   int       `json:"service_type"`
	Cost          float64   `json:"cost"`
	ModelName     string    `json:"model_name"`
	ModelRequest  string    `json:"model_request,omitempty"`
	ModelResponse string    `json:"model_response,omitempty"`
	ModelCost     float64   `json:"model_cost"`
	CreatedAt     time.Time `json:"created_at"`
}

// ToResponse 转换为响应格式
func (a *APILog) ToResponse() *APILogResponse {
	return &APILogResponse{
		ID:            a.ID,
		UserID:        a.UserID,
		AppID:         a.AppID,
		Method:        a.Method,
		Path:          a.Path,
		UserAgent:     a.UserAgent,
		ClientIP:      a.ClientIP,
		StatusCode:    a.StatusCode,
		ResponseTime:  a.ResponseTime,
		RequestSize:   a.RequestSize,
		ResponseSize:  a.ResponseSize,
		ErrorMsg:      a.ErrorMsg,
		ServiceType:   a.ServiceType,
		Cost:          a.Cost,
		ModelName:     a.ModelName,
		ModelRequest:  a.ModelRequest,
		ModelResponse: a.ModelResponse,
		ModelCost:     a.ModelCost,
		CreatedAt:     a.CreatedAt,
	}
}

// SystemStats 系统统计表
type SystemStats struct {
	ID              uint      `gorm:"primaryKey" json:"id"`
	Date            string    `gorm:"uniqueIndex;size:10;not null;comment:'统计日期 YYYY-MM-DD'" json:"date"`
	TotalUsers      int64     `gorm:"comment:'总用户数'" json:"total_users"`
	ActiveUsers     int64     `gorm:"comment:'活跃用户数'" json:"active_users"`
	NewUsers        int64     `gorm:"comment:'新增用户数'" json:"new_users"`
	TotalApps       int64     `gorm:"comment:'总应用数'" json:"total_apps"`
	TotalAPICalls   int64     `gorm:"comment:'总API调用次数'" json:"total_api_calls"`
	TotalRevenue    float64   `gorm:"comment:'总收入'" json:"total_revenue"`
	TotalQuestions  int64     `gorm:"comment:'总题目数'" json:"total_questions"`
	NewQuestions    int64     `gorm:"comment:'新增题目数'" json:"new_questions"`
	CacheHitRate    float64   `gorm:"comment:'缓存命中率'" json:"cache_hit_rate"`
	AvgResponseTime float64   `gorm:"comment:'平均响应时间(毫秒)'" json:"avg_response_time"`
	ErrorRate       float64   `gorm:"comment:'错误率'" json:"error_rate"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// TableName 指定表名
func (SystemStats) TableName() string {
	return "system_stats"
}

// SystemStatsResponse 系统统计响应
type SystemStatsResponse struct {
	ID              uint      `json:"id"`
	Date            string    `json:"date"`
	TotalUsers      int64     `json:"total_users"`
	ActiveUsers     int64     `json:"active_users"`
	NewUsers        int64     `json:"new_users"`
	TotalApps       int64     `json:"total_apps"`
	TotalAPICalls   int64     `json:"total_api_calls"`
	TotalRevenue    float64   `json:"total_revenue"`
	TotalQuestions  int64     `json:"total_questions"`
	NewQuestions    int64     `json:"new_questions"`
	CacheHitRate    float64   `json:"cache_hit_rate"`
	AvgResponseTime float64   `json:"avg_response_time"`
	ErrorRate       float64   `json:"error_rate"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (s *SystemStats) ToResponse() *SystemStatsResponse {
	return &SystemStatsResponse{
		ID:              s.ID,
		Date:            s.Date,
		TotalUsers:      s.TotalUsers,
		ActiveUsers:     s.ActiveUsers,
		NewUsers:        s.NewUsers,
		TotalApps:       s.TotalApps,
		TotalAPICalls:   s.TotalAPICalls,
		TotalRevenue:    s.TotalRevenue,
		TotalQuestions:  s.TotalQuestions,
		NewQuestions:    s.NewQuestions,
		CacheHitRate:    s.CacheHitRate,
		AvgResponseTime: s.AvgResponseTime,
		ErrorRate:       s.ErrorRate,
		CreatedAt:       s.CreatedAt,
		UpdatedAt:       s.UpdatedAt,
	}
}

// UserStats 用户统计表
type UserStats struct {
	ID              uint      `gorm:"primaryKey" json:"id"`
	UserID          uint      `gorm:"index;not null" json:"user_id"`
	Date            string    `gorm:"index;size:10;not null;comment:'统计日期 YYYY-MM-DD'" json:"date"`
	APICalls        int64     `gorm:"comment:'API调用次数'" json:"api_calls"`
	SuccessCalls    int64     `gorm:"comment:'成功调用次数'" json:"success_calls"`
	ErrorCalls      int64     `gorm:"comment:'错误调用次数'" json:"error_calls"`
	TotalCost       float64   `gorm:"comment:'总费用'" json:"total_cost"`
	AvgResponseTime float64   `gorm:"comment:'平均响应时间(毫秒)'" json:"avg_response_time"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// TableName 指定表名
func (UserStats) TableName() string {
	return "user_stats"
}

// UserStatsResponse 用户统计响应
type UserStatsResponse struct {
	ID              uint      `json:"id"`
	UserID          uint      `json:"user_id"`
	Date            string    `json:"date"`
	APICalls        int64     `json:"api_calls"`
	SuccessCalls    int64     `json:"success_calls"`
	ErrorCalls      int64     `json:"error_calls"`
	TotalCost       float64   `json:"total_cost"`
	AvgResponseTime float64   `json:"avg_response_time"`
	SuccessRate     float64   `json:"success_rate"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (u *UserStats) ToResponse() *UserStatsResponse {
	successRate := float64(0)
	if u.APICalls > 0 {
		successRate = float64(u.SuccessCalls) / float64(u.APICalls) * 100
	}

	return &UserStatsResponse{
		ID:              u.ID,
		UserID:          u.UserID,
		Date:            u.Date,
		APICalls:        u.APICalls,
		SuccessCalls:    u.SuccessCalls,
		ErrorCalls:      u.ErrorCalls,
		TotalCost:       u.TotalCost,
		AvgResponseTime: u.AvgResponseTime,
		SuccessRate:     successRate,
		CreatedAt:       u.CreatedAt,
		UpdatedAt:       u.UpdatedAt,
	}
}

// GetTodayDateString 获取今天的日期字符串
func GetTodayDateString() string {
	return time.Now().Format("2006-01-02")
}

// GetYesterdayDateString 获取昨天的日期字符串
func GetYesterdayDateString() string {
	return time.Now().AddDate(0, 0, -1).Format("2006-01-02")
}
