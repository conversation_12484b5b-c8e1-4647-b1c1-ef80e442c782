package api

import (
	"fmt"
	"os"
	"path/filepath"
	"solve_api/internal/service"
	"solve_api/internal/utils"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type LogHandler struct {
	apiLogService *service.APILogService
}

// NewLogHandler 创建日志处理器实例
func NewLogHandler(apiLogService *service.APILogService) *LogHandler {
	return &LogHandler{
		apiLogService: apiLogService,
	}
}

// GetAPILogs 获取API日志列表
func (h *LogHandler) GetAPILogs(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("page_size", "20"))

	// 可选的过滤参数
	userID := c.Query("user_id")
	appID := c.Query("app_id")
	method := c.Query("method")
	path := c.Query("path")
	statusCode := c.Query("status_code")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// 构建查询条件
	filters := make(map[string]interface{})
	if userID != "" {
		if uid, err := strconv.ParseUint(userID, 10, 32); err == nil {
			filters["user_id"] = uint(uid)
		}
	}
	if appID != "" {
		if aid, err := strconv.ParseUint(appID, 10, 32); err == nil {
			filters["app_id"] = uint(aid)
		}
	}
	if method != "" {
		filters["method"] = method
	}
	if path != "" {
		filters["path"] = path
	}
	if statusCode != "" {
		if sc, err := strconv.Atoi(statusCode); err == nil {
			filters["status_code"] = sc
		}
	}
	if startDate != "" {
		filters["start_date"] = startDate
	}
	if endDate != "" {
		filters["end_date"] = endDate
	}

	logs, total, err := h.apiLogService.GetAPILogsWithFilters(filters, page, pageSize)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	result := gin.H{
		"list":      logs,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
		"filters":   filters,
	}

	utils.SuccessWithMessage(c, "获取API日志列表成功", result)
}

// GetAPILogDetail 获取API日志详情
func (h *LogHandler) GetAPILogDetail(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		utils.BadRequest(c, "日志ID不能为空")
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "日志ID格式错误")
		return
	}

	log, err := h.apiLogService.GetAPILog(uint(id))
	if err != nil {
		if err.Error() == "API日志不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取API日志详情成功", log)
}

// GetAPILogStats 获取API日志统计
func (h *LogHandler) GetAPILogStats(c *gin.Context) {
	// 获取查询参数
	startDate := c.DefaultQuery("start_date", time.Now().AddDate(0, 0, -7).Format("2006-01-02"))
	endDate := c.DefaultQuery("end_date", time.Now().Format("2006-01-02"))
	userID := c.Query("user_id")
	appID := c.Query("app_id")

	// 构建统计条件
	filters := make(map[string]interface{})
	if userID != "" {
		if uid, err := strconv.ParseUint(userID, 10, 32); err == nil {
			filters["user_id"] = uint(uid)
		}
	}
	if appID != "" {
		if aid, err := strconv.ParseUint(appID, 10, 32); err == nil {
			filters["app_id"] = uint(aid)
		}
	}
	filters["start_date"] = startDate
	filters["end_date"] = endDate

	stats, err := h.apiLogService.GetAPILogStats(filters)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取API日志统计成功", stats)
}

// GetSystemLogs 获取系统日志
func (h *LogHandler) GetSystemLogs(c *gin.Context) {
	// 获取查询参数
	level := c.DefaultQuery("level", "")
	lines := c.DefaultQuery("lines", "100")
	keyword := c.Query("keyword")

	maxLines, _ := strconv.Atoi(lines)
	if maxLines <= 0 || maxLines > 1000 {
		maxLines = 100
	}

	// 读取系统日志文件
	logFile := "logs/app.log"
	if _, err := os.Stat(logFile); os.IsNotExist(err) {
		// 如果日志文件不存在，返回空日志
		utils.SuccessWithMessage(c, "获取系统日志成功", gin.H{
			"logs":  []string{},
			"total": 0,
			"file":  logFile,
		})
		return
	}

	logs, err := h.readLogFile(logFile, maxLines, level, keyword)
	if err != nil {
		utils.ServerError(c, "读取系统日志失败: "+err.Error())
		return
	}

	result := gin.H{
		"logs":    logs,
		"total":   len(logs),
		"file":    logFile,
		"level":   level,
		"keyword": keyword,
		"lines":   maxLines,
	}

	utils.SuccessWithMessage(c, "获取系统日志成功", result)
}

// ExportAPILogs 导出API日志
func (h *LogHandler) ExportAPILogs(c *gin.Context) {
	// 获取导出参数
	format := c.DefaultQuery("format", "csv")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	userID := c.Query("user_id")
	appID := c.Query("app_id")

	// 构建查询条件
	filters := make(map[string]interface{})
	if userID != "" {
		if uid, err := strconv.ParseUint(userID, 10, 32); err == nil {
			filters["user_id"] = uint(uid)
		}
	}
	if appID != "" {
		if aid, err := strconv.ParseUint(appID, 10, 32); err == nil {
			filters["app_id"] = uint(aid)
		}
	}
	if startDate != "" {
		filters["start_date"] = startDate
	}
	if endDate != "" {
		filters["end_date"] = endDate
	}

	// 导出日志
	filename, err := h.apiLogService.ExportAPILogs(filters, format)
	if err != nil {
		utils.ServerError(c, "导出API日志失败: "+err.Error())
		return
	}

	// 设置下载响应头
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filepath.Base(filename)))
	c.Header("Content-Type", "application/octet-stream")

	// 返回文件
	c.File(filename)
}

// CleanAPILogs 清理API日志
func (h *LogHandler) CleanAPILogs(c *gin.Context) {
	var req struct {
		Days int `json:"days" binding:"required,min=1"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	err := h.apiLogService.CleanOldLogs(req.Days)
	if err != nil {
		utils.ServerError(c, "清理API日志失败: "+err.Error())
		return
	}

	utils.SuccessWithMessage(c, "API日志清理成功", gin.H{
		"days":    req.Days,
		"message": fmt.Sprintf("已清理%d天前的API日志", req.Days),
	})
}

// readLogFile 读取日志文件
func (h *LogHandler) readLogFile(filename string, maxLines int, level, keyword string) ([]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var lines []string
	var buffer []byte
	var line []byte

	// 从文件末尾开始读取
	stat, err := file.Stat()
	if err != nil {
		return nil, err
	}

	fileSize := stat.Size()
	bufferSize := int64(4096)
	offset := fileSize

	for offset > 0 && len(lines) < maxLines {
		if offset < bufferSize {
			bufferSize = offset
		}
		offset -= bufferSize

		buffer = make([]byte, bufferSize)
		_, err := file.ReadAt(buffer, offset)
		if err != nil {
			return nil, err
		}

		// 处理缓冲区中的行
		for i := len(buffer) - 1; i >= 0; i-- {
			if buffer[i] == '\n' {
				if len(line) > 0 {
					lineStr := string(line)
					if h.matchLogFilter(lineStr, level, keyword) {
						lines = append([]string{lineStr}, lines...)
						if len(lines) >= maxLines {
							break
						}
					}
					line = nil
				}
			} else {
				line = append([]byte{buffer[i]}, line...)
			}
		}
	}

	// 处理最后一行
	if len(line) > 0 && len(lines) < maxLines {
		lineStr := string(line)
		if h.matchLogFilter(lineStr, level, keyword) {
			lines = append([]string{lineStr}, lines...)
		}
	}

	return lines, nil
}

// matchLogFilter 匹配日志过滤条件
func (h *LogHandler) matchLogFilter(line, level, keyword string) bool {
	// 级别过滤
	if level != "" {
		if !strings.Contains(strings.ToUpper(line), strings.ToUpper(level)) {
			return false
		}
	}

	// 关键词过滤
	if keyword != "" {
		if !strings.Contains(strings.ToLower(line), strings.ToLower(keyword)) {
			return false
		}
	}

	return true
}
