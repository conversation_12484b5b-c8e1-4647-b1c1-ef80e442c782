# Questions表字段冗余分析报告

## 📊 问题概述

通过多次业务调整和迁移，questions表积累了大量冗余字段。本文档详细分析了所有重复字段并提供彻底的清理方案。

## 🔍 当前字段冗余情况

### 重复的内容字段（3个字段存储相同内容）

| 字段名 | 用途 | 数据来源 | 状态 |
|--------|------|----------|------|
| `content` | 兼容字段，存储题目内容 | 历史遗留 | ❌ **删除** |
| `question_text` | 结构化题目内容 | 新架构主字段 | ✅ **保留** |
| `question_doc` | 题目内容 | 重新架构时添加 | ❌ **删除** |

**冗余原因**：三个字段存储完全相同的题目内容，造成存储浪费和维护复杂性。

### 重复的缓存键字段（2个字段存储相同哈希）

| 字段名 | 用途 | 数据来源 | 状态 |
|--------|------|----------|------|
| `hash` | 题目哈希值 | 原始设计 | ❌ **删除** |
| `cache_key` | 缓存键 | 新架构添加 | ✅ **保留** |

**冗余原因**：两个字段存储相同的MD5哈希值，用于缓存查询。

### 重复的原始数据字段（2个字段存储相同原始内容）

| 字段名 | 用途 | 数据来源 | 状态 |
|--------|------|----------|------|
| `raw_content` | 原始识别内容 | 早期添加 | ❌ **删除** |
| `raw_qwen` | Qwen原始返回数据 | 调试需求添加 | ✅ **保留** |

**冗余原因**：两个字段存储相同的AI识别原始数据。

### 选项存储方式冗余（JSON vs 分散字段）

| 存储方式 | 字段 | 优势 | 劣势 | 决策 |
|----------|------|------|------|------|
| JSON格式 | `options` | 结构化存储 | 不便单独更新 | ❌ **删除** |
| 分散字段 | `options_a/b/c/d/y/n` | 便于维护 | 字段较多 | ✅ **保留** |

**保留分散字段的原因**：
- ✅ 可以单独更新某个选项
- ✅ SQL查询可以直接访问特定选项  
- ✅ 避免JSON解析开销
- ✅ 数据完整性更好

### 很少使用的字段

| 字段名 | 用途 | 使用频率 | 状态 |
|--------|------|----------|------|
| `question_img` | 题目图片 | 极少 | ❌ **删除** |
| `associates` | 关联键 | 很少 | ❌ **删除** |

## 🎯 优化后的最终字段结构

### 核心业务字段（必需保留）

| 字段名 | 类型 | 用途 | 约束 |
|--------|------|------|------|
| `id` | uint | 主键 | PRIMARY KEY |
| `cache_key` | varchar(128) | 缓存键 | UNIQUE, NOT NULL |
| `question_type` | varchar(20) | 题目类型 | NOT NULL, INDEX |
| `question_text` | text | 题目内容 | NOT NULL |
| `answer` | text | 正确答案 | NOT NULL |
| `analysis` | text | 答案解析 | - |

### 选项字段（分散存储）

| 字段名 | 类型 | 用途 | 适用题型 |
|--------|------|------|----------|
| `options_a` | text | 选项A | 单选题、多选题 |
| `options_b` | text | 选项B | 单选题、多选题 |
| `options_c` | text | 选项C | 单选题、多选题 |
| `options_d` | text | 选项D | 单选题、多选题 |
| `options_y` | text | 选项Y（正确） | 判断题 |
| `options_n` | text | 选项N（错误） | 判断题 |

### 统计和元数据字段

| 字段名 | 类型 | 用途 | 默认值 |
|--------|------|------|--------|
| `response` | int | 响应次数 | 0 |
| `subject` | varchar(20) | 学科 | '未知' |
| `grade` | varchar(20) | 年级 | '未知' |
| `difficulty` | int | 难度1-5 | 3 |
| `source_model` | varchar(50) | 来源模型 | 'unknown' |

### 图片和调试字段

| 字段名 | 类型 | 用途 | 说明 |
|--------|------|------|------|
| `question_img_raw` | text | 用户原始图片URL | 可选 |
| `raw_qwen` | longtext | Qwen原始数据 | 调试用 |
| `raw_deepseek` | longtext | DeepSeek原始数据 | 调试用 |

### 时间字段

| 字段名 | 类型 | 用途 |
|--------|------|------|
| `created_at` | timestamp | 创建时间 |
| `updated_at` | timestamp | 更新时间 |
| `deleted_at` | timestamp | 软删除时间 |

## 📈 优化效果预估

### 字段数量对比

| 项目 | 优化前 | 优化后 | 减少 |
|------|--------|--------|------|
| 总字段数 | ~30个 | ~18个 | 40% |
| 内容字段 | 3个 | 1个 | 67% |
| 缓存字段 | 2个 | 1个 | 50% |
| 原始数据字段 | 2个 | 1个 | 50% |

### 存储空间优化

```sql
-- 优化前：每条记录约占用空间
-- content(TEXT) + question_text(TEXT) + question_doc(TEXT) = 3倍内容存储
-- hash(64) + cache_key(128) = 192字节哈希存储

-- 优化后：每条记录约占用空间  
-- question_text(TEXT) = 1倍内容存储
-- cache_key(128) = 128字节哈希存储

-- 预计减少存储空间：35-40%
```

### 查询性能优化

```sql
-- 优化前：需要处理多个冗余字段
SELECT content, question_text, question_doc FROM questions WHERE hash = ? OR cache_key = ?;

-- 优化后：单一字段查询
SELECT question_text FROM questions WHERE cache_key = ?;
```

## 🔧 执行步骤

### 1. 数据备份
```sql
CREATE TABLE questions_backup_final AS SELECT * FROM questions;
```

### 2. 数据迁移
```sql
-- 统一内容到question_text
UPDATE questions SET question_text = COALESCE(question_text, question_doc, content);

-- 统一缓存键到cache_key  
UPDATE questions SET cache_key = COALESCE(cache_key, hash);
```

### 3. 删除冗余字段
```sql
-- 删除重复内容字段
ALTER TABLE questions DROP COLUMN content, DROP COLUMN question_doc;

-- 删除重复缓存字段
ALTER TABLE questions DROP COLUMN hash;

-- 删除其他冗余字段
ALTER TABLE questions DROP COLUMN raw_content, DROP COLUMN options, 
                      DROP COLUMN question_img, DROP COLUMN associates;
```

### 4. 优化索引
```sql
-- 删除基于已删除字段的索引
DROP INDEX idx_questions_hash;

-- 创建新的优化索引
CREATE UNIQUE INDEX idx_questions_cache_key_unique ON questions(cache_key);
```

## ⚠️ 注意事项

1. **执行顺序**：必须先迁移数据，再删除字段
2. **备份重要**：删除字段前务必完整备份
3. **测试验证**：在测试环境完整验证后再在生产环境执行
4. **代码同步**：确保代码中的模型定义与数据库结构一致

## 🎯 预期收益

1. **存储优化**：减少35-40%的存储空间
2. **查询性能**：提升30-40%的查询速度
3. **维护简化**：减少字段维护复杂度
4. **代码清晰**：统一的字段命名和用途

通过这次彻底的字段清理，questions表将变得更加精简、高效和易于维护。
