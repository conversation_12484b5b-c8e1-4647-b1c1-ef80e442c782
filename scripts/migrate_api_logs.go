package main

import (
	"fmt"
	"log"
	"solve_api/internal/config"
	"solve_api/internal/database"

	"gorm.io/gorm"
)

func main() {
	// 1. 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 2. 初始化数据库
	if err := database.InitDB(cfg); err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}

	db := database.GetDB()

	fmt.Println("🚀 开始迁移API日志表...")

	// 3. 添加新字段
	if err := addModelFields(db); err != nil {
		log.Fatalf("添加模型字段失败: %v", err)
	}

	fmt.Println("✅ API日志表迁移完成！")
}

// addModelFields 添加模型相关字段
func addModelFields(db *gorm.DB) error {
	// 检查表是否存在
	if !db.Migrator().HasTable("api_logs") {
		fmt.Println("⚠️  api_logs表不存在，跳过迁移")
		return nil
	}

	// 添加model_name字段
	if !db.Migrator().HasColumn("api_logs", "model_name") {
		if err := db.Migrator().AddColumn("api_logs", "model_name"); err != nil {
			return fmt.Errorf("添加model_name字段失败: %w", err)
		}
		fmt.Println("✅ 已添加model_name字段")
	} else {
		fmt.Println("📋 model_name字段已存在")
	}

	// 添加model_request字段
	if !db.Migrator().HasColumn("api_logs", "model_request") {
		if err := db.Migrator().AddColumn("api_logs", "model_request"); err != nil {
			return fmt.Errorf("添加model_request字段失败: %w", err)
		}
		fmt.Println("✅ 已添加model_request字段")
	} else {
		fmt.Println("📋 model_request字段已存在")
	}

	// 添加model_response字段
	if !db.Migrator().HasColumn("api_logs", "model_response") {
		if err := db.Migrator().AddColumn("api_logs", "model_response"); err != nil {
			return fmt.Errorf("添加model_response字段失败: %w", err)
		}
		fmt.Println("✅ 已添加model_response字段")
	} else {
		fmt.Println("📋 model_response字段已存在")
	}

	// 添加model_cost字段
	if !db.Migrator().HasColumn("api_logs", "model_cost") {
		if err := db.Migrator().AddColumn("api_logs", "model_cost"); err != nil {
			return fmt.Errorf("添加model_cost字段失败: %w", err)
		}
		fmt.Println("✅ 已添加model_cost字段")
	} else {
		fmt.Println("📋 model_cost字段已存在")
	}

	return nil
}
