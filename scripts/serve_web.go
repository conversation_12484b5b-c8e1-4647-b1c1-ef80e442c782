package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
)

func main() {
	// 获取当前工作目录
	wd, err := os.Getwd()
	if err != nil {
		log.Fatal(err)
	}

	// 设置静态文件服务
	webDir := filepath.Join(wd, "web")

	// 检查web目录是否存在
	if _, err := os.Stat(webDir); os.IsNotExist(err) {
		log.Fatalf("Web目录不存在: %s", webDir)
	}

	// 创建文件服务器
	fs := http.FileServer(http.Dir(webDir))

	// 设置路由
	http.Handle("/", fs)

	// 添加CORS支持
	corsHandler := func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// 设置CORS头
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.<PERSON><PERSON>().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

			// 处理预检请求
			if r.Method == "OPTIONS" {
				w.WriteHeader(http.StatusOK)
				return
			}

			next(w, r)
		}
	}

	// 管理界面
	http.HandleFunc("/admin.html", corsHandler(func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, filepath.Join(webDir, "admin.html"))
	}))

	// 测试界面
	http.HandleFunc("/test.html", corsHandler(func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, filepath.Join(webDir, "test.html"))
	}))

	port := "3000"
	fmt.Printf("🌐 Web服务器启动成功！\n")
	fmt.Printf("📱 管理界面: http://localhost:%s/admin.html\n", port)
	fmt.Printf("🧪 测试界面: http://localhost:%s/test.html\n", port)
	fmt.Printf("🔧 API服务: http://localhost:8080\n")
	fmt.Printf("\n")
	fmt.Printf("📋 默认管理员账号:\n")
	fmt.Printf("   用户名: 15688515913\n")
	fmt.Printf("   密码: admin888\n")
	fmt.Printf("\n")
	fmt.Printf("🎯 功能说明:\n")
	fmt.Printf("   - admin.html: 模型参数配置管理\n")
	fmt.Printf("   - test.html: API搜题流程测试\n")
	fmt.Printf("\n")
	fmt.Printf("⚠️  请确保API服务(端口8080)已启动\n")
	fmt.Printf("💡 按 Ctrl+C 停止服务\n")
	fmt.Printf("\n")

	log.Fatal(http.ListenAndServe(":"+port, nil))
}
