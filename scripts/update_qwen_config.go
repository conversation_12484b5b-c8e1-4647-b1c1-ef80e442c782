package main

import (
	"encoding/json"
	"fmt"
	"log"
	"solve_api/config"
	"solve_api/internal/database"
	"solve_api/internal/model"
	"solve_api/internal/repository"
)

func main() {
	fmt.Println("🔧 更新Qwen模型配置，移除不必要的参数...")

	// 1. 加载配置
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 2. 初始化数据库
	if err := database.InitDB(cfg); err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}

	// 3. 创建仓库
	modelConfigRepo := repository.NewModelConfigRepository(database.GetDB())

	// 4. 获取当前Qwen配置
	qwenConfig, err := modelConfigRepo.GetByName(model.ModelNameQwenVLPlus)
	if err != nil {
		log.Fatalf("获取Qwen配置失败: %v", err)
	}

	if qwenConfig == nil {
		log.Fatalf("Qwen配置不存在")
	}

	fmt.Printf("📋 当前Qwen配置:\n")
	currentParams, _ := qwenConfig.GetParamsMap()
	currentJSON, _ := json.MarshalIndent(currentParams, "", "  ")
	fmt.Printf("%s\n\n", string(currentJSON))

	// 5. 构建新的参数配置（只包含Qwen API支持的参数）
	newParams := map[string]interface{}{
		// 模型配置
		"model": "qwen-vl-plus",

		// 基础参数
		"temperature":       0,
		"top_p":             0.01,
		"top_k":             1,
		"do_sample":         false,
		"frequency_penalty": -2,
		"presence_penalty":  -2,

		// 输出格式
		"response_format": map[string]string{"type": "json_object"},

		// 图像处理
		"detail": "high",

		// 消息配置
		"system_message": "精准且完整的识别题目内容，严格标准返回json格式。示例{\"question_type\": \"单选题or多选题or判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\"}}",
		"user_message":   "question_text内的值不应该出现题目类型以及问题序号。",
	}

	fmt.Printf("🔄 新的Qwen配置:\n")
	newJSON, _ := json.MarshalIndent(newParams, "", "  ")
	fmt.Printf("%s\n\n", string(newJSON))

	// 6. 更新配置
	if err := qwenConfig.SetParamsMap(newParams); err != nil {
		log.Fatalf("设置新参数失败: %v", err)
	}

	if err := modelConfigRepo.Update(qwenConfig); err != nil {
		log.Fatalf("更新配置失败: %v", err)
	}

	fmt.Println("✅ Qwen配置更新成功！")

	// 7. 验证更新结果
	updatedConfig, err := modelConfigRepo.GetByName(model.ModelNameQwenVLPlus)
	if err != nil {
		log.Fatalf("验证更新失败: %v", err)
	}

	fmt.Printf("🔍 验证更新后的配置:\n")
	verifyParams, _ := updatedConfig.GetParamsMap()
	verifyJSON, _ := json.MarshalIndent(verifyParams, "", "  ")
	fmt.Printf("%s\n\n", string(verifyJSON))

	// 8. 构建示例请求体
	fmt.Printf("📤 更新后的Qwen请求体示例:\n")

	// 提取技术参数
	parameters := make(map[string]interface{})
	supportedParams := []string{
		"temperature", "top_p", "top_k", "do_sample",
		"response_format", "detail", "frequency_penalty", "presence_penalty",
	}

	for _, paramName := range supportedParams {
		if value, exists := verifyParams[paramName]; exists {
			parameters[paramName] = value
		}
	}

	// 构建完整请求体
	requestBody := map[string]interface{}{
		"model": verifyParams["model"],
		"messages": []map[string]interface{}{
			{
				"role":    "system",
				"content": verifyParams["system_message"],
			},
			{
				"role": "user",
				"content": []map[string]interface{}{
					{
						"image": "http://solve.igmdns.com/img/23.jpg",
					},
					{
						"text": verifyParams["user_message"],
					},
				},
			},
		},
		"parameters": parameters,
	}

	requestJSON, _ := json.MarshalIndent(requestBody, "", "  ")
	fmt.Printf("%s\n", string(requestJSON))

	fmt.Println("\n🎉 配置更新完成！现在Qwen请求将严格按照API文档格式发送。")
}
