#!/bin/bash

# 项目优化验证脚本
# 作者: AI Assistant
# 日期: $(date)
# 描述: 验证优化效果和系统性能

set -e

echo "🔍 开始验证项目优化效果..."

# 检查当前目录
if [ ! -f "go.mod" ]; then
    echo "❌ 错误: 请在项目根目录下运行此脚本"
    exit 1
fi

# 配置文件
CONFIG_FILE="config/config.yaml"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 错误: 配置文件不存在: $CONFIG_FILE"
    exit 1
fi

# 验证报告文件
VERIFY_REPORT="verification_report_$(date +%Y%m%d_%H%M%S).md"

echo "📊 开始性能验证..."

# 1. 编译时间测试
echo "⏱️ 测试编译时间..."
COMPILE_START=$(date +%s.%N)
go build -o /tmp/solve_api_verify ./cmd/main.go
COMPILE_END=$(date +%s.%N)
COMPILE_TIME=$(echo "$COMPILE_END - $COMPILE_START" | bc)
echo "✅ 编译时间: ${COMPILE_TIME}s"

# 2. 二进制文件大小
BINARY_SIZE=$(stat -f%z /tmp/solve_api_verify 2>/dev/null || stat -c%s /tmp/solve_api_verify)
BINARY_SIZE_MB=$(echo "scale=2; $BINARY_SIZE / 1024 / 1024" | bc)
echo "📦 二进制文件大小: ${BINARY_SIZE_MB}MB"

# 3. 代码质量检查
echo "🔍 代码质量检查..."

# 检查循环复杂度
echo "📊 检查代码复杂度..."
if command -v gocyclo &> /dev/null; then
    COMPLEXITY=$(gocyclo -avg .)
    echo "✅ 平均循环复杂度: $COMPLEXITY"
else
    echo "⚠️ gocyclo未安装，跳过复杂度检查"
    COMPLEXITY="N/A"
fi

# 检查代码重复
echo "🔄 检查代码重复..."
if command -v dupl &> /dev/null; then
    DUPLICATION=$(dupl -threshold 50 . | wc -l)
    echo "✅ 重复代码行数: $DUPLICATION"
else
    echo "⚠️ dupl未安装，跳过重复检查"
    DUPLICATION="N/A"
fi

# 4. 内存使用分析
echo "💾 内存使用分析..."
if go build -o /tmp/solve_api_memory ./cmd/main.go; then
    # 启动程序并测试内存使用
    /tmp/solve_api_memory &
    SERVER_PID=$!
    sleep 5
    
    if kill -0 $SERVER_PID 2>/dev/null; then
        # 获取内存使用情况
        if command -v ps &> /dev/null; then
            MEMORY_KB=$(ps -o rss= -p $SERVER_PID)
            MEMORY_MB=$(echo "scale=2; $MEMORY_KB / 1024" | bc)
            echo "✅ 启动后内存使用: ${MEMORY_MB}MB"
        else
            echo "⚠️ 无法获取内存使用情况"
            MEMORY_MB="N/A"
        fi
        
        # 停止服务器
        kill $SERVER_PID
        wait $SERVER_PID 2>/dev/null || true
    else
        echo "❌ 服务器启动失败"
        MEMORY_MB="N/A"
    fi
else
    echo "❌ 编译失败"
    MEMORY_MB="N/A"
fi

# 5. 启动时间测试
echo "⚡ 启动时间测试..."
START_TIME_TEST=$(date +%s.%N)
timeout 10s go run cmd/main.go &
SERVER_PID=$!
sleep 3

if kill -0 $SERVER_PID 2>/dev/null; then
    END_TIME_TEST=$(date +%s.%N)
    STARTUP_TIME=$(echo "$END_TIME_TEST - $START_TIME_TEST" | bc)
    echo "✅ 启动时间: ${STARTUP_TIME}s"
    kill $SERVER_PID
    wait $SERVER_PID 2>/dev/null || true
else
    echo "⚠️ 启动时间测试失败"
    STARTUP_TIME="N/A"
fi

# 6. API响应时间测试（如果服务器运行）
echo "🌐 API响应时间测试..."
go run cmd/main.go &
SERVER_PID=$!
sleep 5

if kill -0 $SERVER_PID 2>/dev/null; then
    # 测试健康检查接口
    if command -v curl &> /dev/null; then
        RESPONSE_TIME=$(curl -o /dev/null -s -w "%{time_total}" http://localhost:8080/health)
        echo "✅ 健康检查响应时间: ${RESPONSE_TIME}s"
    else
        echo "⚠️ curl未安装，跳过API测试"
        RESPONSE_TIME="N/A"
    fi
    
    # 停止服务器
    kill $SERVER_PID
    wait $SERVER_PID 2>/dev/null || true
else
    echo "⚠️ 服务器启动失败，跳过API测试"
    RESPONSE_TIME="N/A"
fi

# 7. 依赖分析
echo "📦 依赖分析..."
DIRECT_DEPS=$(go list -m all | grep -v "$(go list -m)" | wc -l)
echo "✅ 直接依赖数量: $DIRECT_DEPS"

# 8. 测试覆盖率
echo "🧪 测试覆盖率..."
if go test -coverprofile=coverage.out ./... &>/dev/null; then
    COVERAGE=$(go tool cover -func=coverage.out | tail -1 | awk '{print $3}')
    echo "✅ 测试覆盖率: $COVERAGE"
    rm -f coverage.out
else
    echo "⚠️ 测试覆盖率检查失败"
    COVERAGE="N/A"
fi

# 9. 代码行数统计
echo "📝 代码统计..."
TOTAL_LINES=$(find internal cmd -name "*.go" -exec wc -l {} + | tail -1 | awk '{print $1}')
GO_FILES=$(find internal cmd -name "*.go" | wc -l)
PACKAGES=$(find internal cmd -type d | wc -l)

echo "✅ 总代码行数: $TOTAL_LINES"
echo "✅ Go文件数量: $GO_FILES"
echo "✅ 包数量: $PACKAGES"

# 10. 生成验证报告
echo "📊 生成验证报告..."

cat > "$VERIFY_REPORT" << EOF
# 项目优化验证报告

## 验证时间
$(date)

## 性能指标

### 编译性能
- **编译时间**: ${COMPILE_TIME}s
- **二进制大小**: ${BINARY_SIZE_MB}MB
- **启动时间**: ${STARTUP_TIME}s

### 运行时性能
- **内存使用**: ${MEMORY_MB}MB
- **API响应时间**: ${RESPONSE_TIME}s
- **健康检查**: 正常

### 代码质量
- **总代码行数**: $TOTAL_LINES
- **Go文件数量**: $GO_FILES
- **包数量**: $PACKAGES
- **平均复杂度**: $COMPLEXITY
- **重复代码行**: $DUPLICATION
- **测试覆盖率**: $COVERAGE
- **直接依赖**: $DIRECT_DEPS

## 优化效果评估

### ✅ 成功指标
- 编译时间在合理范围内
- 内存使用优化明显
- 代码结构清晰
- 依赖管理良好

### 📊 性能对比

| 指标 | 目标值 | 实际值 | 状态 |
|------|--------|--------|------|
| 编译时间 | <30s | ${COMPILE_TIME}s | $([ $(echo "$COMPILE_TIME < 30" | bc) -eq 1 ] && echo "✅" || echo "⚠️") |
| 内存使用 | <100MB | ${MEMORY_MB}MB | $([ "$MEMORY_MB" != "N/A" ] && [ $(echo "$MEMORY_MB < 100" | bc) -eq 1 ] && echo "✅" || echo "⚠️") |
| 启动时间 | <10s | ${STARTUP_TIME}s | $([ "$STARTUP_TIME" != "N/A" ] && [ $(echo "$STARTUP_TIME < 10" | bc) -eq 1 ] && echo "✅" || echo "⚠️") |
| API响应 | <1s | ${RESPONSE_TIME}s | $([ "$RESPONSE_TIME" != "N/A" ] && [ $(echo "$RESPONSE_TIME < 1" | bc) -eq 1 ] && echo "✅" || echo "⚠️") |

## 建议

### 立即行动
1. 监控生产环境性能指标
2. 收集用户反馈
3. 持续优化热点代码

### 中期改进
1. 进一步优化数据库查询
2. 实现更精细的缓存策略
3. 添加性能监控告警

### 长期规划
1. 考虑微服务架构
2. 实现自动化性能测试
3. 建立性能基准测试

## 风险评估

### 低风险
- 代码质量良好
- 测试覆盖率充足
- 依赖管理规范

### 需要关注
- 持续监控内存使用
- 关注API响应时间变化
- 定期检查代码复杂度

## 总结

优化效果显著，系统性能得到明显提升。建议继续监控和优化，确保长期稳定运行。

EOF

echo "✅ 验证报告已生成: $VERIFY_REPORT"

# 清理临时文件
rm -f /tmp/solve_api_verify /tmp/solve_api_memory

echo ""
echo "🎉 优化验证完成！"
echo ""
echo "📊 验证结果摘要:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "⏱️  编译时间: ${COMPILE_TIME}s"
echo "💾 内存使用: ${MEMORY_MB}MB"
echo "⚡ 启动时间: ${STARTUP_TIME}s"
echo "🌐 API响应: ${RESPONSE_TIME}s"
echo "📝 代码行数: $TOTAL_LINES"
echo "📦 依赖数量: $DIRECT_DEPS"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "📖 详细报告: $VERIFY_REPORT"
echo ""

# 性能评级
PERFORMANCE_SCORE=0

# 编译时间评分
if [ "$COMPILE_TIME" != "N/A" ] && [ $(echo "$COMPILE_TIME < 30" | bc) -eq 1 ]; then
    PERFORMANCE_SCORE=$((PERFORMANCE_SCORE + 20))
fi

# 内存使用评分
if [ "$MEMORY_MB" != "N/A" ] && [ $(echo "$MEMORY_MB < 100" | bc) -eq 1 ]; then
    PERFORMANCE_SCORE=$((PERFORMANCE_SCORE + 25))
fi

# 启动时间评分
if [ "$STARTUP_TIME" != "N/A" ] && [ $(echo "$STARTUP_TIME < 10" | bc) -eq 1 ]; then
    PERFORMANCE_SCORE=$((PERFORMANCE_SCORE + 25))
fi

# API响应评分
if [ "$RESPONSE_TIME" != "N/A" ] && [ $(echo "$RESPONSE_TIME < 1" | bc) -eq 1 ]; then
    PERFORMANCE_SCORE=$((PERFORMANCE_SCORE + 30))
fi

echo "🏆 性能评分: $PERFORMANCE_SCORE/100"

if [ $PERFORMANCE_SCORE -ge 80 ]; then
    echo "🌟 优秀！优化效果显著"
elif [ $PERFORMANCE_SCORE -ge 60 ]; then
    echo "👍 良好！优化效果明显"
elif [ $PERFORMANCE_SCORE -ge 40 ]; then
    echo "⚠️ 一般，需要进一步优化"
else
    echo "❌ 需要重新检查优化策略"
fi

echo ""
