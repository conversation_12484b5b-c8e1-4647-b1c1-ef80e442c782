#!/bin/bash

# Redis重复写入修复验证脚本

echo "🔍 Redis重复写入修复验证开始..."

# 1. 编译检查
echo "📦 步骤1: 编译检查..."
cd /Users/<USER>/go_lang/solve_api
if ! go build -o /tmp/solve_api cmd/main.go; then
    echo "❌ 编译失败"
    exit 1
fi
echo "✅ 编译成功"

# 2. 启动服务（后台）
echo "🚀 步骤2: 启动服务..."
go run cmd/main.go > /tmp/solve_api.log 2>&1 &
SERVER_PID=$!
echo "✅ 服务启动，PID: $SERVER_PID"

# 等待服务启动
sleep 8

# 3. 检查服务是否正常启动
echo "🔍 步骤3: 检查服务状态..."
if ! curl -s http://localhost:8080/health > /dev/null; then
    echo "❌ 服务启动失败"
    kill $SERVER_PID 2>/dev/null
    exit 1
fi
echo "✅ 服务正常运行"

# 4. 准备测试数据
echo "📝 步骤4: 准备测试数据..."
TEST_IMAGE_URL="https://example.com/test-image.jpg"
TEST_APP_KEY="test_app_key_001"

# 5. 执行搜题测试（模拟重复写入场景）
echo "🧪 步骤5: 执行搜题测试..."

# 清空Redis监控日志
echo "" > /tmp/redis_monitor.log

# 启动Redis监控（如果可用）
if command -v redis-cli &> /dev/null; then
    echo "📊 启动Redis监控..."
    redis-cli -h r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com -a SsYZyxSSr8uEVWKJ --no-auth-warning monitor > /tmp/redis_monitor.log 2>&1 &
    REDIS_MONITOR_PID=$!
    sleep 2
fi

# 执行搜题请求
echo "🔍 发送搜题请求..."
RESPONSE=$(curl -s -X POST http://localhost:8080/api/question/search \
  -H "Content-Type: application/json" \
  -d "{
    \"app_key\": \"$TEST_APP_KEY\",
    \"image_url\": \"$TEST_IMAGE_URL\"
  }")

echo "📋 搜题响应: $RESPONSE"

# 停止Redis监控
if [ ! -z "$REDIS_MONITOR_PID" ]; then
    sleep 3
    kill $REDIS_MONITOR_PID 2>/dev/null
fi

# 6. 分析Redis操作日志
echo "📊 步骤6: 分析Redis操作..."
if [ -f /tmp/redis_monitor.log ]; then
    echo "🔍 Redis操作统计:"
    
    # 统计SET操作次数
    SET_COUNT=$(grep -c "SET.*question:" /tmp/redis_monitor.log 2>/dev/null || echo "0")
    echo "   - SET操作次数: $SET_COUNT"
    
    # 统计GET操作次数
    GET_COUNT=$(grep -c "GET.*question:" /tmp/redis_monitor.log 2>/dev/null || echo "0")
    echo "   - GET操作次数: $GET_COUNT"
    
    # 统计EXISTS操作次数
    EXISTS_COUNT=$(grep -c "EXISTS.*question:" /tmp/redis_monitor.log 2>/dev/null || echo "0")
    echo "   - EXISTS操作次数: $EXISTS_COUNT"
    
    # 检查是否有重复的SET操作
    DUPLICATE_SETS=$(grep "SET.*question:" /tmp/redis_monitor.log 2>/dev/null | sort | uniq -d | wc -l || echo "0")
    echo "   - 重复SET操作: $DUPLICATE_SETS"
    
    if [ "$DUPLICATE_SETS" -eq 0 ]; then
        echo "✅ 未发现重复SET操作"
    else
        echo "⚠️ 发现 $DUPLICATE_SETS 个重复SET操作"
        echo "🔍 重复操作详情:"
        grep "SET.*question:" /tmp/redis_monitor.log 2>/dev/null | sort | uniq -d
    fi
else
    echo "⚠️ Redis监控日志不可用，跳过Redis操作分析"
fi

# 7. 分析应用日志
echo "📊 步骤7: 分析应用日志..."
if [ -f /tmp/solve_api.log ]; then
    echo "🔍 应用日志分析:"
    
    # 统计Redis写入相关日志
    REDIS_SET_LOGS=$(grep -c "Redis缓存" /tmp/solve_api.log 2>/dev/null || echo "0")
    echo "   - Redis缓存操作日志: $REDIS_SET_LOGS"
    
    # 统计回写相关日志
    CACHE_BACK_LOGS=$(grep -c "回传" /tmp/solve_api.log 2>/dev/null || echo "0")
    echo "   - 缓存回写日志: $CACHE_BACK_LOGS"
    
    # 检查是否有重复写入的警告
    DUPLICATE_WARNINGS=$(grep -c "重复写入" /tmp/solve_api.log 2>/dev/null || echo "0")
    echo "   - 重复写入警告: $DUPLICATE_WARNINGS"
    
    # 显示最近的Redis相关日志
    echo "🔍 最近的Redis操作日志:"
    grep -E "(Redis|缓存|回写|回传)" /tmp/solve_api.log 2>/dev/null | tail -10 || echo "   无相关日志"
fi

# 8. 性能分析
echo "📊 步骤8: 性能分析..."
if [ -f /tmp/solve_api.log ]; then
    # 提取处理时间
    PROCESS_TIME=$(grep "完整搜题流程总耗时" /tmp/solve_api.log 2>/dev/null | tail -1 | grep -o "[0-9.]*[ms]*" | head -1 || echo "未知")
    echo "   - 搜题流程总耗时: $PROCESS_TIME"
    
    # 提取Redis操作时间
    REDIS_TIME=$(grep "Redis缓存保存完成" /tmp/solve_api.log 2>/dev/null | tail -1 | grep -o "[0-9.]*[ms]*" | head -1 || echo "未知")
    echo "   - Redis缓存操作耗时: $REDIS_TIME"
fi

# 9. 清理
echo "🧹 步骤9: 清理资源..."
kill $SERVER_PID 2>/dev/null
echo "✅ 服务已停止"

# 10. 生成报告
echo "📋 步骤10: 生成测试报告..."

cat > /tmp/redis_duplicate_test_report.md << EOF
# Redis重复写入修复验证报告

## 测试概览
- 测试时间: $(date)
- 测试场景: 搜题流程Redis写入优化
- 服务状态: 正常启动和响应

## Redis操作统计
- SET操作次数: $SET_COUNT
- GET操作次数: $GET_COUNT  
- EXISTS操作次数: $EXISTS_COUNT
- 重复SET操作: $DUPLICATE_SETS

## 应用日志统计
- Redis缓存操作日志: $REDIS_SET_LOGS
- 缓存回写日志: $CACHE_BACK_LOGS
- 重复写入警告: $DUPLICATE_WARNINGS

## 性能指标
- 搜题流程总耗时: $PROCESS_TIME
- Redis缓存操作耗时: $REDIS_TIME

## 修复效果评估
EOF

if [ "$DUPLICATE_SETS" -eq 0 ] && [ "$DUPLICATE_WARNINGS" -eq 0 ]; then
    echo "✅ **修复成功**: 未发现Redis重复写入问题" >> /tmp/redis_duplicate_test_report.md
    echo ""
    echo "🎉 Redis重复写入修复验证通过！"
    echo "📋 详细报告: /tmp/redis_duplicate_test_report.md"
    RESULT=0
else
    echo "⚠️ **需要进一步优化**: 仍存在重复写入问题" >> /tmp/redis_duplicate_test_report.md
    echo ""
    echo "⚠️ Redis重复写入问题仍需优化"
    echo "📋 详细报告: /tmp/redis_duplicate_test_report.md"
    RESULT=1
fi

echo ""
echo "🔍 完整测试报告:"
cat /tmp/redis_duplicate_test_report.md

exit $RESULT
