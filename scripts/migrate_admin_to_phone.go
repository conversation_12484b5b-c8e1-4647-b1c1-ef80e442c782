package main

import (
	"fmt"
	"log"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/model"
	"solve_api/internal/utils"
	"time"

	"gorm.io/gorm"
)

// OldAdmin 旧的管理员结构（用于迁移）
type OldAdmin struct {
	ID        uint           `gorm:"primaryKey" json:"id"`
	Username  string         `gorm:"uniqueIndex;size:50;not null" json:"username"`
	Password  string         `gorm:"size:100;not null" json:"-"`
	Role      int            `gorm:"not null" json:"role"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

func (OldAdmin) TableName() string {
	return "admins"
}

func main() {
	fmt.Println("=== 管理员账号迁移工具 ===")
	fmt.Println("将管理员账号从用户名格式迁移到手机号格式")

	// 加载配置
	cfg, err := config.LoadConfig("config.yaml")
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 初始化数据库
	if err := database.InitMySQL(&cfg.Database.MySQL); err != nil {
		log.Fatalf("❌ 数据库初始化失败: %v", err)
	}

	db := database.GetDB()

	// 检查是否已经迁移过
	if isAlreadyMigrated(db) {
		fmt.Println("✅ 数据库已经迁移过，无需重复迁移")
		return
	}

	// 执行迁移
	if err := migrateAdminToPhone(db); err != nil {
		log.Fatalf("❌ 迁移失败: %v", err)
	}

	fmt.Println("✅ 管理员账号迁移完成")
}

// isAlreadyMigrated 检查是否已经迁移过
func isAlreadyMigrated(db *gorm.DB) bool {
	// 检查是否存在phone字段
	if db.Migrator().HasColumn(&model.Admin{}, "phone") {
		// 检查是否还有username字段
		if !db.Migrator().HasColumn(&model.Admin{}, "username") {
			return true
		}
	}
	return false
}

// migrateAdminToPhone 执行迁移
func migrateAdminToPhone(db *gorm.DB) error {
	fmt.Println("开始迁移管理员账号...")

	// 1. 添加phone字段
	fmt.Println("1. 添加phone字段...")
	if !db.Migrator().HasColumn(&model.Admin{}, "phone") {
		if err := db.Migrator().AddColumn(&model.Admin{}, "phone"); err != nil {
			return fmt.Errorf("添加phone字段失败: %w", err)
		}
	}

	// 2. 查询所有管理员（使用旧结构）
	var oldAdmins []OldAdmin
	if err := db.Find(&oldAdmins).Error; err != nil {
		return fmt.Errorf("查询管理员失败: %w", err)
	}

	fmt.Printf("2. 找到 %d 个管理员账号需要迁移\n", len(oldAdmins))

	// 3. 迁移数据
	for i, admin := range oldAdmins {
		fmt.Printf("3.%d 迁移管理员: %s\n", i+1, admin.Username)

		var phone string

		// 如果username已经是手机号格式，直接使用
		if utils.ValidatePhone(admin.Username) {
			phone = admin.Username
		} else {
			// 否则生成一个默认手机号（需要管理员后续修改）
			phone = fmt.Sprintf("1568851%04d", admin.ID)
			fmt.Printf("   ⚠️  用户名 '%s' 不是手机号格式，生成默认手机号: %s\n", admin.Username, phone)
			fmt.Println("   📝 请提醒管理员登录后修改为真实手机号")
		}

		// 更新phone字段
		if err := db.Model(&admin).Update("phone", phone).Error; err != nil {
			return fmt.Errorf("更新管理员 %s 的手机号失败: %w", admin.Username, err)
		}
	}

	// 4. 添加phone字段的唯一索引
	fmt.Println("4. 添加phone字段唯一索引...")
	if err := db.Exec("ALTER TABLE admins ADD UNIQUE INDEX idx_admins_phone (phone)").Error; err != nil {
		// 如果索引已存在，忽略错误
		fmt.Printf("   ⚠️  添加索引时出现错误（可能已存在）: %v\n", err)
	}

	// 5. 删除username字段的唯一索引
	fmt.Println("5. 删除username字段唯一索引...")
	if err := db.Exec("ALTER TABLE admins DROP INDEX idx_admins_username").Error; err != nil {
		// 如果索引不存在，忽略错误
		fmt.Printf("   ⚠️  删除索引时出现错误（可能不存在）: %v\n", err)
	}

	// 6. 删除username字段
	fmt.Println("6. 删除username字段...")
	if db.Migrator().HasColumn(&model.Admin{}, "username") {
		if err := db.Migrator().DropColumn(&model.Admin{}, "username"); err != nil {
			return fmt.Errorf("删除username字段失败: %w", err)
		}
	}

	// 7. 重新运行自动迁移以确保表结构正确
	fmt.Println("7. 更新表结构...")
	if err := db.AutoMigrate(&model.Admin{}); err != nil {
		return fmt.Errorf("自动迁移失败: %w", err)
	}

	return nil
}
