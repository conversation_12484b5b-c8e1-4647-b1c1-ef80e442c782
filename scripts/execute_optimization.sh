#!/bin/bash

# Questions表优化执行脚本
# 用途：安全地执行questions表的字段冗余清理
# 作者：AI Assistant
# 日期：2024-01-01

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 数据库配置
DB_HOST="***********"
DB_PORT="3380"
DB_USER="gmdns"
DB_PASS="5e7fFn3HpPfuQ6Qx42Az"
DB_NAME="go_solve"

# 日志文件
LOG_FILE="optimization_$(date +%Y%m%d_%H%M%S).log"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}" | tee -a "$LOG_FILE"
}

# 执行SQL并记录日志
execute_sql() {
    local sql=$1
    local description=$2
    
    print_message $BLUE "执行: $description"
    echo "SQL: $sql" >> "$LOG_FILE"
    
    mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "$sql" 2>&1 | tee -a "$LOG_FILE"
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 成功: $description"
    else
        print_message $RED "❌ 失败: $description"
        exit 1
    fi
}

# 检查数据库连接
check_database_connection() {
    print_message $BLUE "检查数据库连接..."
    
    mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "SELECT 1;" > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 数据库连接成功"
    else
        print_message $RED "❌ 数据库连接失败，请检查配置"
        exit 1
    fi
}

# 显示当前表结构
show_current_structure() {
    print_message $BLUE "显示当前questions表结构..."
    
    execute_sql "SELECT COUNT(*) as '当前字段数' FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = 'questions';" "统计当前字段数"
    
    execute_sql "SELECT COLUMN_NAME as '字段名', DATA_TYPE as '类型', COLUMN_COMMENT as '注释' FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = 'questions' ORDER BY ORDINAL_POSITION;" "显示当前字段列表"
}

# 创建备份
create_backup() {
    print_message $BLUE "创建数据备份..."
    
    execute_sql "CREATE TABLE IF NOT EXISTS questions_backup_final AS SELECT * FROM questions;" "创建完整备份表"
    
    execute_sql "SELECT COUNT(*) as '备份记录数' FROM questions_backup_final;" "验证备份数据"
    
    print_message $GREEN "✅ 数据备份完成"
}

# 数据迁移
migrate_data() {
    print_message $BLUE "开始数据迁移..."
    
    # 统一内容字段
    execute_sql "UPDATE questions SET question_text = CASE WHEN question_text IS NOT NULL AND question_text != '' THEN question_text WHEN question_doc IS NOT NULL AND question_doc != '' THEN question_doc WHEN content IS NOT NULL AND content != '' THEN content ELSE '' END WHERE question_text IS NULL OR question_text = '';" "统一内容字段到question_text"
    
    # 统一缓存键
    execute_sql "UPDATE questions SET cache_key = CASE WHEN cache_key IS NOT NULL AND cache_key != '' THEN cache_key WHEN hash IS NOT NULL AND hash != '' THEN hash ELSE MD5(CONCAT(COALESCE(question_type, ''), ':', COALESCE(question_text, ''))) END WHERE cache_key IS NULL OR cache_key = '';" "统一缓存键到cache_key"
    
    # 从JSON选项提取到分散字段
    execute_sql "UPDATE questions SET options_a = COALESCE(NULLIF(options_a, ''), CASE WHEN JSON_VALID(options) THEN JSON_UNQUOTE(JSON_EXTRACT(options, '$.A')) ELSE '' END, ''), options_b = COALESCE(NULLIF(options_b, ''), CASE WHEN JSON_VALID(options) THEN JSON_UNQUOTE(JSON_EXTRACT(options, '$.B')) ELSE '' END, ''), options_c = COALESCE(NULLIF(options_c, ''), CASE WHEN JSON_VALID(options) THEN JSON_UNQUOTE(JSON_EXTRACT(options, '$.C')) ELSE '' END, ''), options_d = COALESCE(NULLIF(options_d, ''), CASE WHEN JSON_VALID(options) THEN JSON_UNQUOTE(JSON_EXTRACT(options, '$.D')) ELSE '' END, ''), options_y = COALESCE(NULLIF(options_y, ''), CASE WHEN JSON_VALID(options) THEN JSON_UNQUOTE(JSON_EXTRACT(options, '$.Y')) ELSE '' END, ''), options_n = COALESCE(NULLIF(options_n, ''), CASE WHEN JSON_VALID(options) THEN JSON_UNQUOTE(JSON_EXTRACT(options, '$.N')) ELSE '' END, '') WHERE options IS NOT NULL AND options != '';" "从JSON选项提取到分散字段"
    
    print_message $GREEN "✅ 数据迁移完成"
}

# 验证数据迁移
verify_migration() {
    print_message $BLUE "验证数据迁移结果..."
    
    execute_sql "SELECT COUNT(*) as total_records, COUNT(CASE WHEN question_text IS NOT NULL AND question_text != '' THEN 1 END) as with_question_text, COUNT(CASE WHEN cache_key IS NOT NULL AND cache_key != '' THEN 1 END) as with_cache_key, COUNT(CASE WHEN options_a != '' OR options_b != '' OR options_c != '' OR options_d != '' THEN 1 END) as with_choice_options, COUNT(CASE WHEN options_y != '' OR options_n != '' THEN 1 END) as with_judgment_options FROM questions;" "验证数据完整性"
}

# 删除冗余字段
remove_redundant_fields() {
    print_message $YELLOW "⚠️  即将删除冗余字段，这是不可逆操作！"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_message $YELLOW "操作已取消"
        exit 0
    fi
    
    print_message $BLUE "删除冗余字段..."
    
    # 删除重复的内容字段
    execute_sql "ALTER TABLE questions DROP COLUMN IF EXISTS content;" "删除content字段"
    execute_sql "ALTER TABLE questions DROP COLUMN IF EXISTS question_doc;" "删除question_doc字段"
    
    # 删除重复的哈希字段
    execute_sql "ALTER TABLE questions DROP COLUMN IF EXISTS hash;" "删除hash字段"
    
    # 删除重复的原始内容字段
    execute_sql "ALTER TABLE questions DROP COLUMN IF EXISTS raw_content;" "删除raw_content字段"
    
    # 删除JSON选项字段
    execute_sql "ALTER TABLE questions DROP COLUMN IF EXISTS options;" "删除options字段"
    
    # 删除很少使用的字段
    execute_sql "ALTER TABLE questions DROP COLUMN IF EXISTS question_img;" "删除question_img字段"
    execute_sql "ALTER TABLE questions DROP COLUMN IF EXISTS associates;" "删除associates字段"
    
    print_message $GREEN "✅ 冗余字段删除完成"
}

# 优化索引
optimize_indexes() {
    print_message $BLUE "优化索引..."
    
    # 删除旧索引
    execute_sql "DROP INDEX IF EXISTS idx_questions_hash ON questions;" "删除hash索引"
    execute_sql "DROP INDEX IF EXISTS idx_questions_question_type_new ON questions;" "删除旧的question_type索引"
    execute_sql "DROP INDEX IF EXISTS idx_questions_associates ON questions;" "删除associates索引"
    execute_sql "DROP INDEX IF EXISTS idx_questions_cache_key ON questions;" "删除旧的cache_key索引"
    
    # 创建新索引
    execute_sql "CREATE UNIQUE INDEX IF NOT EXISTS idx_questions_cache_key_unique ON questions(cache_key);" "创建唯一cache_key索引"
    execute_sql "CREATE INDEX IF NOT EXISTS idx_questions_question_type ON questions(question_type);" "创建question_type索引"
    execute_sql "CREATE INDEX IF NOT EXISTS idx_questions_subject_grade ON questions(subject, grade);" "创建subject_grade复合索引"
    execute_sql "CREATE INDEX IF NOT EXISTS idx_questions_difficulty ON questions(difficulty);" "创建difficulty索引"
    execute_sql "CREATE INDEX IF NOT EXISTS idx_questions_response ON questions(response);" "创建response索引"
    execute_sql "CREATE INDEX IF NOT EXISTS idx_questions_created_at ON questions(created_at);" "创建created_at索引"
    
    print_message $GREEN "✅ 索引优化完成"
}

# 更新字段约束
update_constraints() {
    print_message $BLUE "更新字段约束..."
    
    execute_sql "ALTER TABLE questions MODIFY COLUMN cache_key VARCHAR(128) NOT NULL COMMENT '缓存键，基于题目内容生成';" "更新cache_key约束"
    execute_sql "ALTER TABLE questions MODIFY COLUMN question_type VARCHAR(20) NOT NULL COMMENT '题目类型';" "更新question_type约束"
    execute_sql "ALTER TABLE questions MODIFY COLUMN question_text TEXT NOT NULL COMMENT '题目内容';" "更新question_text约束"
    execute_sql "ALTER TABLE questions MODIFY COLUMN answer TEXT NOT NULL COMMENT '正确答案';" "更新answer约束"
    execute_sql "ALTER TABLE questions MODIFY COLUMN response INT DEFAULT 0 NOT NULL COMMENT '响应次数';" "更新response约束"
    
    print_message $GREEN "✅ 字段约束更新完成"
}

# 显示优化结果
show_optimization_result() {
    print_message $BLUE "显示优化结果..."
    
    execute_sql "SELECT '优化前字段数' as '指标', (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = 'questions_backup_final') as '数值' UNION ALL SELECT '优化后字段数' as '指标', (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = 'questions') as '数值' UNION ALL SELECT '减少字段数' as '指标', ((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = 'questions_backup_final') - (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = 'questions')) as '数值';" "统计优化效果"
    
    execute_sql "SELECT CONCAT('✅ ', COLUMN_NAME, ' - ', COLUMN_COMMENT) as '保留字段' FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '$DB_NAME' AND TABLE_NAME = 'questions' ORDER BY ORDINAL_POSITION;" "显示最终字段列表"
}

# 主函数
main() {
    print_message $GREEN "🚀 开始Questions表优化流程"
    print_message $BLUE "日志文件: $LOG_FILE"
    
    # 执行步骤
    check_database_connection
    show_current_structure
    create_backup
    migrate_data
    verify_migration
    remove_redundant_fields
    optimize_indexes
    update_constraints
    show_optimization_result
    
    print_message $GREEN "🎉 Questions表优化完成！"
    print_message $BLUE "备份表: questions_backup_final"
    print_message $BLUE "日志文件: $LOG_FILE"
}

# 执行主函数
main "$@"
