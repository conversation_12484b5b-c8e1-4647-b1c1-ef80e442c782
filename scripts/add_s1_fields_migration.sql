-- S1.md要求的新字段迁移脚本
-- 添加 associate 和 question_img 字段

-- 添加 associate 字段（关联键，存储多个cache_key，逗号分隔）
ALTER TABLE questions ADD COLUMN associate TEXT COMMENT '关联键，存储多个cache_key，逗号分隔';

-- 添加 question_img 字段（管理员添加的图片链接）
ALTER TABLE questions ADD COLUMN question_img TEXT COMMENT '管理员添加的图片链接';

-- 为新字段添加索引（可选，提升查询性能）
-- CREATE INDEX idx_questions_associate ON questions(associate(255));
-- CREATE INDEX idx_questions_question_img ON questions(question_img(255));

-- 验证字段添加成功
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT, 
    COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'questions' 
AND COLUMN_NAME IN ('associate', 'question_img')
ORDER BY COLUMN_NAME;
