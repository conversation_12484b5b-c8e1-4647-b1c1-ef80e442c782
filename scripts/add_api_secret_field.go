package main

import (
	"fmt"
	"log"
	"solve_api/internal/config"
	"solve_api/internal/database"
)

func main() {
	fmt.Println("🔧 添加API Secret字段...")

	// 1. 加载配置
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 2. 初始化数据库
	if err := database.InitMySQL(&cfg.Database.MySQL); err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}
	defer database.CloseMySQL()

	db := database.GetDB()

	// 3. 检查字段是否已存在
	fmt.Println("📋 检查api_secret字段是否存在...")
	var count int64
	err = db.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'model_configs' AND column_name = 'api_secret'").Scan(&count).Error
	if err != nil {
		log.Fatalf("检查字段失败: %v", err)
	}

	if count > 0 {
		fmt.Println("✅ api_secret字段已存在，无需添加")
		return
	}

	// 4. 添加api_secret字段
	fmt.Println("➕ 添加api_secret字段...")
	err = db.Exec("ALTER TABLE model_configs ADD COLUMN api_secret varchar(100) DEFAULT '' COMMENT 'API密钥/App Secret'").Error
	if err != nil {
		log.Fatalf("添加字段失败: %v", err)
	}

	fmt.Println("✅ api_secret字段添加成功")

	// 5. 验证字段添加
	fmt.Println("🔍 验证字段结构...")
	rows, err := db.Raw("DESCRIBE model_configs").Rows()
	if err != nil {
		log.Fatalf("查询表结构失败: %v", err)
	}
	defer rows.Close()

	fmt.Printf("%-15s %-20s %-10s %-10s %-15s %-10s\n", "Field", "Type", "Null", "Key", "Default", "Extra")
	fmt.Println("================================================================================")

	for rows.Next() {
		var field, fieldType, null, key, defaultVal, extra string
		if err := rows.Scan(&field, &fieldType, &null, &key, &defaultVal, &extra); err != nil {
			log.Printf("读取行失败: %v", err)
			continue
		}
		fmt.Printf("%-15s %-20s %-10s %-10s %-15s %-10s\n", field, fieldType, null, key, defaultVal, extra)
	}

	// 6. 测试字段更新
	fmt.Println("\n🧪 测试字段更新...")
	err = db.Exec("UPDATE model_configs SET api_secret = 'test-secret' WHERE name = 'qwen-vl-plus'").Error
	if err != nil {
		log.Printf("⚠️ 测试更新失败: %v", err)
	} else {
		fmt.Println("✅ 字段更新测试成功")

		// 恢复空值
		db.Exec("UPDATE model_configs SET api_secret = '' WHERE name = 'qwen-vl-plus'")
	}

	fmt.Println("\n🎉 API Secret字段添加完成！")
	fmt.Println("\n📋 使用说明:")
	fmt.Println("  - API Key: 主要的API密钥，通常以sk-开头")
	fmt.Println("  - API Secret: 配对的密钥，某些服务商（如阿里云）需要")
	fmt.Println("  - 两个字段都可以通过管理界面进行配置")
}
