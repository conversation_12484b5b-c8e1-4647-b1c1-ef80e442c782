package main

import (
	"fmt"
	"log"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/model"
	"solve_api/internal/repository"

	"gorm.io/gorm"
)

func main() {
	// 1. 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 2. 初始化数据库
	if err := database.InitDB(cfg); err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}

	db := database.GetDB()
	modelRepo := repository.NewModelConfigRepository(db)

	fmt.Println("🚀 开始初始化模型配置...")

	// 3. 初始化Qwen模型
	if err := initQwenModel(db, modelRepo); err != nil {
		log.Fatalf("初始化Qwen模型失败: %v", err)
	}

	// 4. 初始化DeepSeek模型
	if err := initDeepSeekModel(db, modelRepo); err != nil {
		log.Fatalf("初始化DeepSeek模型失败: %v", err)
	}

	fmt.Println("✅ 模型配置初始化完成！")
}

// initQwenModel 初始化Qwen识图模型
func initQwenModel(db *gorm.DB, modelRepo *repository.ModelConfigRepository) error {
	modelName := model.ModelNameQwenVLPlus

	// 检查是否已存在
	existing, err := modelRepo.GetByName(modelName)
	if err != nil {
		return fmt.Errorf("查询Qwen模型失败: %w", err)
	}

	if existing != nil {
		fmt.Printf("📋 Qwen模型已存在，ID: %d\n", existing.ID)
		// 确保状态为启用
		if existing.Status != model.ModelConfigStatusEnabled {
			if err := modelRepo.UpdateStatus(existing.ID, model.ModelConfigStatusEnabled); err != nil {
				return fmt.Errorf("更新Qwen模型状态失败: %w", err)
			}
			fmt.Println("🔄 已将Qwen模型状态更新为启用")
		}
		return nil
	}

	// 创建新的Qwen模型配置
	qwenModel := &model.ModelConfig{
		Name:   modelName,
		ApiURL: "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation",
		ApiKey: "sk-placeholder", // 需要管理员后续配置
		Status: model.ModelConfigStatusEnabled,
	}

	// 设置默认参数
	defaultParams := model.GetDefaultModelParams(modelName)
	if err := qwenModel.SetParamsMap(defaultParams); err != nil {
		return fmt.Errorf("设置Qwen默认参数失败: %w", err)
	}

	if err := modelRepo.Create(qwenModel); err != nil {
		return fmt.Errorf("创建Qwen模型失败: %w", err)
	}

	fmt.Printf("✅ 成功创建Qwen识图模型，ID: %d\n", qwenModel.ID)
	fmt.Printf("   - API URL: %s\n", qwenModel.ApiURL)
	fmt.Printf("   - 默认参数: %s\n", qwenModel.Params)

	return nil
}

// initDeepSeekModel 初始化DeepSeek解题模型
func initDeepSeekModel(db *gorm.DB, modelRepo *repository.ModelConfigRepository) error {
	modelName := model.ModelNameDeepseekChat

	// 检查是否已存在
	existing, err := modelRepo.GetByName(modelName)
	if err != nil {
		return fmt.Errorf("查询DeepSeek模型失败: %w", err)
	}

	if existing != nil {
		fmt.Printf("📋 DeepSeek模型已存在，ID: %d\n", existing.ID)
		// 确保状态为启用
		if existing.Status != model.ModelConfigStatusEnabled {
			if err := modelRepo.UpdateStatus(existing.ID, model.ModelConfigStatusEnabled); err != nil {
				return fmt.Errorf("更新DeepSeek模型状态失败: %w", err)
			}
			fmt.Println("🔄 已将DeepSeek模型状态更新为启用")
		}
		return nil
	}

	// 创建新的DeepSeek模型配置
	deepseekModel := &model.ModelConfig{
		Name:   modelName,
		ApiURL: "https://api.deepseek.com/v1/chat/completions",
		ApiKey: "sk-placeholder", // 需要管理员后续配置
		Status: model.ModelConfigStatusEnabled,
	}

	// 设置默认参数
	defaultParams := model.GetDefaultModelParams(modelName)
	if err := deepseekModel.SetParamsMap(defaultParams); err != nil {
		return fmt.Errorf("设置DeepSeek默认参数失败: %w", err)
	}

	if err := modelRepo.Create(deepseekModel); err != nil {
		return fmt.Errorf("创建DeepSeek模型失败: %w", err)
	}

	fmt.Printf("✅ 成功创建DeepSeek解题模型，ID: %d\n", deepseekModel.ID)
	fmt.Printf("   - API URL: %s\n", deepseekModel.ApiURL)
	fmt.Printf("   - 默认参数: %s\n", deepseekModel.Params)

	return nil
}
