package main

import (
	"fmt"
	"log"
	"solve_api/internal/config"
	"solve_api/internal/database"
)

func main() {
	fmt.Println("=== 修复管理员表结构 ===")

	// 加载配置
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 初始化数据库
	if err := database.InitMySQL(&cfg.Database.MySQL); err != nil {
		log.Fatalf("❌ 数据库初始化失败: %v", err)
	}

	db := database.GetDB()

	// 检查并删除username字段
	fmt.Println("1. 检查username字段...")
	var count int64
	err = db.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'go_solve' AND table_name = 'admins' AND column_name = 'username'").Scan(&count).Error
	if err != nil {
		log.Fatalf("❌ 检查字段失败: %v", err)
	}

	if count > 0 {
		fmt.Println("2. 删除username字段...")

		// 先删除唯一索引
		fmt.Println("   删除username唯一索引...")
		err = db.Exec("ALTER TABLE admins DROP INDEX IF EXISTS idx_admins_username").Error
		if err != nil {
			fmt.Printf("   ⚠️  删除索引失败（可能不存在）: %v\n", err)
		}

		// 删除字段
		fmt.Println("   删除username字段...")
		err = db.Exec("ALTER TABLE admins DROP COLUMN username").Error
		if err != nil {
			log.Fatalf("❌ 删除username字段失败: %v", err)
		}

		fmt.Println("✅ username字段删除成功")
	} else {
		fmt.Println("✅ username字段不存在，无需删除")
	}

	// 检查phone字段
	fmt.Println("3. 检查phone字段...")
	err = db.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = 'go_solve' AND table_name = 'admins' AND column_name = 'phone'").Scan(&count).Error
	if err != nil {
		log.Fatalf("❌ 检查phone字段失败: %v", err)
	}

	if count == 0 {
		fmt.Println("4. 添加phone字段...")
		err = db.Exec("ALTER TABLE admins ADD COLUMN phone varchar(20) NOT NULL UNIQUE").Error
		if err != nil {
			log.Fatalf("❌ 添加phone字段失败: %v", err)
		}
		fmt.Println("✅ phone字段添加成功")
	} else {
		fmt.Println("✅ phone字段已存在")
	}

	fmt.Println("✅ 管理员表结构修复完成")
}
