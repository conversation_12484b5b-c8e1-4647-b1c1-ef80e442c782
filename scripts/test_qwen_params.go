package main

import (
	"encoding/json"
	"fmt"
	"log"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/model"
)

func main() {
	fmt.Println("🧪 测试Qwen参数配置...")

	// 1. 加载配置
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 2. 初始化数据库
	if err := database.InitMySQL(&cfg.Database.MySQL); err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}
	defer database.CloseMySQL()

	db := database.GetDB()

	// 3. 获取Qwen模型配置
	var qwenConfig model.ModelConfig
	if err := db.Where("name = ?", "qwen-vl-plus").First(&qwenConfig).Error; err != nil {
		log.Fatalf("获取Qwen配置失败: %v", err)
	}

	fmt.Printf("📋 Qwen-VL-Plus 当前配置:\n")
	fmt.Printf("  模型名称: %s\n", qwenConfig.Name)
	fmt.Printf("  API地址: %s\n", qwenConfig.ApiURL)
	fmt.Printf("  API密钥: %s\n", maskApiKey(qwenConfig.ApiKey))
	fmt.Printf("  状态: %d (%s)\n", qwenConfig.Status, getStatusName(qwenConfig.Status))

	// 4. 解析参数
	params, err := qwenConfig.GetParamsMap()
	if err != nil {
		log.Fatalf("解析参数失败: %v", err)
	}

	fmt.Printf("\n🔧 参数详情:\n")
	for key, value := range params {
		fmt.Printf("  %s: %v\n", key, value)
	}

	// 5. 构建完整的Qwen请求体（模拟您提供的格式）
	qwenRequest := map[string]interface{}{
		"model": "qwen-vl-plus",
		"input": map[string]interface{}{
			"messages": []map[string]interface{}{
				{
					"role":    "system",
					"content": "精准且完整的识别题目内容，严格标准返回json格式。示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，",
				},
				{
					"role": "user",
					"content": []map[string]interface{}{
						{
							"image": "http://solve.igmdns.com/img/23.jpg",
						},
						{
							"text": "question_text内的值不应该出现题目类型以及问题序号。",
						},
					},
				},
			},
		},
		"parameters": params,
	}

	// 6. 输出完整请求体
	fmt.Printf("\n📤 完整Qwen请求体:\n")
	requestJSON, err := json.MarshalIndent(qwenRequest, "", "  ")
	if err != nil {
		log.Fatalf("序列化请求失败: %v", err)
	}
	fmt.Println(string(requestJSON))

	// 7. 参数说明
	fmt.Printf("\n📖 参数说明:\n")
	fmt.Printf("  temperature: %.2f - 控制输出随机性 (0=确定性, 2=最随机)\n", getFloat(params, "temperature"))
	fmt.Printf("  max_tokens: %d - 最大输出token数量\n", getInt(params, "max_tokens"))
	fmt.Printf("  top_p: %.2f - 核采样概率，控制词汇选择范围\n", getFloat(params, "top_p"))
	fmt.Printf("  top_k: %d - 从前K个最可能的词中选择\n", getInt(params, "top_k"))
	fmt.Printf("  do_sample: %t - 是否启用随机采样\n", getBool(params, "do_sample"))
	fmt.Printf("  frequency_penalty: %.1f - 频率惩罚，负值鼓励重复\n", getFloat(params, "frequency_penalty"))
	fmt.Printf("  presence_penalty: %.1f - 存在惩罚，负值鼓励重复话题\n", getFloat(params, "presence_penalty"))
	fmt.Printf("  detail: %s - 图像分析精度级别\n", getString(params, "detail"))

	fmt.Printf("\n🎯 配置建议:\n")
	fmt.Printf("  - 当前配置适合题目识别：确定性输出、高精度图像分析\n")
	fmt.Printf("  - temperature=0 确保输出稳定性\n")
	fmt.Printf("  - top_p=0.01 限制词汇选择范围，提高准确性\n")
	fmt.Printf("  - 负惩罚值鼓励结构化输出的一致性\n")

	fmt.Printf("\n✅ 测试完成！\n")
}

func maskApiKey(apiKey string) string {
	if len(apiKey) <= 8 {
		return apiKey
	}
	return apiKey[:8] + "****" + apiKey[len(apiKey)-4:]
}

func getStatusName(status int) string {
	switch status {
	case 1:
		return "启用"
	case 2:
		return "禁用"
	default:
		return "未知"
	}
}

func getFloat(params map[string]interface{}, key string) float64 {
	if val, ok := params[key]; ok {
		if f, ok := val.(float64); ok {
			return f
		}
	}
	return 0
}

func getInt(params map[string]interface{}, key string) int {
	if val, ok := params[key]; ok {
		if f, ok := val.(float64); ok {
			return int(f)
		}
	}
	return 0
}

func getBool(params map[string]interface{}, key string) bool {
	if val, ok := params[key]; ok {
		if b, ok := val.(bool); ok {
			return b
		}
	}
	return false
}

func getString(params map[string]interface{}, key string) string {
	if val, ok := params[key]; ok {
		if s, ok := val.(string); ok {
			return s
		}
	}
	return ""
}
