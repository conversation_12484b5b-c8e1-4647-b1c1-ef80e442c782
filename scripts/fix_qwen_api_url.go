package main

import (
	"fmt"
	"log"
	"solve_api/config"
	"solve_api/internal/database"
)

func main() {
	fmt.Println("🔧 修复Qwen API URL...")

	// 1. 加载配置
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 2. 初始化数据库
	if err := database.InitDB(cfg); err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}

	db := database.GetDB()

	// 3. 查看当前的API URL
	var currentURL string
	err = db.Raw("SELECT api_url FROM model_configs WHERE name = 'qwen-vl-plus'").Scan(&currentURL).Error
	if err != nil {
		log.Fatalf("查询当前API URL失败: %v", err)
	}

	fmt.Printf("📋 当前API URL: %s\n", currentURL)

	// 4. 更新为正确的OpenAI兼容端点
	correctURL := "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"

	result := db.Exec("UPDATE model_configs SET api_url = ? WHERE name = 'qwen-vl-plus'", correctURL)
	if result.Error != nil {
		log.Fatalf("更新API URL失败: %v", result.Error)
	}

	fmt.Printf("✅ API URL已更新为: %s\n", correctURL)
	fmt.Printf("📊 影响行数: %d\n", result.RowsAffected)

	// 5. 验证更新结果
	var newURL string
	err = db.Raw("SELECT api_url FROM model_configs WHERE name = 'qwen-vl-plus'").Scan(&newURL).Error
	if err != nil {
		log.Fatalf("验证更新失败: %v", err)
	}

	fmt.Printf("🔍 验证结果: %s\n", newURL)

	if newURL == correctURL {
		fmt.Println("🎉 API URL修复成功！")
	} else {
		fmt.Println("❌ API URL修复失败！")
	}
}
