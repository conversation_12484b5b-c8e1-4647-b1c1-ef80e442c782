package main

import (
	"context"
	"fmt"
	"log"
	"solve_api/internal/config"
	"solve_api/internal/database"
	"solve_api/internal/model"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
)

// 数据库连接测试工具
func main() {
	fmt.Println("=== 数据库连接测试工具 ===")

	// 1. 加载配置
	fmt.Println("\n--- 加载配置 ---")
	cfg, err := config.LoadConfig("config/config.yaml")
	if err != nil {
		log.Fatalf("❌ 配置加载失败: %v", err)
	}
	fmt.Printf("✅ 配置加载成功\n")
	fmt.Printf("   MySQL: %s:%d\n", cfg.Database.MySQL.Host, cfg.Database.MySQL.Port)
	fmt.Printf("   Redis: %s:%d\n", cfg.Redis.Host, cfg.Redis.Port)

	// 2. 测试MySQL连接
	testMySQL(cfg)

	// 3. 测试Redis连接
	testRedis(cfg)

	// 4. 测试数据库表创建
	testTableCreation()

	fmt.Println("\n=== 测试完成 ===")
}

// 测试MySQL连接
func testMySQL(cfg *config.Config) {
	fmt.Println("\n--- 测试MySQL连接 ---")

	// 显示连接信息
	dsn := cfg.Database.MySQL.GetDSN()
	fmt.Printf("连接字符串: %s\n", maskPassword(dsn))

	// 尝试连接
	err := database.InitMySQL(&cfg.Database.MySQL)
	if err != nil {
		fmt.Printf("❌ MySQL连接失败: %v\n", err)
		return
	}
	defer database.CloseMySQL()

	fmt.Printf("✅ MySQL连接成功\n")

	// 测试数据库操作
	db := database.GetDB()

	// 获取数据库版本
	var version string
	err = db.Raw("SELECT VERSION()").Scan(&version).Error
	if err != nil {
		fmt.Printf("❌ 查询数据库版本失败: %v\n", err)
	} else {
		fmt.Printf("   数据库版本: %s\n", version)
	}

	// 检查数据库名称
	var dbName string
	err = db.Raw("SELECT DATABASE()").Scan(&dbName).Error
	if err != nil {
		fmt.Printf("❌ 查询当前数据库失败: %v\n", err)
	} else {
		fmt.Printf("   当前数据库: %s\n", dbName)
	}

	// 检查现有表
	var tables []string
	err = db.Raw("SHOW TABLES").Scan(&tables).Error
	if err != nil {
		fmt.Printf("❌ 查询表列表失败: %v\n", err)
	} else {
		fmt.Printf("   现有表数量: %d\n", len(tables))
		if len(tables) > 0 {
			fmt.Printf("   表列表: %v\n", tables)
		}
	}
}

// 测试Redis连接
func testRedis(cfg *config.Config) {
	fmt.Println("\n--- 测试Redis连接 ---")

	// 显示连接信息
	fmt.Printf("Redis地址: %s\n", cfg.Redis.GetRedisAddr())

	// 尝试连接
	err := database.InitRedis(&cfg.Redis)
	if err != nil {
		fmt.Printf("❌ Redis连接失败: %v\n", err)
		return
	}
	defer database.CloseRedis()

	fmt.Printf("✅ Redis连接成功\n")

	// 测试Redis操作
	rdb := database.GetRedis()
	ctx := context.Background()

	// 测试PING
	pong, err := rdb.Ping(ctx).Result()
	if err != nil {
		fmt.Printf("❌ Redis PING失败: %v\n", err)
	} else {
		fmt.Printf("   PING响应: %s\n", pong)
	}

	// 获取Redis信息
	info, err := rdb.Info(ctx, "server").Result()
	if err != nil {
		fmt.Printf("❌ 获取Redis信息失败: %v\n", err)
	} else {
		// 解析版本信息
		lines := parseRedisInfo(info)
		if version, ok := lines["redis_version"]; ok {
			fmt.Printf("   Redis版本: %s\n", version)
		}
		if mode, ok := lines["redis_mode"]; ok {
			fmt.Printf("   Redis模式: %s\n", mode)
		}
	}

	// 测试读写操作
	testKey := "test_connection_" + fmt.Sprintf("%d", time.Now().Unix())
	err = rdb.Set(ctx, testKey, "test_value", time.Minute).Err()
	if err != nil {
		fmt.Printf("❌ Redis写入测试失败: %v\n", err)
	} else {
		val, err := rdb.Get(ctx, testKey).Result()
		if err != nil {
			fmt.Printf("❌ Redis读取测试失败: %v\n", err)
		} else {
			fmt.Printf("   读写测试: %s\n", val)
			// 清理测试数据
			rdb.Del(ctx, testKey)
		}
	}
}

// 测试数据库表创建
func testTableCreation() {
	fmt.Println("\n--- 测试数据库表创建 ---")

	db := database.GetDB()
	if db == nil {
		fmt.Printf("❌ 数据库连接不可用\n")
		return
	}

	// 执行自动迁移
	fmt.Printf("正在执行数据库迁移...\n")
	err := db.AutoMigrate(
		&model.User{},
		&model.SystemConfig{},
		&model.Admin{},
	)
	if err != nil {
		fmt.Printf("❌ 数据库迁移失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 数据库迁移成功\n")

	// 检查表是否创建成功
	tables := []string{"users", "system_configs", "admins"}
	for _, table := range tables {
		var count int64
		err := db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?", table).Scan(&count).Error
		if err != nil {
			fmt.Printf("❌ 检查表 %s 失败: %v\n", table, err)
		} else if count > 0 {
			fmt.Printf("   ✅ 表 %s 存在\n", table)

			// 检查表中的记录数
			var recordCount int64
			err = db.Table(table).Count(&recordCount).Error
			if err != nil {
				fmt.Printf("      ❌ 查询记录数失败: %v\n", err)
			} else {
				fmt.Printf("      记录数: %d\n", recordCount)
			}
		} else {
			fmt.Printf("   ❌ 表 %s 不存在\n", table)
		}
	}
}

// 解析Redis INFO响应
func parseRedisInfo(info string) map[string]string {
	result := make(map[string]string)
	lines := strings.Split(info, "\r\n")
	for _, line := range lines {
		if strings.Contains(line, ":") {
			parts := strings.SplitN(line, ":", 2)
			if len(parts) == 2 {
				result[parts[0]] = parts[1]
			}
		}
	}
	return result
}

// 屏蔽密码显示
func maskPassword(dsn string) string {
	// 简单的密码屏蔽，将密码部分替换为***
	if idx := strings.Index(dsn, ":"); idx != -1 {
		if idx2 := strings.Index(dsn[idx+1:], "@"); idx2 != -1 {
			return dsn[:idx+1] + "***" + dsn[idx+1+idx2:]
		}
	}
	return dsn
}
