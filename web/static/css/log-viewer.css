/* 日志查看器样式 */

/* 全局样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.2rem;
}

.navbar-text {
    font-size: 0.9rem;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* 侧边栏样式 */
.list-group-item {
    border: none;
    border-radius: 6px !important;
    margin-bottom: 2px;
    transition: all 0.2s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
}

.list-group-item.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.list-group-item .badge {
    font-size: 0.75rem;
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-sm {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

/* 统计卡片样式 */
.stats-card {
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-card h3 {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}

.stats-card h5 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.85rem;
    padding: 0.75rem 0.5rem;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    font-size: 0.85rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,0.02);
}

/* 状态码样式 */
.status-success {
    color: #198754;
    font-weight: 600;
}

.status-error {
    color: #dc3545;
    font-weight: 600;
}

.status-warning {
    color: #fd7e14;
    font-weight: 600;
}

/* 响应时间样式 */
.response-time-fast {
    color: #198754;
}

.response-time-medium {
    color: #fd7e14;
}

.response-time-slow {
    color: #dc3545;
}

/* 系统日志样式 */
#system-logs-content {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.log-line {
    margin-bottom: 2px;
    padding: 2px 0;
}

.log-info {
    color: #17a2b8;
}

.log-warn {
    color: #ffc107;
}

.log-error {
    color: #dc3545;
    font-weight: bold;
}

.log-debug {
    color: #6c757d;
}

/* 分页样式 */
.pagination {
    margin-bottom: 0;
}

.page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #0d6efd;
}

.page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* 模态框样式 */
.modal-content {
    border-radius: 8px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
}

/* 加载动画 */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.8rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.8rem;
    }
    
    .btn {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }
    
    .stats-card h3 {
        font-size: 1.5rem;
    }
    
    .navbar-brand {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .col-md-3 {
        margin-bottom: 1rem;
    }
    
    .table th, .table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.75rem;
    }
    
    .btn-sm {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
    
    #system-logs-content {
        font-size: 0.7rem;
        height: 400px;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

/* 高亮效果 */
.highlight {
    background-color: #fff3cd;
    padding: 2px 4px;
    border-radius: 3px;
    animation: fadeIn 0.5s ease-in-out;
}

/* 错误提示样式 */
.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* 成功提示样式 */
.success-message {
    color: #198754;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h5 {
    margin-bottom: 0.5rem;
    color: #495057;
}

.empty-state p {
    margin-bottom: 0;
    font-size: 0.9rem;
}
