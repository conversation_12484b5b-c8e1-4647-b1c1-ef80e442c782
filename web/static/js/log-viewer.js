// 日志查看器JavaScript

class LogViewer {
    constructor() {
        this.currentLogType = 'api';
        this.currentPage = 1;
        this.pageSize = 20;
        this.autoRefreshInterval = null;
        this.autoRefreshEnabled = false;
        this.adminToken = localStorage.getItem('admin_token') || '';
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateCurrentTime();
        this.setDefaultDates();
        this.loadLogs();
        
        // 每秒更新时间
        setInterval(() => this.updateCurrentTime(), 1000);
    }

    bindEvents() {
        // 日志类型切换
        document.querySelectorAll('[data-log-type]').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchLogType(e.target.dataset.logType);
            });
        });

        // 过滤表单提交
        document.getElementById('filter-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.currentPage = 1;
            this.loadLogs();
        });

        // 重置过滤器
        document.getElementById('reset-filters').addEventListener('click', () => {
            this.resetFilters();
        });

        // 刷新日志
        document.getElementById('refresh-logs').addEventListener('click', () => {
            this.loadLogs();
        });

        // 自动刷新切换
        document.getElementById('auto-refresh-toggle').addEventListener('click', () => {
            this.toggleAutoRefresh();
        });

        // 导出日志
        document.getElementById('export-logs').addEventListener('click', () => {
            this.exportLogs();
        });

        // 清理日志
        document.getElementById('clean-logs').addEventListener('click', () => {
            this.showCleanLogsModal();
        });

        // 确认清理
        document.getElementById('confirm-clean').addEventListener('click', () => {
            this.cleanLogs();
        });
    }

    updateCurrentTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        document.getElementById('current-time').textContent = timeString;
    }

    setDefaultDates() {
        const today = new Date();
        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        
        document.querySelector('input[name="start_date"]').value = weekAgo.toISOString().split('T')[0];
        document.querySelector('input[name="end_date"]').value = today.toISOString().split('T')[0];
    }

    switchLogType(logType) {
        this.currentLogType = logType;
        this.currentPage = 1;

        // 更新导航状态
        document.querySelectorAll('[data-log-type]').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-log-type="${logType}"]`).classList.add('active');

        // 切换过滤器显示
        if (logType === 'api') {
            document.getElementById('api-filters').style.display = 'block';
            document.getElementById('system-filters').style.display = 'none';
            document.getElementById('stats-section').style.display = 'block';
            document.getElementById('api-logs-table').style.display = 'block';
            document.getElementById('system-logs-display').style.display = 'none';
            document.getElementById('pagination-nav').style.display = 'block';
            document.getElementById('log-type-title').textContent = 'API日志';
        } else {
            document.getElementById('api-filters').style.display = 'none';
            document.getElementById('system-filters').style.display = 'block';
            document.getElementById('stats-section').style.display = 'none';
            document.getElementById('api-logs-table').style.display = 'none';
            document.getElementById('system-logs-display').style.display = 'block';
            document.getElementById('pagination-nav').style.display = 'none';
            document.getElementById('log-type-title').textContent = '系统日志';
        }

        this.loadLogs();
    }

    getFilters() {
        const form = document.getElementById('filter-form');
        const formData = new FormData(form);
        const filters = {};

        for (let [key, value] of formData.entries()) {
            if (value.trim() !== '') {
                filters[key] = value.trim();
            }
        }

        return filters;
    }

    resetFilters() {
        document.getElementById('filter-form').reset();
        this.setDefaultDates();
        this.currentPage = 1;
        this.loadLogs();
    }

    async loadLogs() {
        this.showLoading(true);

        try {
            if (this.currentLogType === 'api') {
                await this.loadAPILogs();
            } else {
                await this.loadSystemLogs();
            }
        } catch (error) {
            console.error('加载日志失败:', error);
            this.showError('加载日志失败: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    async loadAPILogs() {
        const filters = this.getFilters();
        const params = new URLSearchParams({
            page: this.currentPage,
            page_size: this.pageSize,
            ...filters
        });

        const response = await this.apiRequest(`/api/v1/admin/logs/api?${params}`);
        
        if (response.code === 200) {
            this.renderAPILogs(response.data);
            await this.loadAPIStats();
        } else {
            throw new Error(response.message);
        }
    }

    async loadSystemLogs() {
        const filters = this.getFilters();
        const params = new URLSearchParams(filters);

        const response = await this.apiRequest(`/api/v1/admin/logs/system?${params}`);
        
        if (response.code === 200) {
            this.renderSystemLogs(response.data);
        } else {
            throw new Error(response.message);
        }
    }

    async loadAPIStats() {
        const filters = this.getFilters();
        const params = new URLSearchParams(filters);

        try {
            const response = await this.apiRequest(`/api/v1/admin/logs/api/stats?${params}`);
            
            if (response.code === 200) {
                this.renderAPIStats(response.data);
            }
        } catch (error) {
            console.error('加载统计失败:', error);
        }
    }

    renderAPILogs(data) {
        const tbody = document.getElementById('api-logs-tbody');
        tbody.innerHTML = '';

        if (data.list && data.list.length > 0) {
            data.list.forEach(log => {
                const row = this.createAPILogRow(log);
                tbody.appendChild(row);
            });
        } else {
            tbody.innerHTML = `
                <tr>
                    <td colspan="11" class="text-center text-muted py-4">
                        <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                        <div class="mt-2">暂无日志数据</div>
                    </td>
                </tr>
            `;
        }

        // 更新计数和分页
        document.getElementById('log-count').textContent = `共 ${data.total || 0} 条`;
        document.getElementById('api-log-count').textContent = data.total || 0;
        this.renderPagination(data.total || 0, data.page || 1, data.page_size || this.pageSize);
    }

    createAPILogRow(log) {
        const row = document.createElement('tr');
        row.className = 'fade-in';
        
        const statusClass = log.status_code >= 200 && log.status_code < 300 ? 'status-success' : 'status-error';
        const responseTimeClass = this.getResponseTimeClass(log.response_time);
        
        row.innerHTML = `
            <td>${log.id}</td>
            <td>${this.formatDateTime(log.created_at)}</td>
            <td>${log.user_id}</td>
            <td>${log.app_id}</td>
            <td><span class="badge bg-secondary">${log.method}</span></td>
            <td class="text-truncate" style="max-width: 150px;" title="${log.path}">${log.path}</td>
            <td><span class="${statusClass}">${log.status_code}</span></td>
            <td class="${responseTimeClass}">${log.response_time}ms</td>
            <td>¥${log.cost.toFixed(4)}</td>
            <td>${log.client_ip}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="logViewer.showLogDetail(${log.id})">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
        
        return row;
    }

    renderSystemLogs(data) {
        const content = document.getElementById('system-logs-content');
        
        if (data.logs && data.logs.length > 0) {
            const logText = data.logs.map(line => this.formatLogLine(line)).join('\n');
            content.textContent = logText;
        } else {
            content.textContent = '暂无系统日志';
        }

        // 更新计数
        document.getElementById('log-count').textContent = `共 ${data.total || 0} 行`;
        document.getElementById('system-log-count').textContent = data.total || 0;
        
        // 滚动到底部
        content.scrollTop = content.scrollHeight;
    }

    renderAPIStats(stats) {
        document.getElementById('total-calls').textContent = stats.total_calls || 0;
        document.getElementById('success-calls').textContent = stats.success_calls || 0;
        document.getElementById('error-calls').textContent = stats.error_calls || 0;
        document.getElementById('success-rate').textContent = (stats.success_rate || 0).toFixed(1) + '%';
    }

    renderPagination(total, currentPage, pageSize) {
        const totalPages = Math.ceil(total / pageSize);
        const pagination = document.getElementById('pagination');
        pagination.innerHTML = '';

        if (totalPages <= 1) return;

        // 上一页
        const prevItem = document.createElement('li');
        prevItem.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
        prevItem.innerHTML = `
            <a class="page-link" href="#" data-page="${currentPage - 1}">
                <i class="bi bi-chevron-left"></i>
            </a>
        `;
        pagination.appendChild(prevItem);

        // 页码
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const pageItem = document.createElement('li');
            pageItem.className = `page-item ${i === currentPage ? 'active' : ''}`;
            pageItem.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
            pagination.appendChild(pageItem);
        }

        // 下一页
        const nextItem = document.createElement('li');
        nextItem.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
        nextItem.innerHTML = `
            <a class="page-link" href="#" data-page="${currentPage + 1}">
                <i class="bi bi-chevron-right"></i>
            </a>
        `;
        pagination.appendChild(nextItem);

        // 绑定分页事件
        pagination.addEventListener('click', (e) => {
            e.preventDefault();
            if (e.target.dataset.page && !e.target.closest('.disabled')) {
                this.currentPage = parseInt(e.target.dataset.page);
                this.loadLogs();
            }
        });
    }

    getResponseTimeClass(responseTime) {
        if (responseTime < 100) return 'response-time-fast';
        if (responseTime < 500) return 'response-time-medium';
        return 'response-time-slow';
    }

    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    formatLogLine(line) {
        // 简单的日志格式化，可以根据需要扩展
        return line;
    }

    async showLogDetail(logId) {
        try {
            const response = await this.apiRequest(`/api/v1/admin/logs/api/${logId}`);
            
            if (response.code === 200) {
                this.renderLogDetail(response.data);
                const modal = new bootstrap.Modal(document.getElementById('logDetailModal'));
                modal.show();
            } else {
                throw new Error(response.message);
            }
        } catch (error) {
            console.error('获取日志详情失败:', error);
            this.showError('获取日志详情失败: ' + error.message);
        }
    }

    renderLogDetail(log) {
        const content = document.getElementById('log-detail-content');
        content.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr><td>ID</td><td>${log.id}</td></tr>
                        <tr><td>用户ID</td><td>${log.user_id}</td></tr>
                        <tr><td>应用ID</td><td>${log.app_id}</td></tr>
                        <tr><td>请求方法</td><td><span class="badge bg-secondary">${log.method}</span></td></tr>
                        <tr><td>请求路径</td><td>${log.path}</td></tr>
                        <tr><td>状态码</td><td><span class="${log.status_code >= 200 && log.status_code < 300 ? 'status-success' : 'status-error'}">${log.status_code}</span></td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>性能信息</h6>
                    <table class="table table-sm">
                        <tr><td>响应时间</td><td>${log.response_time}ms</td></tr>
                        <tr><td>请求大小</td><td>${this.formatBytes(log.request_size)}</td></tr>
                        <tr><td>响应大小</td><td>${this.formatBytes(log.response_size)}</td></tr>
                        <tr><td>费用</td><td>¥${log.cost.toFixed(4)}</td></tr>
                        <tr><td>客户端IP</td><td>${log.client_ip}</td></tr>
                        <tr><td>创建时间</td><td>${new Date(log.created_at).toLocaleString('zh-CN')}</td></tr>
                    </table>
                </div>
            </div>
            ${log.error_msg ? `
                <div class="mt-3">
                    <h6>错误信息</h6>
                    <div class="alert alert-danger">
                        <pre class="mb-0">${log.error_msg}</pre>
                    </div>
                </div>
            ` : ''}
            ${log.user_agent ? `
                <div class="mt-3">
                    <h6>User Agent</h6>
                    <div class="alert alert-info">
                        <small>${log.user_agent}</small>
                    </div>
                </div>
            ` : ''}
        `;
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    toggleAutoRefresh() {
        const button = document.getElementById('auto-refresh-toggle');

        if (this.autoRefreshEnabled) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshEnabled = false;
            button.innerHTML = '<i class="bi bi-arrow-repeat"></i> 自动刷新';
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-primary');
        } else {
            this.autoRefreshInterval = setInterval(() => {
                this.loadLogs();
            }, 30000); // 30秒刷新一次
            this.autoRefreshEnabled = true;
            button.innerHTML = '<i class="bi bi-pause"></i> 停止刷新';
            button.classList.remove('btn-outline-primary');
            button.classList.add('btn-success');
        }
    }

    async exportLogs() {
        try {
            const filters = this.getFilters();
            const params = new URLSearchParams({
                format: 'csv',
                ...filters
            });

            // 创建下载链接
            const url = `/api/v1/admin/logs/api/export?${params}`;
            const link = document.createElement('a');
            link.href = url;
            link.download = `api_logs_${new Date().toISOString().split('T')[0]}.csv`;

            // 添加认证头（通过隐藏表单提交）
            const form = document.createElement('form');
            form.method = 'GET';
            form.action = url;
            form.style.display = 'none';

            // 添加token参数
            const tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = 'token';
            tokenInput.value = this.adminToken;
            form.appendChild(tokenInput);

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);

            this.showSuccess('日志导出已开始，请稍候下载');
        } catch (error) {
            console.error('导出日志失败:', error);
            this.showError('导出日志失败: ' + error.message);
        }
    }

    showCleanLogsModal() {
        const modal = new bootstrap.Modal(document.getElementById('cleanLogsModal'));
        modal.show();
    }

    async cleanLogs() {
        try {
            const form = document.getElementById('clean-logs-form');
            const formData = new FormData(form);
            const days = parseInt(formData.get('days'));

            if (!days || days < 1) {
                this.showError('请输入有效的保留天数');
                return;
            }

            const response = await this.apiRequest('/api/v1/admin/logs/api/clean', {
                method: 'POST',
                body: JSON.stringify({ days })
            });

            if (response.code === 200) {
                this.showSuccess('日志清理成功');
                const modal = bootstrap.Modal.getInstance(document.getElementById('cleanLogsModal'));
                modal.hide();
                this.loadLogs(); // 重新加载日志
            } else {
                throw new Error(response.message);
            }
        } catch (error) {
            console.error('清理日志失败:', error);
            this.showError('清理日志失败: ' + error.message);
        }
    }

    async apiRequest(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.adminToken}`
            }
        };

        const finalOptions = { ...defaultOptions, ...options };

        if (finalOptions.headers) {
            finalOptions.headers = { ...defaultOptions.headers, ...finalOptions.headers };
        }

        const response = await fetch(url, finalOptions);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    }

    showLoading(show) {
        const loading = document.getElementById('loading');
        if (show) {
            loading.style.display = 'block';
        } else {
            loading.style.display = 'none';
        }
    }

    showError(message) {
        this.showToast(message, 'error');
    }

    showSuccess(message) {
        this.showToast(message, 'success');
    }

    showToast(message, type = 'info') {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="bi bi-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        // 添加到页面
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }

        toastContainer.appendChild(toast);

        // 显示toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: type === 'error' ? 5000 : 3000
        });
        bsToast.show();

        // 自动移除
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    // 设置管理员token
    setAdminToken(token) {
        this.adminToken = token;
        localStorage.setItem('admin_token', token);
    }

    // 获取管理员token
    getAdminToken() {
        return this.adminToken || localStorage.getItem('admin_token') || '';
    }

    // 检查是否已登录
    checkAuth() {
        if (!this.getAdminToken()) {
            this.showError('请先登录管理员账户');
            // 可以重定向到登录页面
            // window.location.href = '/admin/login';
            return false;
        }
        return true;
    }
}

// 全局实例
let logViewer;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    logViewer = new LogViewer();

    // 检查是否有token参数（用于从其他页面跳转）
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    if (token) {
        logViewer.setAdminToken(token);
        // 清除URL中的token参数
        window.history.replaceState({}, document.title, window.location.pathname);
    }

    // 检查认证状态
    if (!logViewer.checkAuth()) {
        // 显示登录提示
        document.querySelector('.container-fluid').innerHTML = `
            <div class="row justify-content-center mt-5">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="bi bi-shield-lock" style="font-size: 3rem; color: #dc3545;"></i>
                            <h4 class="mt-3">需要管理员权限</h4>
                            <p class="text-muted">请先登录管理员账户才能查看日志</p>
                            <div class="mt-4">
                                <input type="password" class="form-control mb-3" id="token-input" placeholder="请输入管理员Token">
                                <button class="btn btn-primary" onclick="loginWithToken()">登录</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
});

// 使用token登录
function loginWithToken() {
    const token = document.getElementById('token-input').value.trim();
    if (token) {
        logViewer.setAdminToken(token);
        location.reload();
    } else {
        logViewer.showError('请输入有效的Token');
    }
}

// 导出到全局作用域，供HTML中的onclick使用
window.logViewer = logViewer;
window.loginWithToken = loginWithToken;
