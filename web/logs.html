<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI模型原始数据日志查看器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .controls {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-2px);
        }

        .status-info {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 14px;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-online {
            background: #d4edda;
            color: #155724;
        }

        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }

        .main-content {
            padding: 30px;
        }

        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .model-section {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            overflow: hidden;
        }

        .model-header {
            padding: 15px 20px;
            font-weight: 600;
            font-size: 16px;
            color: white;
        }

        .qwen-header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .deepseek-header {
            background: linear-gradient(135deg, #4834d4, #686de0);
        }

        .data-section {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .data-section:last-child {
            border-bottom: none;
        }

        .data-title {
            font-size: 14px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;
        }

        .data-title-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .data-title-right {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .copy-btn {
            padding: 2px 6px;
            font-size: 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.2s;
        }

        .copy-btn:hover {
            opacity: 1;
        }

        .data-info {
            font-size: 10px;
            color: #6c757d;
            font-weight: normal;
        }

        .data-content {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-wrap: break-word;
            max-height: 300px;
            overflow-y: auto;
            color: #212529;
        }

        .data-content.empty {
            color: #6c757d;
            font-style: italic;
            text-align: center;
            padding: 30px;
        }

        .logs-list {
            margin-top: 30px;
        }

        .logs-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .logs-header h3 {
            color: #495057;
            font-size: 18px;
        }

        .log-entry {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            margin-bottom: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .log-entry:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .log-entry-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .log-timestamp {
            font-size: 14px;
            color: #6c757d;
            font-weight: 500;
        }

        .log-id {
            font-size: 12px;
            color: #007bff;
            background: #e7f3ff;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .scrollbar-custom {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e0 #f7fafc;
        }

        .scrollbar-custom::-webkit-scrollbar {
            width: 8px;
        }

        .scrollbar-custom::-webkit-scrollbar-track {
            background: #f7fafc;
            border-radius: 4px;
        }

        .scrollbar-custom::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 4px;
        }

        .scrollbar-custom::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        @media (max-width: 768px) {
            .comparison-container {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .control-group {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 AI模型原始数据日志查看器</h1>
            <p>实时查看Qwen与DeepSeek的原始请求/响应数据，便于对比分析模型差异</p>
            <p style="font-size: 14px; opacity: 0.8; margin-top: 10px;">
                ⚠️ 注意：此页面显示的是完全未经处理的原始数据，不进行任何JSON格式化或解析，确保数据的完整性和准确性
            </p>
        </div>

        <div class="controls">
            <div class="control-group">
                <button class="btn btn-primary" onclick="refreshLogs()">🔄 刷新日志</button>
                <button class="btn btn-danger" onclick="clearLogs()">🗑️ 清空日志</button>
                <button class="btn btn-success" onclick="exportLogs()">📥 导出日志</button>
            </div>
            <div class="status-info">
                <span>状态:</span>
                <span class="status-badge status-online" id="connectionStatus">🟢 已连接</span>
                <span>日志数量: <strong id="logCount">0</strong></span>
                <span>最后更新: <strong id="lastUpdate">--</strong></span>
            </div>
        </div>

        <div class="main-content">
            <!-- 当前对比数据 -->
            <div class="comparison-container" id="currentComparison" style="display: none;">
                <!-- Qwen 数据 -->
                <div class="model-section">
                    <div class="model-header qwen-header">
                        🎯 Qwen-VL 数据
                    </div>
                    <div class="data-section">
                        <div class="data-title">
                            <div class="data-title-left">
                                <span>📤 发送给Qwen的原始请求数据</span>
                                <span class="data-info" id="qwenRequestInfo"></span>
                            </div>
                            <div class="data-title-right">
                                <button class="copy-btn" onclick="copyToClipboard('qwenRequest')">复制</button>
                            </div>
                        </div>
                        <div class="data-content scrollbar-custom" id="qwenRequest">暂无数据</div>
                    </div>
                    <div class="data-section">
                        <div class="data-title">
                            <div class="data-title-left">
                                <span>📥 Qwen返回的Content数据</span>
                                <span class="data-info" id="qwenResponseInfo"></span>
                            </div>
                            <div class="data-title-right">
                                <button class="copy-btn" onclick="copyToClipboard('qwenResponse')">复制</button>
                            </div>
                        </div>
                        <div class="data-content scrollbar-custom" id="qwenResponse">暂无数据</div>
                    </div>
                </div>

                <!-- DeepSeek 数据 -->
                <div class="model-section">
                    <div class="model-header deepseek-header">
                        🚀 DeepSeek 数据
                    </div>
                    <div class="data-section">
                        <div class="data-title">
                            <div class="data-title-left">
                                <span>📤 发送给DeepSeek的原始请求数据</span>
                                <span class="data-info" id="deepseekRequestInfo"></span>
                            </div>
                            <div class="data-title-right">
                                <button class="copy-btn" onclick="copyToClipboard('deepseekRequest')">复制</button>
                            </div>
                        </div>
                        <div class="data-content scrollbar-custom" id="deepseekRequest">暂无数据</div>
                    </div>
                    <div class="data-section">
                        <div class="data-title">
                            <div class="data-title-left">
                                <span>📥 DeepSeek返回的Content数据</span>
                                <span class="data-info" id="deepseekResponseInfo"></span>
                            </div>
                            <div class="data-title-right">
                                <button class="copy-btn" onclick="copyToClipboard('deepseekResponse')">复制</button>
                            </div>
                        </div>
                        <div class="data-content scrollbar-custom" id="deepseekResponse">暂无数据</div>
                    </div>
                </div>
            </div>

            <!-- 历史日志列表 -->
            <div class="logs-list">
                <div class="logs-header">
                    <h3>📋 历史日志记录</h3>
                    <div class="control-group">
                        <button class="btn btn-primary" onclick="toggleAutoRefresh()">
                            <span id="autoRefreshText">🔄 开启自动刷新</span>
                        </button>
                    </div>
                </div>
                <div id="logsList">
                    <div class="empty-state">
                        <div style="font-size: 48px; margin-bottom: 20px;">📝</div>
                        <h4>暂无日志数据</h4>
                        <p>请先进行拍照搜题测试以生成日志数据</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let logs = [];
        let autoRefreshInterval = null;
        let isAutoRefresh = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshLogs();
        });

        // 刷新日志
        function refreshLogs() {
            updateLastUpdateTime();

            // 从后端API获取日志数据
            fetch('/api/v1/logs/ai?limit=50')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        logs = data.data.logs || [];
                        renderLogs();
                        updateLogCount();
                        updateConnectionStatus(true);
                    } else {
                        console.error('获取日志失败:', data.message);
                        updateConnectionStatus(false);
                    }
                })
                .catch(error => {
                    console.error('获取日志失败:', error);
                    updateConnectionStatus(false);
                });
        }

        // 清空日志
        function clearLogs() {
            if (confirm('确定要清空所有日志吗？此操作不可恢复。')) {
                fetch('/api/v1/logs/ai/clear', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        logs = [];
                        renderLogs();
                        updateLogCount();
                        clearCurrentComparison();
                        alert('日志已清空');
                    } else {
                        alert('清空日志失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('清空日志失败:', error);
                    alert('清空日志失败');
                });
            }
        }

        // 导出日志
        function exportLogs() {
            if (logs.length === 0) {
                alert('暂无日志数据可导出');
                return;
            }

            // 创建JSON文件并下载
            const dataStr = JSON.stringify(logs, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'ai-logs-' + new Date().toISOString().slice(0, 19).replace(/:/g, '-') + '.json';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            if (isAutoRefresh) {
                clearInterval(autoRefreshInterval);
                isAutoRefresh = false;
                document.getElementById('autoRefreshText').textContent = '🔄 开启自动刷新';
            } else {
                autoRefreshInterval = setInterval(refreshLogs, 5000); // 每5秒刷新
                isAutoRefresh = true;
                document.getElementById('autoRefreshText').textContent = '⏸️ 关闭自动刷新';
            }
        }

        // 渲染日志列表
        function renderLogs() {
            const logsList = document.getElementById('logsList');
            
            if (logs.length === 0) {
                logsList.innerHTML = `
                    <div class="empty-state">
                        <div style="font-size: 48px; margin-bottom: 20px;">📝</div>
                        <h4>暂无日志数据</h4>
                        <p>请先进行拍照搜题测试以生成日志数据</p>
                    </div>
                `;
                return;
            }

            const logsHtml = logs.map((log, index) => {
                const hasQwen = log.qwen && (log.qwen.request || log.qwen.response);
                const hasDeepSeek = log.deepseek && (log.deepseek.request || log.deepseek.response);
                const statusText = hasQwen && hasDeepSeek ? '完整数据' :
                                  hasQwen ? '仅Qwen数据' :
                                  hasDeepSeek ? '仅DeepSeek数据' : '无数据';

                return `
                    <div class="log-entry" onclick="showLogDetails(${index})">
                        <div class="log-entry-header">
                            <div class="log-timestamp">${log.timestamp}</div>
                            <div class="log-id">ID: ${log.id} | ${statusText}</div>
                        </div>
                    </div>
                `;
            }).join('');

            logsList.innerHTML = logsHtml;
        }

        // 显示日志详情
        function showLogDetails(index) {
            const log = logs[index];
            if (!log) {
                console.error('日志不存在:', index);
                return;
            }

            console.log('显示日志详情:', log);

            // 显示对比区域
            document.getElementById('currentComparison').style.display = 'grid';

            // 更新Qwen数据 - 确保显示最原始的数据
            const qwenRequest = log.qwen?.request || '暂无数据';
            const qwenResponse = log.qwen?.response || '暂无数据';

            console.log('Qwen请求数据类型:', typeof qwenRequest, '长度:', qwenRequest.length);
            console.log('Qwen响应数据类型:', typeof qwenResponse, '长度:', qwenResponse.length);

            updateDataContent('qwenRequest', qwenRequest);
            updateDataContent('qwenResponse', qwenResponse);

            // 更新DeepSeek数据 - 确保显示最原始的数据
            const deepseekRequest = log.deepseek?.request || '暂无数据';
            const deepseekResponse = log.deepseek?.response || '暂无数据';

            console.log('DeepSeek请求数据类型:', typeof deepseekRequest, '长度:', deepseekRequest.length);
            console.log('DeepSeek响应数据类型:', typeof deepseekResponse, '长度:', deepseekResponse.length);

            updateDataContent('deepseekRequest', deepseekRequest);
            updateDataContent('deepseekResponse', deepseekResponse);

            // 滚动到对比区域
            document.getElementById('currentComparison').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            console.log('日志详情显示完成');
        }

        // 更新数据内容 - 显示最原始的数据，不进行任何格式化
        function updateDataContent(elementId, content) {
            const element = document.getElementById(elementId);
            const infoElementId = elementId + 'Info';
            const infoElement = document.getElementById(infoElementId);

            if (content && content !== '暂无数据') {
                // 直接显示原始字符串，不进行任何JSON解析或格式化
                element.textContent = content;
                element.classList.remove('empty');

                // 添加数据长度和类型信息
                const dataLength = content.length;
                const isJson = content.trim().startsWith('{') && content.trim().endsWith('}');
                const dataType = isJson ? 'JSON' : 'TEXT';

                if (infoElement) {
                    infoElement.textContent = `(${dataLength} 字符, ${dataType})`;
                }

                console.log(`[${elementId}] 原始数据长度: ${dataLength}, 类型: ${dataType}`);
                console.log(`[${elementId}] 原始数据内容:`, content);
            } else {
                element.textContent = '暂无数据';
                element.classList.add('empty');

                if (infoElement) {
                    infoElement.textContent = '';
                }
            }
        }

        // 复制到剪贴板功能
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const content = element.textContent;

            if (content && content !== '暂无数据') {
                navigator.clipboard.writeText(content).then(() => {
                    // 显示复制成功提示
                    const btn = event.target;
                    const originalText = btn.textContent;
                    btn.textContent = '已复制';
                    btn.style.background = '#28a745';

                    setTimeout(() => {
                        btn.textContent = originalText;
                        btn.style.background = '#007bff';
                    }, 1000);

                    console.log(`[${elementId}] 已复制到剪贴板，长度: ${content.length}`);
                }).catch(err => {
                    console.error('复制失败:', err);
                    alert('复制失败，请手动选择文本复制');
                });
            } else {
                alert('没有可复制的内容');
            }
        }

        // 清空当前对比
        function clearCurrentComparison() {
            document.getElementById('currentComparison').style.display = 'none';
            updateDataContent('qwenRequest', '暂无数据');
            updateDataContent('qwenResponse', '暂无数据');
            updateDataContent('deepseekRequest', '暂无数据');
            updateDataContent('deepseekResponse', '暂无数据');

            console.log('已清空当前对比数据');
        }

        // 更新日志数量
        function updateLogCount() {
            document.getElementById('logCount').textContent = logs.length;
        }

        // 更新最后更新时间
        function updateLastUpdateTime() {
            const now = new Date();
            const timeStr = now.toLocaleTimeString('zh-CN');
            document.getElementById('lastUpdate').textContent = timeStr;
        }

        // 更新连接状态
        function updateConnectionStatus(isOnline) {
            const statusElement = document.getElementById('connectionStatus');
            if (isOnline) {
                statusElement.textContent = '🟢 已连接';
                statusElement.className = 'status-badge status-online';
            } else {
                statusElement.textContent = '🔴 连接失败';
                statusElement.className = 'status-badge status-offline';
            }
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>
</body>
</html>
