<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拍照搜题 - 模型配置管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 1200px;
            margin: 20px;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 20px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .content {
            padding: 30px;
        }

        .login-form {
            max-width: 400px;
            margin: 0 auto;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }

        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
            outline: none;
            border-color: #4f46e5;
        }

        .form-group small {
            display: block;
            margin-top: 4px;
            font-size: 12px;
            color: #6b7280;
            font-style: italic;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 60px;
        }

        h4 {
            font-size: 16px;
            font-weight: 600;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 8px;
        }

        .btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6b7280;
            margin-left: 10px;
        }

        .model-config {
            display: none;
        }

        .model-tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 2px solid #e5e7eb;
        }

        .tab {
            padding: 12px 24px;
            background: none;
            border: none;
            font-size: 16px;
            font-weight: 600;
            color: #6b7280;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .tab.active {
            color: #4f46e5;
            border-bottom-color: #4f46e5;
        }

        .model-panel {
            display: none;
            background: #f9fafb;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .model-panel.active {
            display: block;
        }

        .model-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #4f46e5;
        }

        .model-info h3 {
            color: #1f2937;
            margin-bottom: 10px;
        }

        .model-info p {
            color: #6b7280;
            margin-bottom: 5px;
        }

        .params-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-enabled {
            background: #dcfce7;
            color: #166534;
        }

        .status-disabled {
            background: #fee2e2;
            color: #dc2626;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .alert-error {
            background: #fee2e2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #4f46e5;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none !important;
        }

        .logout-btn {
            position: absolute;
            top: 20px;
            right: 30px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 模型配置管理</h1>
            <p>拍照搜题系统 - AI模型参数配置</p>
            <button class="logout-btn hidden" onclick="logout()">退出登录</button>
        </div>

        <div class="content">
            <!-- 登录表单 -->
            <div id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username">管理员账号</label>
                    <input type="text" id="username" placeholder="请输入手机号" value="15688515913">
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" placeholder="请输入密码" value="admin888">
                </div>
                <button class="btn" onclick="login()">登录</button>
                <div id="loginAlert"></div>
            </div>

            <!-- 模型配置界面 -->
            <div id="modelConfig" class="model-config">
                <div class="model-tabs">
                    <button class="tab active" onclick="switchTab('qwen')">Qwen-VL-Plus</button>
                    <button class="tab" onclick="switchTab('deepseek')">DeepSeek-Chat</button>
                </div>

                <!-- Qwen模型配置 -->
                <div id="qwen-panel" class="model-panel active">
                    <div class="model-info">
                        <h3>🎯 Qwen-VL-Plus (识图大模型)</h3>
                        <p><strong>功能:</strong> 图像识别和分析，将图片转换为结构化题目信息</p>
                        <p><strong>API地址:</strong> <span id="qwen-url">-</span></p>
                        <p><strong>状态:</strong> <span id="qwen-status" class="status-badge">-</span></p>
                    </div>

                    <div class="form-group">
                        <label for="qwen-apikey">API Key (App Key)</label>
                        <input type="password" id="qwen-apikey" placeholder="sk-xxx 或 App Key">
                        <small>主要的API密钥，通常以sk-开头</small>
                    </div>

                    <div class="form-group">
                        <button class="btn btn-secondary" style="width: auto;" onclick="updateApiKey('qwen-vl-plus')">更新API密钥</button>
                    </div>

                    <!-- 模型配置 -->
                    <h4 style="margin: 25px 0 15px 0; color: #4f46e5;">🤖 模型配置</h4>
                    <div class="form-group">
                        <label for="qwen-model">模型名称</label>
                        <input type="text" id="qwen-model" value="qwen-vl-plus" readonly>
                        <small>模型标识符，用于API调用</small>
                    </div>

                    <!-- 消息配置 -->
                    <h4 style="margin: 25px 0 15px 0; color: #4f46e5;">💬 消息配置</h4>
                    <div class="form-group">
                        <label for="qwen-system-message">System Message (系统提示)</label>
                        <textarea id="qwen-system-message" rows="4" placeholder="系统角色和任务描述"></textarea>
                        <small>定义AI的角色和任务，影响整体行为</small>
                    </div>
                    <div class="form-group">
                        <label for="qwen-user-message">User Message (用户提示)</label>
                        <textarea id="qwen-user-message" rows="2" placeholder="用户附加指令"></textarea>
                        <small>用户的附加指令，与图片一起发送</small>
                    </div>

                    <!-- 输出格式配置 -->
                    <h4 style="margin: 25px 0 15px 0; color: #4f46e5;">📄 输出格式</h4>
                    <div class="form-group">
                        <label for="qwen-response-format">Response Format (响应格式)</label>
                        <select id="qwen-response-format">
                            <option value="json_object">JSON Object (结构化输出)</option>
                            <option value="text">Text (纯文本输出)</option>
                        </select>
                        <small>控制模型输出格式，JSON适合结构化数据</small>
                    </div>

                    <div class="params-grid">
                        <!-- 基础参数 -->
                        <div class="form-group">
                            <label for="qwen-temperature">Temperature (随机性)</label>
                            <input type="number" id="qwen-temperature" step="0.01" min="0" max="2" value="0.0">
                            <small>控制输出随机性，0=确定性，2=最随机</small>
                        </div>
                        <div class="form-group">
                            <label for="qwen-max-tokens">Max Tokens (最大输出)</label>
                            <input type="number" id="qwen-max-tokens" min="100" max="6000" value="1500">
                            <small>最大输出token数量</small>
                        </div>
                        <div class="form-group">
                            <label for="qwen-top-p">Top P (核采样)</label>
                            <input type="number" id="qwen-top-p" step="0.01" min="0" max="1" value="0.01">
                            <small>核采样概率，控制词汇选择范围</small>
                        </div>
                        <div class="form-group">
                            <label for="qwen-top-k">Top K (采样范围)</label>
                            <input type="number" id="qwen-top-k" min="1" max="100" value="1">
                            <small>从前K个最可能的词中选择</small>
                        </div>

                        <!-- 采样控制 -->
                        <div class="form-group">
                            <label for="qwen-do-sample">Do Sample (启用采样)</label>
                            <select id="qwen-do-sample">
                                <option value="false" selected>关闭 (确定性输出)</option>
                                <option value="true">开启 (随机采样)</option>
                            </select>
                            <small>是否启用随机采样</small>
                        </div>

                        <!-- 惩罚参数 -->
                        <div class="form-group">
                            <label for="qwen-frequency-penalty">Frequency Penalty (频率惩罚)</label>
                            <input type="number" id="qwen-frequency-penalty" step="0.1" min="-2" max="2" value="-2.0">
                            <small>减少重复词汇，负值鼓励重复</small>
                        </div>
                        <div class="form-group">
                            <label for="qwen-presence-penalty">Presence Penalty (存在惩罚)</label>
                            <input type="number" id="qwen-presence-penalty" step="0.1" min="-2" max="2" value="-2.0">
                            <small>鼓励新话题，负值鼓励重复话题</small>
                        </div>

                        <!-- 图像处理 -->
                        <div class="form-group">
                            <label for="qwen-detail">Detail (图像精度)</label>
                            <select id="qwen-detail">
                                <option value="low">低精度 (快速)</option>
                                <option value="high" selected>高精度 (准确)</option>
                            </select>
                            <small>图像分析精度级别</small>
                        </div>
                    </div>

                    <button class="btn" onclick="updateParams('qwen-vl-plus')">保存参数配置</button>
                </div>

                <!-- DeepSeek模型配置 -->
                <div id="deepseek-panel" class="model-panel">
                    <div class="model-info">
                        <h3>🧠 DeepSeek-Chat (解题大模型)</h3>
                        <p><strong>功能:</strong> 问题解答和分析，根据题目信息生成详细解析</p>
                        <p><strong>API地址:</strong> <span id="deepseek-url">-</span></p>
                        <p><strong>状态:</strong> <span id="deepseek-status" class="status-badge">-</span></p>
                    </div>

                    <div class="form-group">
                        <label for="deepseek-apikey">API Key (App Key)</label>
                        <input type="password" id="deepseek-apikey" placeholder="sk-xxx 或 App Key">
                        <small>主要的API密钥，通常以sk-开头</small>
                    </div>

                    <div class="form-group">
                        <button class="btn btn-secondary" style="width: auto;" onclick="updateApiKey('deepseek-chat')">更新API密钥</button>
                    </div>

                    <!-- 模型配置 -->
                    <h4 style="margin: 25px 0 15px 0; color: #4f46e5;">🤖 模型配置</h4>
                    <div class="form-group">
                        <label for="deepseek-model">模型名称</label>
                        <input type="text" id="deepseek-model" value="deepseek-chat" readonly>
                        <small>模型标识符，用于API调用</small>
                    </div>

                    <!-- 消息配置 -->
                    <h4 style="margin: 25px 0 15px 0; color: #4f46e5;">💬 消息配置</h4>
                    <div class="form-group">
                        <label for="deepseek-system-message">System Message (系统提示)</label>
                        <textarea id="deepseek-system-message" rows="4" placeholder="系统角色和任务描述"></textarea>
                        <small>定义AI的角色和任务，影响整体行为</small>
                    </div>
                    <div class="form-group">
                        <label for="deepseek-user-message">User Message (用户提示)</label>
                        <textarea id="deepseek-user-message" rows="2" placeholder="用户附加指令"></textarea>
                        <small>用户的附加指令，与题目信息一起发送</small>
                    </div>

                    <!-- 输出格式配置 -->
                    <h4 style="margin: 25px 0 15px 0; color: #4f46e5;">📄 输出格式</h4>
                    <div class="form-group">
                        <label for="deepseek-response-format">Response Format (响应格式)</label>
                        <select id="deepseek-response-format">
                            <option value="json_object">JSON Object (结构化输出)</option>
                            <option value="text">Text (纯文本输出)</option>
                        </select>
                        <small>控制模型输出格式，JSON适合结构化数据</small>
                    </div>

                    <div class="params-grid">
                        <div class="form-group">
                            <label for="deepseek-temperature">Temperature (随机性)</label>
                            <input type="number" id="deepseek-temperature" step="0.1" min="0" max="2" value="0.3">
                        </div>
                        <div class="form-group">
                            <label for="deepseek-max-tokens">Max Tokens (最大输出)</label>
                            <input type="number" id="deepseek-max-tokens" min="100" max="8000" value="2500">
                        </div>
                        <div class="form-group">
                            <label for="deepseek-top-p">Top P (多样性)</label>
                            <input type="number" id="deepseek-top-p" step="0.1" min="0" max="1" value="0.8">
                        </div>
                    </div>

                    <button class="btn" onclick="updateParams('deepseek-chat')">保存参数配置</button>
                </div>

                <div id="configAlert"></div>
                <div id="loading" class="loading">
                    <div class="spinner"></div>
                    <p>处理中...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api/v1';
        let authToken = '';

        // 登录功能
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showAlert('loginAlert', '请输入账号和密码', 'error');
                return;
            }

            showLoading(true);

            try {
                const response = await fetch(`${API_BASE}/admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        phone: username,
                        password: password
                    })
                });

                const data = await response.json();

                if (data.code === 200) {
                    authToken = data.data.token;
                    showAlert('loginAlert', '登录成功！', 'success');
                    
                    setTimeout(() => {
                        document.getElementById('loginForm').style.display = 'none';
                        document.getElementById('modelConfig').style.display = 'block';
                        document.querySelector('.logout-btn').classList.remove('hidden');
                        loadModelConfigs();
                    }, 1000);
                } else {
                    showAlert('loginAlert', data.message || '登录失败', 'error');
                }
            } catch (error) {
                showAlert('loginAlert', '网络错误: ' + error.message, 'error');
            }

            showLoading(false);
        }

        // 退出登录
        function logout() {
            authToken = '';
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('modelConfig').style.display = 'none';
            document.querySelector('.logout-btn').classList.add('hidden');
            document.getElementById('username').value = '15688515913';
            document.getElementById('password').value = 'admin888';
            clearAlerts();
        }

        // 切换标签页
        function switchTab(model) {
            // 更新标签状态
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');

            // 更新面板显示
            document.querySelectorAll('.model-panel').forEach(panel => panel.classList.remove('active'));
            document.getElementById(`${model}-panel`).classList.add('active');
        }

        // 加载模型配置
        async function loadModelConfigs() {
            showLoading(true);

            try {
                const response = await fetch(`${API_BASE}/admin/model/fixed`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (data.code === 200) {
                    const { qwen, deepseek } = data.data;
                    
                    if (qwen) {
                        updateModelInfo('qwen', qwen);
                        loadModelParams('qwen', qwen.params);
                    }
                    
                    if (deepseek) {
                        updateModelInfo('deepseek', deepseek);
                        loadModelParams('deepseek', deepseek.params);
                    }
                } else {
                    showAlert('configAlert', '加载模型配置失败: ' + data.message, 'error');
                }
            } catch (error) {
                showAlert('configAlert', '网络错误: ' + error.message, 'error');
            }

            showLoading(false);
        }

        // 更新模型信息显示
        function updateModelInfo(model, config) {
            const prefix = model === 'qwen' ? 'qwen' : 'deepseek';
            const modelName = model === 'qwen' ? 'qwen-vl-plus' : 'deepseek-chat';

            document.getElementById(`${prefix}-url`).textContent = config.api_url;

            const statusElement = document.getElementById(`${prefix}-status`);
            statusElement.textContent = config.status_name;
            statusElement.className = `status-badge ${config.status === 1 ? 'status-enabled' : 'status-disabled'}`;

            // 显示API Key（脱敏）
            const apiKeyInput = document.getElementById(`${prefix}-apikey`);
            if (config.api_key && config.api_key !== 'sk-placeholder') {
                apiKeyInput.placeholder = config.api_key.substring(0, 8) + '****';
            }


        }

        // 加载模型参数
        function loadModelParams(model, params) {
            const prefix = model === 'qwen' ? 'qwen' : 'deepseek';

            // 模型配置
            if (params.model !== undefined) {
                document.getElementById(`${prefix}-model`).value = params.model;
            }

            // 基础参数
            if (params.temperature !== undefined) {
                document.getElementById(`${prefix}-temperature`).value = params.temperature;
            }
            if (params.max_tokens !== undefined) {
                document.getElementById(`${prefix}-max-tokens`).value = params.max_tokens;
            }
            if (params.top_p !== undefined) {
                document.getElementById(`${prefix}-top-p`).value = params.top_p;
            }

            // 消息配置
            if (params.system_message !== undefined) {
                document.getElementById(`${prefix}-system-message`).value = params.system_message;
            }
            if (params.user_message !== undefined) {
                document.getElementById(`${prefix}-user-message`).value = params.user_message;
            }

            // 输出格式
            if (params.response_format !== undefined) {
                const formatType = params.response_format.type || 'json_object';
                document.getElementById(`${prefix}-response-format`).value = formatType;
            }

            // Qwen特有参数
            if (model === 'qwen') {
                if (params.top_k !== undefined) {
                    document.getElementById('qwen-top-k').value = params.top_k;
                }
                if (params.do_sample !== undefined) {
                    document.getElementById('qwen-do-sample').value = params.do_sample.toString();
                }
                if (params.frequency_penalty !== undefined) {
                    document.getElementById('qwen-frequency-penalty').value = params.frequency_penalty;
                }
                if (params.presence_penalty !== undefined) {
                    document.getElementById('qwen-presence-penalty').value = params.presence_penalty;
                }
                if (params.detail !== undefined) {
                    document.getElementById('qwen-detail').value = params.detail;
                }
            }
        }

        // 更新模型参数
        async function updateParams(modelName) {
            const prefix = modelName === 'qwen-vl-plus' ? 'qwen' : 'deepseek';

            const params = {
                // 模型配置
                model: document.getElementById(`${prefix}-model`).value,

                // 基础参数
                temperature: parseFloat(document.getElementById(`${prefix}-temperature`).value),
                max_tokens: parseInt(document.getElementById(`${prefix}-max-tokens`).value),
                top_p: parseFloat(document.getElementById(`${prefix}-top-p`).value),

                // 消息配置
                system_message: document.getElementById(`${prefix}-system-message`).value,
                user_message: document.getElementById(`${prefix}-user-message`).value,

                // 输出格式
                response_format: {
                    type: document.getElementById(`${prefix}-response-format`).value
                }
            };

            if (modelName === 'qwen-vl-plus') {
                // Qwen特有参数
                params.top_k = parseInt(document.getElementById('qwen-top-k').value);
                params.do_sample = document.getElementById('qwen-do-sample').value === 'true';
                params.frequency_penalty = parseFloat(document.getElementById('qwen-frequency-penalty').value);
                params.presence_penalty = parseFloat(document.getElementById('qwen-presence-penalty').value);
                params.detail = document.getElementById('qwen-detail').value;
            }

            showLoading(true);

            try {
                const response = await fetch(`${API_BASE}/admin/model/name/${modelName}/params`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ params })
                });

                const data = await response.json();

                if (data.code === 200) {
                    showAlert('configAlert', `${modelName} 参数更新成功！`, 'success');
                } else {
                    showAlert('configAlert', '参数更新失败: ' + data.message, 'error');
                }
            } catch (error) {
                showAlert('configAlert', '网络错误: ' + error.message, 'error');
            }

            showLoading(false);
        }

        // 更新API密钥
        async function updateApiKey(modelName) {
            const prefix = modelName === 'qwen-vl-plus' ? 'qwen' : 'deepseek';
            const apiKey = document.getElementById(`${prefix}-apikey`).value;

            if (!apiKey) {
                showAlert('configAlert', '请输入API Key', 'error');
                return;
            }

            showLoading(true);

            try {
                const requestBody = { api_key: apiKey };

                const response = await fetch(`${API_BASE}/admin/model/name/${modelName}/apikey`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(requestBody)
                });

                const data = await response.json();

                if (data.code === 200) {
                    showAlert('configAlert', `${modelName} API密钥更新成功！`, 'success');

                    // 清空输入框并更新占位符
                    document.getElementById(`${prefix}-apikey`).value = '';
                    document.getElementById(`${prefix}-apikey`).placeholder = apiKey.substring(0, 8) + '****';

                    if (apiSecret) {
                        document.getElementById(`${prefix}-apisecret`).value = '';
                        document.getElementById(`${prefix}-apisecret`).placeholder = apiSecret.substring(0, 6) + '****';
                    }
                } else {
                    showAlert('configAlert', 'API密钥更新失败: ' + data.message, 'error');
                }
            } catch (error) {
                showAlert('configAlert', '网络错误: ' + error.message, 'error');
            }

            showLoading(false);
        }

        // 显示提示信息
        function showAlert(elementId, message, type) {
            const alertElement = document.getElementById(elementId);
            alertElement.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
            
            setTimeout(() => {
                alertElement.innerHTML = '';
            }, 5000);
        }

        // 清除所有提示
        function clearAlerts() {
            document.getElementById('loginAlert').innerHTML = '';
            document.getElementById('configAlert').innerHTML = '';
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 支持回车键登录
            document.getElementById('password').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    login();
                }
            });
        });
    </script>
</body>
</html>
