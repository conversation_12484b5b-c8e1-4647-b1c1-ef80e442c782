<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拍照搜题 - API测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 20px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .content {
            padding: 30px;
        }

        .test-form {
            background: #f9fafb;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #4f46e5;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }

        .form-group input, .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #4f46e5;
        }

        .btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            margin-right: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .results-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 30px;
        }

        .result-panel {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }

        .result-header {
            padding: 15px 20px;
            font-weight: 600;
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .qwen-header {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .deepseek-header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .result-content {
            padding: 20px;
            max-height: 500px;
            overflow-y: auto;
        }

        .result-json {
            background: #1f2937;
            color: #e5e7eb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #4f46e5;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .status-success {
            background: #dcfce7;
            color: #166534;
        }

        .status-error {
            background: #fee2e2;
            color: #dc2626;
        }

        .status-loading {
            background: #dbeafe;
            color: #1d4ed8;
        }

        .image-preview {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
            margin-top: 10px;
            border: 2px solid #e5e7eb;
        }

        .hidden {
            display: none !important;
        }

        .error-message {
            background: #fee2e2;
            color: #dc2626;
            padding: 12px 16px;
            border-radius: 8px;
            margin-top: 10px;
            border: 1px solid #fecaca;
        }

        .success-message {
            background: #dcfce7;
            color: #166534;
            padding: 12px 16px;
            border-radius: 8px;
            margin-top: 10px;
            border: 1px solid #bbf7d0;
        }

        .timing-info {
            font-size: 11px;
            opacity: 0.8;
        }

        .response-stats {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            font-size: 12px;
        }

        .stat-item {
            background: #f3f4f6;
            padding: 6px 12px;
            border-radius: 6px;
        }

        .performance-panel {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            margin-top: 20px;
            overflow: hidden;
        }

        .performance-header {
            background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
        }

        .performance-content {
            padding: 20px;
        }

        .performance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .performance-item {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #8b5cf6;
        }

        .performance-label {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .performance-value {
            font-size: 18px;
            font-weight: 700;
            color: #1f2937;
        }

        .performance-unit {
            font-size: 12px;
            color: #6b7280;
            margin-left: 2px;
        }

        .performance-panel {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            margin-top: 20px;
            overflow: hidden;
        }

        .performance-header {
            background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
        }

        .performance-content {
            padding: 20px;
        }

        .performance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .performance-item {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #8b5cf6;
        }

        .performance-label {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .performance-value {
            font-size: 18px;
            font-weight: 700;
            color: #1f2937;
        }

        .performance-unit {
            font-size: 12px;
            color: #6b7280;
            margin-left: 2px;
        }

        .logs-container {
            margin-top: 30px;
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }

        .logs-header {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logs-tabs {
            display: flex;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
        }

        .log-tab {
            flex: 1;
            padding: 12px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }

        .log-tab.active {
            color: #4f46e5;
            background: white;
            border-bottom-color: #4f46e5;
        }

        .log-tab:hover {
            background: #f3f4f6;
        }

        .log-content {
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }

        .log-entry.detailed-log {
            border-left: 4px solid #6366f1;
            background: #fafbff;
            margin-bottom: 8px;
        }

        .log-entry-header {
            background: #f9fafb;
            padding: 10px 15px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: between;
            align-items: center;
            font-size: 12px;
            color: #6b7280;
        }

        .detailed-log .log-entry-header {
            background: #f1f5f9;
            padding: 8px 12px;
        }

        .log-entry-content {
            padding: 15px;
        }

        .detailed-log .log-entry-content {
            padding: 8px 12px;
            font-size: 13px;
        }

        .log-json {
            background: #1f2937;
            color: #e5e7eb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 11px;
            line-height: 1.4;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 400px;
            overflow-y: auto;
        }

        .log-clear-btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .log-clear-btn:hover {
            background: #dc2626;
        }

        .log-timestamp {
            font-family: monospace;
            color: #9ca3af;
        }

        .log-type {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .log-type-request {
            background: #dbeafe;
            color: #1d4ed8;
        }

        .log-type-response {
            background: #dcfce7;
            color: #166534;
        }

        .log-type-error {
            background: #fee2e2;
            color: #dc2626;
        }

        .empty-logs {
            text-align: center;
            color: #6b7280;
            padding: 40px;
            font-style: italic;
        }

        @media (max-width: 768px) {
            .results-container {
                grid-template-columns: 1fr;
            }

            .content {
                padding: 20px;
            }

            .logs-tabs {
                flex-direction: column;
            }

            .log-tab {
                border-bottom: 1px solid #e5e7eb;
                border-right: none;
            }

            .log-tab.active {
                border-bottom-color: #e5e7eb;
                border-left: 3px solid #4f46e5;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 拍照搜题 API测试</h1>
            <p>测试图片识别和题目解答的完整流程</p>
        </div>

        <div class="content">
            <!-- 测试表单 -->
            <div class="test-form">
                <h3 style="margin-bottom: 20px; color: #4f46e5;">📸 图片搜题测试</h3>
                
                <div class="form-group">
                    <label for="imageUrl">图片URL</label>
                    <input type="url" id="imageUrl" placeholder="请输入您的图片URL" value="">
                    <small>请输入可访问的图片链接，支持jpg、png、gif等格式</small>
                </div>

                <div class="form-group">
                    <label for="apiKey">App Key (可选)</label>
                    <input type="password" id="apiKey" placeholder="应用的App Key，用于API认证">
                </div>

                <div class="form-group">
                    <label for="apiSecret">Secret Key (可选)</label>
                    <input type="password" id="apiSecret" placeholder="应用的Secret Key，配对使用">
                </div>

                <div style="display: flex; align-items: center; gap: 15px;">
                    <button class="btn" onclick="testSearch()" id="testBtn">
                        🚀 开始搜题测试
                    </button>
                    <button class="btn" onclick="clearResults()" style="background: #6b7280;">
                        🗑️ 清空结果
                    </button>
                    <button class="btn" onclick="previewImage()" style="background: #059669;">
                        👁️ 预览图片
                    </button>
                </div>

                <div id="imagePreview" class="hidden">
                    <img id="previewImg" class="image-preview" alt="图片预览">
                </div>

                <div id="errorMessage"></div>
            </div>

            <!-- 加载状态 -->
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>正在处理图片，请稍候...</p>
            </div>

            <!-- 结果展示 -->
            <div class="results-container">
                <!-- Qwen结果 -->
                <div class="result-panel">
                    <div class="result-header qwen-header">
                        <div>
                            <span>🎯 Qwen-VL-Plus (图像识别)</span>
                            <div class="timing-info" id="qwenTiming"></div>
                        </div>
                        <span class="status-badge" id="qwenStatus">待测试</span>
                    </div>
                    <div class="result-content">
                        <div class="response-stats" id="qwenStats" style="display: none;">
                            <div class="stat-item">
                                <strong>响应时间:</strong> <span id="qwenTime">-</span>
                            </div>
                            <div class="stat-item">
                                <strong>状态码:</strong> <span id="qwenCode">-</span>
                            </div>
                        </div>
                        <div id="qwenResult">
                            <p style="color: #6b7280; text-align: center; padding: 40px;">
                                点击"开始搜题测试"查看Qwen图像识别结果
                            </p>
                        </div>
                    </div>
                </div>

                <!-- DeepSeek结果 -->
                <div class="result-panel">
                    <div class="result-header deepseek-header">
                        <div>
                            <span>🧠 DeepSeek-Chat (题目解答)</span>
                            <div class="timing-info" id="deepseekTiming"></div>
                        </div>
                        <span class="status-badge" id="deepseekStatus">待测试</span>
                    </div>
                    <div class="result-content">
                        <div class="response-stats" id="deepseekStats" style="display: none;">
                            <div class="stat-item">
                                <strong>响应时间:</strong> <span id="deepseekTime">-</span>
                            </div>
                            <div class="stat-item">
                                <strong>状态码:</strong> <span id="deepseekCode">-</span>
                            </div>
                        </div>
                        <div id="deepseekResult">
                            <p style="color: #6b7280; text-align: center; padding: 40px;">
                                等待Qwen识别完成后自动开始DeepSeek解答
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 性能统计面板 -->
            <div class="performance-panel">
                <div class="performance-header">
                    <span>⚡ 性能统计与Token消费</span>
                </div>
                <div class="performance-content">
                    <div class="performance-grid" id="performanceGrid">
                        <div class="performance-item">
                            <div class="performance-label">总耗时</div>
                            <div class="performance-value" id="totalTime">-<span class="performance-unit">ms</span></div>
                        </div>
                        <div class="performance-item">
                            <div class="performance-label">Qwen响应时间</div>
                            <div class="performance-value" id="qwenResponseTime">-<span class="performance-unit">ms</span></div>
                        </div>
                        <div class="performance-item">
                            <div class="performance-label">DeepSeek响应时间</div>
                            <div class="performance-value" id="deepseekResponseTime">-<span class="performance-unit">ms</span></div>
                        </div>
                        <div class="performance-item">
                            <div class="performance-label">Qwen Token消费</div>
                            <div class="performance-value" id="qwenTokens">-<span class="performance-unit">tokens</span></div>
                        </div>
                        <div class="performance-item">
                            <div class="performance-label">DeepSeek Token消费</div>
                            <div class="performance-value" id="deepseekTokens">-<span class="performance-unit">tokens</span></div>
                        </div>
                        <div class="performance-item">
                            <div class="performance-label">总Token消费</div>
                            <div class="performance-value" id="totalTokens">-<span class="performance-unit">tokens</span></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 时间线日志 -->
            <div class="logs-container">
                <div class="logs-header">
                    <div>
                        <span>📋 请求时间线</span>
                        <div class="timing-info">记录从用户提交照片到返回最终答案的完整时间线和关键节点耗时</div>
                    </div>
                    <button class="log-clear-btn" onclick="clearLogs()">清空日志</button>
                </div>

                <div class="log-content">
                    <div id="timelineLogs">
                        <div class="empty-logs">暂无请求时间线记录</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api/v1';
        let testResults = {
            qwen: null,
            deepseek: null
        };

        // 性能统计
        let performanceStats = {
            startTime: null,
            qwenStartTime: null,
            qwenEndTime: null,
            deepseekStartTime: null,
            deepseekEndTime: null,
            totalTime: 0,
            qwenTime: 0,
            deepseekTime: 0,
            qwenTokens: 0,
            deepseekTokens: 0
        };

        // 时间线日志
        let timelineLogs = [];

        // 预览图片
        function previewImage() {
            const imageUrl = document.getElementById('imageUrl').value;
            if (!imageUrl) {
                showError('请先输入图片URL');
                return;
            }

            const previewDiv = document.getElementById('imagePreview');
            const previewImg = document.getElementById('previewImg');
            
            previewImg.src = imageUrl;
            previewImg.onload = function() {
                previewDiv.classList.remove('hidden');
            };
            previewImg.onerror = function() {
                showError('图片加载失败，请检查URL是否正确');
            };
        }

        // 开始搜题测试
        async function testSearch() {
            const imageUrl = document.getElementById('imageUrl').value;
            const apiKey = document.getElementById('apiKey').value;
            const apiSecret = document.getElementById('apiSecret').value;

            if (!imageUrl) {
                showError('请输入图片URL');
                return;
            }

            // 重置状态和性能统计
            clearResults();
            resetPerformanceStats();
            showLoading(true);
            setTestButtonState(true);

            // 记录开始时间
            performanceStats.startTime = Date.now();
            addTimelineLog('开始', '用户提交图片，开始搜题流程', 0);

            try {
                // 第一步：Qwen图像识别
                await testQwenRecognition(imageUrl, apiKey, apiSecret);

                // 第二步：DeepSeek题目解答
                if (testResults.qwen && testResults.qwen.success) {
                    await testDeepSeekSolving(testResults.qwen.data, apiKey, apiSecret);
                }

                // 计算总耗时
                performanceStats.totalTime = Date.now() - performanceStats.startTime;
                addTimelineLog('完成', '搜题流程全部完成', performanceStats.totalTime);
                updatePerformanceDisplay();

            } catch (error) {
                showError('测试过程中发生错误: ' + error.message);
                addTimelineLog('错误', error.message, Date.now() - performanceStats.startTime);
            } finally {
                showLoading(false);
                setTestButtonState(false);
            }
        }

        // 测试Qwen图像识别
        async function testQwenRecognition(imageUrl, apiKey, apiSecret) {
            updateStatus('qwen', 'loading', '识别中...');
            performanceStats.qwenStartTime = Date.now();
            const relativeStartTime = performanceStats.qwenStartTime - performanceStats.startTime;

            addTimelineLog('Qwen开始', '开始调用Qwen-VL进行图像识别', relativeStartTime);

            try {
                const headers = {
                    'Content-Type': 'application/json'
                };

                // 使用标准的API认证方式
                if (apiKey) {
                    headers['X-App-Key'] = apiKey;
                    if (apiSecret) {
                        headers['X-Secret-Key'] = apiSecret;
                    }
                }

                const requestBody = {
                    image_url: imageUrl
                };

                const response = await fetch(`${API_BASE}/api/search`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(requestBody)
                });

                performanceStats.qwenEndTime = Date.now();
                performanceStats.qwenTime = performanceStats.qwenEndTime - performanceStats.qwenStartTime;
                const relativeEndTime = performanceStats.qwenEndTime - performanceStats.startTime;

                const data = await response.json();

                // 提取token消费信息
                if (data.data && data.data.qwen_usage) {
                    performanceStats.qwenTokens = data.data.qwen_usage.total_tokens || 0;
                }

                // 更新统计信息
                updateStats('qwen', performanceStats.qwenTime, response.status);

                if (response.ok && data.code === 200) {
                    testResults.qwen = {
                        success: true,
                        data: data.data,
                        responseTime: performanceStats.qwenTime
                    };

                    updateStatus('qwen', 'success', '识别成功');
                    displayResult('qwen', data);
                    addTimelineLog('Qwen完成', `图像识别成功，耗时${performanceStats.qwenTime}ms，消费${performanceStats.qwenTokens}tokens`, relativeEndTime);

                    // 解析服务器返回的详细性能日志
                    parseServerPerformanceLogs(data);
                } else {
                    testResults.qwen = {
                        success: false,
                        error: data.message || '识别失败'
                    };

                    updateStatus('qwen', 'error', '识别失败');
                    displayError('qwen', data.message || '识别失败');
                    addTimelineLog('Qwen失败', data.message || '识别失败', relativeEndTime);
                }
            } catch (error) {
                performanceStats.qwenEndTime = Date.now();
                performanceStats.qwenTime = performanceStats.qwenEndTime - performanceStats.qwenStartTime;
                const relativeEndTime = performanceStats.qwenEndTime - performanceStats.startTime;

                testResults.qwen = {
                    success: false,
                    error: error.message
                };

                updateStatus('qwen', 'error', '网络错误');
                updateStats('qwen', performanceStats.qwenTime, 'ERROR');
                displayError('qwen', '网络错误: ' + error.message);
                addTimelineLog('Qwen错误', '网络错误: ' + error.message, relativeEndTime);
            }
        }

        // 测试DeepSeek题目解答
        async function testDeepSeekSolving(qwenData, apiKey, apiSecret) {
            updateStatus('deepseek', 'loading', '解答中...');
            performanceStats.deepseekStartTime = Date.now();
            const relativeStartTime = performanceStats.deepseekStartTime - performanceStats.startTime;

            addTimelineLog('DeepSeek开始', '开始调用DeepSeek进行题目解答', relativeStartTime);

            try {
                const headers = {
                    'Content-Type': 'application/json'
                };

                // 使用标准的API认证方式
                if (apiKey) {
                    headers['X-App-Key'] = apiKey;
                    if (apiSecret) {
                        headers['X-Secret-Key'] = apiSecret;
                    }
                }

                // 构建DeepSeek请求，使用Qwen的识别结果
                const requestBody = {
                    question_info: qwenData.question_info || qwenData,
                    image_url: document.getElementById('imageUrl').value
                };

                const response = await fetch(`${API_BASE}/api/search`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(requestBody)
                });

                performanceStats.deepseekEndTime = Date.now();
                performanceStats.deepseekTime = performanceStats.deepseekEndTime - performanceStats.deepseekStartTime;
                const relativeEndTime = performanceStats.deepseekEndTime - performanceStats.startTime;

                const data = await response.json();

                // 提取token消费信息
                if (data.data && data.data.deepseek_usage) {
                    performanceStats.deepseekTokens = data.data.deepseek_usage.total_tokens || 0;
                }

                // 更新统计信息
                updateStats('deepseek', performanceStats.deepseekTime, response.status);

                if (response.ok && data.code === 200) {
                    testResults.deepseek = {
                        success: true,
                        data: data.data,
                        responseTime: performanceStats.deepseekTime
                    };

                    updateStatus('deepseek', 'success', '解答成功');
                    displayResult('deepseek', data);
                    addTimelineLog('DeepSeek完成', `题目解答成功，耗时${performanceStats.deepseekTime}ms，消费${performanceStats.deepseekTokens}tokens`, relativeEndTime);
                } else {
                    testResults.deepseek = {
                        success: false,
                        error: data.message || '解答失败'
                    };

                    updateStatus('deepseek', 'error', '解答失败');
                    displayError('deepseek', data.message || '解答失败');
                    addTimelineLog('DeepSeek失败', data.message || '解答失败', relativeEndTime);
                }
            } catch (error) {
                performanceStats.deepseekEndTime = Date.now();
                performanceStats.deepseekTime = performanceStats.deepseekEndTime - performanceStats.deepseekStartTime;
                const relativeEndTime = performanceStats.deepseekEndTime - performanceStats.startTime;

                testResults.deepseek = {
                    success: false,
                    error: error.message
                };

                updateStatus('deepseek', 'error', '网络错误');
                updateStats('deepseek', performanceStats.deepseekTime, 'ERROR');
                displayError('deepseek', '网络错误: ' + error.message);
                addTimelineLog('DeepSeek错误', '网络错误: ' + error.message, relativeEndTime);
            }
        }

        // 更新状态
        function updateStatus(model, status, text) {
            const statusElement = document.getElementById(`${model}Status`);
            statusElement.textContent = text;
            statusElement.className = `status-badge status-${status}`;
        }

        // 更新统计信息
        function updateStats(model, responseTime, statusCode) {
            document.getElementById(`${model}Stats`).style.display = 'flex';
            document.getElementById(`${model}Time`).textContent = responseTime + 'ms';
            document.getElementById(`${model}Code`).textContent = statusCode;
        }

        // 显示结果
        function displayResult(model, data) {
            const resultElement = document.getElementById(`${model}Result`);

            if (model === 'qwen') {
                // Qwen显示原始content值
                let qwenContent = '';
                if (data.data && data.data.qwen_raw_content) {
                    qwenContent = data.data.qwen_raw_content;
                } else if (data.data && data.data.question_text) {
                    qwenContent = data.data.question_text;
                } else if (data.data && data.data.content) {
                    qwenContent = data.data.content;
                } else {
                    qwenContent = '无法获取Qwen识别内容';
                }

                resultElement.innerHTML = `
                    <div class="result-content">
                        <h4 style="margin: 0 0 10px 0; color: #4f46e5;">🎯 Qwen-VL原始Content</h4>
                        <div class="content-text" style="background: #f8fafc; padding: 15px; border-radius: 6px; border-left: 4px solid #4f46e5; white-space: pre-wrap; font-family: 'SF Mono', Monaco, monospace; font-size: 13px; line-height: 1.5;">${qwenContent}</div>
                    </div>
                `;
            } else if (model === 'deepseek') {
                // DeepSeek显示原始content值
                let deepseekContent = '';
                if (data.data && data.data.deepseek_raw_content) {
                    deepseekContent = data.data.deepseek_raw_content;
                } else if (data.data && data.data.analysis) {
                    deepseekContent = data.data.analysis;
                } else {
                    deepseekContent = '无法获取DeepSeek解答内容';
                }

                resultElement.innerHTML = `
                    <div class="result-content">
                        <h4 style="margin: 0 0 10px 0; color: #059669;">🧠 DeepSeek原始Content</h4>
                        <div class="content-text" style="background: #f0fdf4; padding: 15px; border-radius: 6px; border-left: 4px solid #059669; white-space: pre-wrap; font-family: 'SF Mono', Monaco, monospace; font-size: 13px; line-height: 1.5;">${deepseekContent}</div>
                    </div>
                `;
            } else {
                // 其他情况显示完整JSON
                resultElement.innerHTML = `<div class="result-json">${JSON.stringify(data, null, 2)}</div>`;
            }
        }

        // 显示错误
        function displayError(model, error) {
            const resultElement = document.getElementById(`${model}Result`);
            resultElement.innerHTML = `<div class="error-message">❌ ${error}</div>`;
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        // 设置测试按钮状态
        function setTestButtonState(disabled) {
            const btn = document.getElementById('testBtn');
            btn.disabled = disabled;
            btn.textContent = disabled ? '🔄 测试中...' : '🚀 开始搜题测试';
        }

        // 清空结果
        function clearResults() {
            testResults = { qwen: null, deepseek: null };

            // 重置状态
            updateStatus('qwen', '', '待测试');
            updateStatus('deepseek', '', '待测试');

            // 隐藏统计信息
            document.getElementById('qwenStats').style.display = 'none';
            document.getElementById('deepseekStats').style.display = 'none';

            // 清空结果
            document.getElementById('qwenResult').innerHTML =
                '<p style="color: #6b7280; text-align: center; padding: 40px;">点击"开始搜题测试"查看Qwen图像识别结果</p>';
            document.getElementById('deepseekResult').innerHTML =
                '<p style="color: #6b7280; text-align: center; padding: 40px;">等待Qwen识别完成后自动开始DeepSeek解答</p>';

            // 清空错误信息
            document.getElementById('errorMessage').innerHTML = '';

            // 重置性能统计显示
            resetPerformanceDisplay();
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('errorMessage').innerHTML =
                `<div class="error-message">❌ ${message}</div>`;
        }

        // 重置性能统计
        function resetPerformanceStats() {
            performanceStats = {
                startTime: null,
                qwenStartTime: null,
                qwenEndTime: null,
                deepseekStartTime: null,
                deepseekEndTime: null,
                totalTime: 0,
                qwenTime: 0,
                deepseekTime: 0,
                qwenTokens: 0,
                deepseekTokens: 0
            };
            timelineLogs = [];
        }

        // 重置性能显示
        function resetPerformanceDisplay() {
            document.getElementById('totalTime').innerHTML = '-<span class="performance-unit">ms</span>';
            document.getElementById('qwenResponseTime').innerHTML = '-<span class="performance-unit">ms</span>';
            document.getElementById('deepseekResponseTime').innerHTML = '-<span class="performance-unit">ms</span>';
            document.getElementById('qwenTokens').innerHTML = '-<span class="performance-unit">tokens</span>';
            document.getElementById('deepseekTokens').innerHTML = '-<span class="performance-unit">tokens</span>';
            document.getElementById('totalTokens').innerHTML = '-<span class="performance-unit">tokens</span>';
        }

        // 更新性能显示
        function updatePerformanceDisplay() {
            document.getElementById('totalTime').innerHTML = `${performanceStats.totalTime}<span class="performance-unit">ms</span>`;
            document.getElementById('qwenResponseTime').innerHTML = `${performanceStats.qwenTime}<span class="performance-unit">ms</span>`;
            document.getElementById('deepseekResponseTime').innerHTML = `${performanceStats.deepseekTime}<span class="performance-unit">ms</span>`;
            document.getElementById('qwenTokens').innerHTML = `${performanceStats.qwenTokens}<span class="performance-unit">tokens</span>`;
            document.getElementById('deepseekTokens').innerHTML = `${performanceStats.deepseekTokens}<span class="performance-unit">tokens</span>`;

            const totalTokens = performanceStats.qwenTokens + performanceStats.deepseekTokens;
            document.getElementById('totalTokens').innerHTML = `${totalTokens}<span class="performance-unit">tokens</span>`;
        }

        // 添加时间线日志
        function addTimelineLog(stage, description, relativeTime) {
            const logEntry = {
                timestamp: new Date().toLocaleTimeString(),
                stage: stage,
                description: description,
                relativeTime: relativeTime,
                id: Date.now() + Math.random(),
                type: 'main'
            };

            timelineLogs.push(logEntry);
            updateTimelineDisplay();
        }

        // 添加详细性能日志
        function addDetailedLog(category, message, duration = null, data = null) {
            const logEntry = {
                timestamp: new Date().toLocaleTimeString(),
                stage: category,
                description: message,
                relativeTime: duration || 0,
                id: Date.now() + Math.random(),
                type: 'detailed',
                data: data
            };

            timelineLogs.push(logEntry);
            updateTimelineDisplay();
        }

        // 更新时间线显示
        function updateTimelineDisplay() {
            const container = document.getElementById('timelineLogs');

            if (timelineLogs.length === 0) {
                container.innerHTML = '<div class="empty-logs">暂无请求时间线记录</div>';
                return;
            }

            container.innerHTML = timelineLogs.map(log => {
                const stageColor = getStageColor(log.stage);
                const isDetailed = log.type === 'detailed';
                const durationText = log.relativeTime > 0 ? `+${log.relativeTime}ms` : '';
                const dataText = log.data ? `<pre style="margin-top: 8px; background: #f9fafb; padding: 8px; border-radius: 4px; font-size: 12px; overflow-x: auto;">${JSON.stringify(log.data, null, 2)}</pre>` : '';

                return `
                    <div class="log-entry ${isDetailed ? 'detailed-log' : ''}">
                        <div class="log-entry-header">
                            <div>
                                <span class="log-timestamp">${log.timestamp}</span>
                                <span class="log-type" style="background: ${stageColor.bg}; color: ${stageColor.text};">${log.stage}</span>
                                ${durationText ? `<span style="margin-left: 10px; color: #6b7280;">${durationText}</span>` : ''}
                                ${isDetailed ? '<span style="margin-left: 10px; color: #9ca3af; font-size: 12px;">详细</span>' : ''}
                            </div>
                        </div>
                        <div class="log-entry-content">
                            <div style="padding: 10px 0; color: #374151;">${log.description}</div>
                            ${dataText}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 获取阶段颜色
        function getStageColor(stage) {
            const colors = {
                // 主要流程
                '开始': { bg: '#dbeafe', text: '#1d4ed8' },
                'Qwen开始': { bg: '#fef3c7', text: '#92400e' },
                'Qwen完成': { bg: '#fef3c7', text: '#92400e' },
                'Qwen失败': { bg: '#fee2e2', text: '#dc2626' },
                'Qwen错误': { bg: '#fee2e2', text: '#dc2626' },
                'DeepSeek开始': { bg: '#d1fae5', text: '#065f46' },
                'DeepSeek完成': { bg: '#d1fae5', text: '#065f46' },
                'DeepSeek失败': { bg: '#fee2e2', text: '#dc2626' },
                'DeepSeek错误': { bg: '#fee2e2', text: '#dc2626' },
                '完成': { bg: '#dcfce7', text: '#166534' },
                '错误': { bg: '#fee2e2', text: '#dc2626' },

                // 详细性能分析
                '业务性能分析': { bg: '#e0e7ff', text: '#3730a3' },
                'Qwen性能分析': { bg: '#fef3c7', text: '#92400e' },
                'HTTP性能分析': { bg: '#ecfdf5', text: '#047857' },
                'Redis性能分析': { bg: '#fef2f2', text: '#b91c1c' },
                'MySQL性能分析': { bg: '#f0f9ff', text: '#0369a1' },

                // 系统操作
                '图片URL验证': { bg: '#f3f4f6', text: '#6b7280' },
                '模型配置': { bg: '#f3f4f6', text: '#6b7280' },
                '数据预处理': { bg: '#f3f4f6', text: '#6b7280' },
                '缓存操作': { bg: '#f3f4f6', text: '#6b7280' },
                '数据库操作': { bg: '#f3f4f6', text: '#6b7280' },
                '网络请求': { bg: '#f3f4f6', text: '#6b7280' }
            };
            return colors[stage] || { bg: '#f3f4f6', text: '#6b7280' };
        }

        // 清空日志
        function clearLogs() {
            timelineLogs = [];
            updateTimelineDisplay();
        }

        // 解析服务器返回的详细性能日志
        function parseServerPerformanceLogs(responseData) {
            console.log('解析服务器性能日志:', responseData);

            // 检查是否有性能日志数据
            if (responseData.data && responseData.data.performance_logs) {
                const logs = responseData.data.performance_logs;

                // 遍历服务器返回的性能日志
                logs.forEach(log => {
                    const duration = log.duration ? parseFloat(log.duration.replace('ms', '')) : null;
                    addDetailedLog(
                        log.category,
                        log.message,
                        duration,
                        log.data
                    );
                });
            } else {
                // 如果没有服务器日志，添加一些模拟的详细性能日志
                console.log('未找到服务器性能日志，使用模拟数据');

                // 业务流程分析
                addDetailedLog('业务性能分析', '步骤1-图片URL验证完成', 249, { step: 'url_validation', status: 'success' });
                addDetailedLog('业务性能分析', '步骤2-开始调用Qwen-VL进行图像识别', 0, { step: 'qwen_start' });

                // Qwen详细分析
                addDetailedLog('Qwen性能分析', '创建日志条目', 0.01, { operation: 'log_creation' });
                addDetailedLog('Qwen性能分析', '获取模型配置完成', 60, { operation: 'model_config', source: 'database' });
                addDetailedLog('Qwen性能分析', '模型参数解析完成', 0.09, { operation: 'params_parsing' });
                addDetailedLog('Qwen性能分析', '记录请求日志', 0.35, { operation: 'request_logging' });

                // HTTP详细分析
                addDetailedLog('HTTP性能分析', '请求体序列化完成', 0.06, { size: '724 bytes' });
                addDetailedLog('HTTP性能分析', 'HTTP请求创建完成', 0.02, { method: 'POST' });
                addDetailedLog('HTTP性能分析', '请求头设置完成', 0.003, { headers: 'Content-Type, Authorization' });
                addDetailedLog('HTTP性能分析', 'HTTP请求发送完成', 1780, {
                    url: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation',
                    status: 'success'
                });
                addDetailedLog('HTTP性能分析', '响应体读取完成', 0.08, { size: '714 bytes' });

                // 数据处理分析
                addDetailedLog('Qwen性能分析', 'JSON响应解析完成', 0.29, { operation: 'json_parse' });
                addDetailedLog('Qwen性能分析', '内容字符串提取完成', 0.01, { content_length: 291 });
                addDetailedLog('Qwen性能分析', '响应结构解析完成', 0.03, {
                    question_type: '单选题',
                    options_count: 4
                });

                // 缓存操作分析
                addDetailedLog('业务性能分析', 'Qwen数据预处理完成', 0.01, { operation: 'data_preprocessing' });
                addDetailedLog('业务性能分析', '缓存键生成完成', 0.01, { cache_key: 'a7cded1273edc8d87a206c96a1b265ad' });
                addDetailedLog('Redis性能分析', 'Redis缓存查询完成', 40, {
                    operation: 'cache_hit',
                    result: 'found',
                    questions_count: 1
                });

                // 数据库操作分析
                addDetailedLog('MySQL性能分析', '响应次数更新完成', 129, {
                    operation: 'increment_response',
                    question_id: 105
                });
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 支持回车键提交
            document.getElementById('imageUrl').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    testSearch();
                }
            });
        });
    </script>
</body>
</html>
