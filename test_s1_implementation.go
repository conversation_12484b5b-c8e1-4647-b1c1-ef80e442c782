package main

import (
	"encoding/json"
	"fmt"
	"solve_api/internal/model"
	"solve_api/internal/service"
)

func main() {
	fmt.Println("=== S1需求实现测试 ===")

	// 测试1：DeepSeek响应解析（多选题样本）
	testDeepSeekMultipleChoice()

	// 测试2：DeepSeek响应解析（单选题样本）
	testDeepSeekSingleChoice()

	// 测试3：DeepSeek响应解析（判断题样本）
	testDeepSeekJudgment()

	// 测试4：数据库字段映射
	testDatabaseFieldMapping()

	// 测试5：响应结构验证
	testResponseStructure()
}

// 测试多选题DeepSeek响应解析
func testDeepSeekMultipleChoice() {
	fmt.Println("\n--- 测试1：多选题DeepSeek响应解析 ---")

	// S1.md中的多选题样本
	deepseekContent := `{
  "question_type": "多选题",
  "question_text": "同车道行驶的机动车,后车应当与前车保持足以采取紧急制动措施的安全距离。下列哪种情形不得超车？",
  "options": {
    "A": "前车正在左转弯的",
    "B": "前车正在上下乘客的",
    "C": "前车正在超车的",
    "D": "前车正在掉头的"
  },
  "answer": {
    "A": "前车正在左转弯的",
    "C": "前车正在超车的",
    "D": "前车正在掉头的"
  },
  "analysis": "根据《道路交通安全法》的相关规定，同车道行驶的机动车，后车应当与前车保持足以采取紧急制动措施的安全距离。在以下几种情形下，后车不得超车：1.前车正在左转弯的；2.前车正在超车的；3.前车正在掉头的。这些情况下超车会增加交通事故的风险，因此法律明确禁止。选项B"前车正在上下乘客的"虽然也是需要注意的情况，但并不直接属于法律规定的不得超车的情形。"
}`

	var rawResponse map[string]interface{}
	if err := json.Unmarshal([]byte(deepseekContent), &rawResponse); err != nil {
		fmt.Printf("❌ JSON解析失败: %v\n", err)
		return
	}

	// 模拟AIService的parseDeepSeekResponse方法
	result := simulateParseDeepSeekResponse(rawResponse)

	fmt.Printf("✅ 题目类型: %s\n", result.QuestionType)
	fmt.Printf("✅ 答案: %s\n", result.Answer)
	fmt.Printf("✅ 选项数量: %d\n", len(result.Options))
	fmt.Printf("✅ 分析长度: %d\n", len(result.Analysis))

	// 验证多选题答案格式
	expectedAnswer := "A,C,D" // 期望的多选题答案格式
	if result.Answer == expectedAnswer {
		fmt.Printf("✅ 多选题答案格式正确: %s\n", result.Answer)
	} else {
		fmt.Printf("⚠️ 多选题答案格式需要调整: 期望 %s, 实际 %s\n", expectedAnswer, result.Answer)
	}
}

// 测试单选题DeepSeek响应解析
func testDeepSeekSingleChoice() {
	fmt.Println("\n--- 测试2：单选题DeepSeek响应解析 ---")

	// S1.md中的单选题样本
	deepseekContent := `{
  "question_type": "单选题",
  "question_text": "如图所示，驾驶机动车遇到这种情形时，以下做法正确的是什么？",
  "options": {
    "A": "减速靠右，待对向车辆通过后，再缓慢超越行人",
    "B": "鸣喇叭提示左侧车辆后，保持原速行驶",
    "C": "加速行驶，在对面来车交会前超过行人",
    "D": "鸣喇叭提示行人后，保持原速行驶"
  },
  "answer": {
    "A": "减速靠右，待对向车辆通过后，再缓慢超越行人"
  },
  "analysis": "在驾驶机动车遇到对向有来车且前方有行人横穿马路的情形时，最安全的做法是减速靠右，等待对向车辆通过后，再缓慢超越行人。这样可以避免与对向车辆发生碰撞，同时确保行人的安全。鸣喇叭或保持原速行驶可能会对行人造成惊吓或无法及时避让，加速行驶则增加了与对向车辆或行人发生碰撞的风险。因此，选项A是正确的做法。"
}`

	var rawResponse map[string]interface{}
	if err := json.Unmarshal([]byte(deepseekContent), &rawResponse); err != nil {
		fmt.Printf("❌ JSON解析失败: %v\n", err)
		return
	}

	result := simulateParseDeepSeekResponse(rawResponse)

	fmt.Printf("✅ 题目类型: %s\n", result.QuestionType)
	fmt.Printf("✅ 答案: %s\n", result.Answer)
	fmt.Printf("✅ 选项数量: %d\n", len(result.Options))

	// 验证单选题答案格式
	expectedAnswer := "A" // 期望的单选题答案格式
	if result.Answer == expectedAnswer {
		fmt.Printf("✅ 单选题答案格式正确: %s\n", result.Answer)
	} else {
		fmt.Printf("⚠️ 单选题答案格式需要调整: 期望 %s, 实际 %s\n", expectedAnswer, result.Answer)
	}
}

// 测试判断题DeepSeek响应解析
func testDeepSeekJudgment() {
	fmt.Println("\n--- 测试3：判断题DeepSeek响应解析 ---")

	// S1.md中的判断题样本
	deepseekContent := `{
  "question_type": "判断题",
  "question_text": "一个周期内记分满12分的驾驶人,拒不参加学习也不接受考试的,公安机关交通管理部门将公告其驾驶证停止使用。",
  "options": {
    "Y": "正确",
    "N": "错误"
  },
  "answer": {
    "Y": "正确"
  },
  "analysis": "根据《道路交通安全法》及相关规定，驾驶人在一个记分周期内累积记分达到12分的，应当在规定时间内到公安机关交通管理部门接受教育和考试。如果驾驶人拒不参加学习也不接受考试，公安机关交通管理部门有权公告其驾驶证停止使用。因此，题目描述的情况是正确的。"
}`

	var rawResponse map[string]interface{}
	if err := json.Unmarshal([]byte(deepseekContent), &rawResponse); err != nil {
		fmt.Printf("❌ JSON解析失败: %v\n", err)
		return
	}

	result := simulateParseDeepSeekResponse(rawResponse)

	fmt.Printf("✅ 题目类型: %s\n", result.QuestionType)
	fmt.Printf("✅ 答案: %s\n", result.Answer)
	fmt.Printf("✅ 选项数量: %d\n", len(result.Options))

	// 验证判断题答案格式
	expectedAnswer := "Y" // 期望的判断题答案格式
	if result.Answer == expectedAnswer {
		fmt.Printf("✅ 判断题答案格式正确: %s\n", result.Answer)
	} else {
		fmt.Printf("⚠️ 判断题答案格式需要调整: 期望 %s, 实际 %s\n", expectedAnswer, result.Answer)
	}
}

// 测试数据库字段映射
func testDatabaseFieldMapping() {
	fmt.Println("\n--- 测试4：数据库字段映射 ---")

	// 创建预处理数据
	preprocessed := &model.PreprocessedQuestion{
		QuestionType: "多选题",
		QuestionText: "测试题目内容",
		OptionsA:     "选项A",
		OptionsB:     "选项B",
		OptionsC:     "选项C",
		OptionsD:     "选项D",
		Subject:      "交通法规",
		Grade:        "驾考",
		Difficulty:   3,
	}

	// 模拟DeepSeek结果
	deepseekResult := &service.DeepseekResult{
		Analysis: "详细的题目解析内容",
		Answer:   "A,C,D",
		Options: map[string]string{
			"A": "DeepSeek选项A",
			"B": "DeepSeek选项B",
			"C": "DeepSeek选项C",
			"D": "DeepSeek选项D",
		},
		QuestionType: "多选题",
		RawContent:   "DeepSeek原始响应内容",
	}

	// 创建Question并测试FromPreprocessedWithDeepSeek方法
	question := &model.Question{}
	question.FromPreprocessedWithDeepSeek(preprocessed, "http://example.com/image.jpg", "Qwen原始响应", deepseekResult)

	// 验证字段映射
	fmt.Printf("✅ 题目类型: %s\n", question.QuestionType)
	fmt.Printf("✅ 题目内容: %s\n", question.QuestionDoc)
	fmt.Printf("✅ 用户图片: %s\n", question.QuestionImgRaw)
	fmt.Printf("✅ 选项A: %s\n", question.OptionsA)
	fmt.Printf("✅ 选项B: %s\n", question.OptionsB)
	fmt.Printf("✅ 选项C: %s\n", question.OptionsC)
	fmt.Printf("✅ 选项D: %s\n", question.OptionsD)
	fmt.Printf("✅ 答案: %s\n", question.Answer)
	fmt.Printf("✅ 解析: %s\n", question.Analysis)
	fmt.Printf("✅ Qwen原始数据长度: %d\n", len(question.RawQwen))
	fmt.Printf("✅ DeepSeek原始数据长度: %d\n", len(question.RawDeepseek))
}

// 测试响应结构
func testResponseStructure() {
	fmt.Println("\n--- 测试5：响应结构验证 ---")

	// 创建测试题目
	question := &model.Question{
		ID:             1,
		QuestionType:   "单选题",
		QuestionDoc:    "测试题目内容",
		QuestionImg:    "http://example.com/question.jpg",
		QuestionImgRaw: "http://example.com/raw.jpg",
		OptionsA:       "选项A",
		OptionsB:       "选项B",
		OptionsC:       "选项C",
		OptionsD:       "选项D",
		Answer:         "A",
		Analysis:       "详细解析",
		Subject:        "测试学科",
		Grade:          "测试年级",
		Difficulty:     3,
		SourceModel:    "qwen-vl-plus,deepseek-chat",
	}

	// 设置兼容选项
	options := map[string]string{
		"A": question.OptionsA,
		"B": question.OptionsB,
		"C": question.OptionsC,
		"D": question.OptionsD,
	}
	question.SetOptions(options)

	// 测试ToSearchResponse方法
	response := question.ToSearchResponse(false, 1500)

	// 验证S1需求3的新增字段
	fmt.Printf("✅ 题目图片: %s\n", response.QuestionImg)
	fmt.Printf("✅ 用户原始图片: %s\n", response.QuestionImgRaw)
	fmt.Printf("✅ 关联题目数量: %d\n", len(response.AssociatedQuestions))

	// 测试关联题目转换
	association := question.ToAssociation()
	fmt.Printf("✅ 关联题目ID: %d\n", association.ID)
	fmt.Printf("✅ 关联题目类型: %s\n", association.QuestionType)
	fmt.Printf("✅ 关联题目选项数量: %d\n", len(association.Options))
}

// 模拟parseDeepSeekResponse方法的核心逻辑
func simulateParseDeepSeekResponse(rawResponse map[string]interface{}) *service.DeepseekResult {
	result := &service.DeepseekResult{}

	// 提取分析内容
	if analysis, exists := rawResponse["analysis"]; exists {
		if str, ok := analysis.(string); ok {
			result.Analysis = str
		}
	}

	// 提取答案（支持对象格式）
	if answerValue, exists := rawResponse["answer"]; exists {
		if answerObj, ok := answerValue.(map[string]interface{}); ok {
			var answers []string
			for _, option := range []string{"A", "B", "C", "D", "Y", "N"} {
				if _, hasOption := answerObj[option]; hasOption {
					answers = append(answers, option)
				}
			}
			if len(answers) > 0 {
				result.Answer = fmt.Sprintf("%s", answers[0])
				if len(answers) > 1 {
					result.Answer = fmt.Sprintf("%s,%s", answers[0], answers[1])
					if len(answers) > 2 {
						result.Answer = fmt.Sprintf("%s,%s,%s", answers[0], answers[1], answers[2])
					}
				}
			}
		}
	}

	// 提取选项
	result.Options = make(map[string]string)
	if optionsValue, exists := rawResponse["options"]; exists {
		if optionsObj, ok := optionsValue.(map[string]interface{}); ok {
			for key, value := range optionsObj {
				if str, ok := value.(string); ok {
					result.Options[key] = str
				}
			}
		}
	}

	// 提取题目类型
	if questionType, exists := rawResponse["question_type"]; exists {
		if str, ok := questionType.(string); ok {
			result.QuestionType = str
		}
	}

	return result
}
