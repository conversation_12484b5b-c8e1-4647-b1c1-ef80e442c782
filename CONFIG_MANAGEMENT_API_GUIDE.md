# 配置管理API前端接入指南

## 📋 概述

本文档专门为前端开发团队提供配置管理相关API的接入指南，包括模型配置、价格配置、系统配置等管理功能。

## 🔐 认证说明

配置管理API需要管理员权限，请在请求头中携带管理员token：
```http
Authorization: Bearer admin_token
```

## 🤖 模型配置管理

### 数据结构

#### ModelConfig 模型配置
```typescript
interface ModelConfig {
  id: number;
  name: string;           // 模型名称，如 "qwen-vl-plus"
  api_url: string;        // API地址
  api_key: string;        // API密钥
  params: string;         // JSON格式的参数配置
  status: number;         // 1=启用, 2=禁用
  created_at: string;
  updated_at: string;
}
```

#### 参数配置示例
```json
{
  "temperature": 0.3,
  "max_tokens": 1500,
  "top_p": 0.8,
  "response_format": {"type": "json_object"},
  "detail": "high"
}
```

### API接口

#### 1. 获取模型配置列表
```javascript
const getModelConfigs = async (page = 1, pageSize = 10) => {
  const response = await fetch(`/api/v1/admin/model?page=${page}&page_size=${pageSize}`, {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};

// 响应格式
{
  "code": 200,
  "message": "获取模型配置列表成功",
  "data": {
    "list": [ModelConfig],
    "total": 10,
    "page": 1,
    "page_size": 10
  }
}
```

#### 2. 获取启用的模型配置
```javascript
const getEnabledModels = async () => {
  const response = await fetch('/api/v1/admin/model/enabled', {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};
```

#### 3. 获取模型配置详情
```javascript
const getModelConfig = async (id) => {
  const response = await fetch(`/api/v1/admin/model/${id}`, {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};
```

#### 4. 创建模型配置
```javascript
const createModelConfig = async (modelData) => {
  const response = await fetch('/api/v1/admin/model', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer admin_token'
    },
    body: JSON.stringify({
      name: modelData.name,
      api_url: modelData.apiUrl,
      api_key: modelData.apiKey,
      params: JSON.stringify(modelData.params),
      status: modelData.status || 1
    })
  });
  return response.json();
};

// 请求示例
const newModel = {
  name: "gpt-4-vision",
  apiUrl: "https://api.openai.com/v1/chat/completions",
  apiKey: "sk-xxx",
  params: {
    temperature: 0.3,
    max_tokens: 2000,
    model: "gpt-4-vision-preview"
  },
  status: 1
};
```

#### 5. 更新模型配置
```javascript
const updateModelConfig = async (id, modelData) => {
  const response = await fetch(`/api/v1/admin/model/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer admin_token'
    },
    body: JSON.stringify(modelData)
  });
  return response.json();
};
```

#### 6. 更新模型状态
```javascript
const updateModelStatus = async (id, status) => {
  const response = await fetch(`/api/v1/admin/model/${id}/status`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer admin_token'
    },
    body: JSON.stringify({ status })
  });
  return response.json();
};
```

#### 7. ~~删除模型配置~~ (已移除)
```javascript
// ⚠️ 删除模型配置接口已移除
// 为保持系统稳定性和兼容性，模型配置不支持删除操作
// const deleteModelConfig = async (id) => {
//   // 此接口已不可用
// };
```

## 💰 价格配置管理

### 数据结构

#### PriceConfig 价格配置
```typescript
interface PriceConfig {
  id: number;
  service_type: number;   // 服务类型：1=拍照搜题
  price: number;          // 价格（元）
  description: string;    // 描述
  is_default: boolean;    // 是否为默认价格
  created_at: string;
  updated_at: string;
}
```

### API接口

#### 1. 获取价格配置列表
```javascript
const getPriceConfigs = async (page = 1, pageSize = 10) => {
  const response = await fetch(`/api/v1/admin/price?page=${page}&page_size=${pageSize}`, {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};
```

#### 2. 创建价格配置
```javascript
const createPriceConfig = async (priceData) => {
  const response = await fetch('/api/v1/admin/price', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer admin_token'
    },
    body: JSON.stringify({
      service_type: priceData.serviceType,
      price: priceData.price,
      description: priceData.description
    })
  });
  return response.json();
};

// 请求示例
const newPrice = {
  serviceType: 1,
  price: 0.1,
  description: "拍照搜题标准价格"
};
```

#### 3. 根据服务类型获取价格
```javascript
const getPriceByService = async (serviceType, userId = null) => {
  let url = `/api/v1/admin/price/service/${serviceType}`;
  if (userId) url += `?user_id=${userId}`;
  
  const response = await fetch(url, {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};
```

#### 4. 设置默认价格
```javascript
const setDefaultPrice = async (serviceType, price) => {
  const response = await fetch(`/api/v1/admin/price/service/${serviceType}/default`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer admin_token'
    },
    body: JSON.stringify({ price })
  });
  return response.json();
};
```

#### 5. 设置用户专属价格
```javascript
const setUserPrice = async (serviceType, userId, price) => {
  const response = await fetch(`/api/v1/admin/price/service/${serviceType}/user/${userId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer admin_token'
    },
    body: JSON.stringify({ price })
  });
  return response.json();
};
```

## ⚙️ 系统配置管理

### API接口

#### 1. 获取系统信息
```javascript
const getSystemInfo = async () => {
  const response = await fetch('/api/v1/admin/system/info', {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};

// 响应格式
{
  "code": 200,
  "message": "获取系统信息成功",
  "data": {
    "system_stats": {
      "total_users": 100,
      "total_apps": 50,
      "total_questions": 1000
    },
    "configs": {
      "app_name": "solve_api",
      "version": "1.0.0",
      "max_apps_per_user": 5
    },
    "version": "1.0.0",
    "build_time": "2024-01-01"
  }
}
```

#### 2. 获取系统健康状态
```javascript
const getSystemHealth = async () => {
  const response = await fetch('/api/v1/admin/system/health', {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};
```

#### 3. 获取仪表板数据
```javascript
const getDashboard = async () => {
  const response = await fetch('/api/v1/admin/system/dashboard', {
    headers: { 'Authorization': 'Bearer admin_token' }
  });
  return response.json();
};
```

#### 4. 更新系统配置
```javascript
const updateSystemConfig = async (configs) => {
  const response = await fetch('/api/v1/admin/system/config', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer admin_token'
    },
    body: JSON.stringify(configs)
  });
  return response.json();
};

// 请求示例
const configs = {
  "max_apps_per_user": "10",
  "rate_limit": "20",
  "cache_ttl": "86400"
};
```

#### 5. 系统清理
```javascript
const cleanupSystem = async (options) => {
  const response = await fetch('/api/v1/admin/system/cleanup', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer admin_token'
    },
    body: JSON.stringify({
      clean_logs: options.cleanLogs || false,
      clean_stats: options.cleanStats || false,
      days: options.days || 30
    })
  });
  return response.json();
};
```

## 🎨 Vue.js 配置管理组件示例

### 模型配置管理组件
```vue
<template>
  <div class="model-config">
    <h2>模型配置管理</h2>
    
    <!-- 添加新模型 -->
    <div class="add-model">
      <h3>添加新模型</h3>
      <form @submit.prevent="createModel">
        <input v-model="newModel.name" placeholder="模型名称" required>
        <input v-model="newModel.apiUrl" placeholder="API地址" required>
        <input v-model="newModel.apiKey" placeholder="API密钥" required>
        <textarea v-model="newModel.paramsJson" placeholder="参数配置(JSON)"></textarea>
        <button type="submit">创建模型</button>
      </form>
    </div>
    
    <!-- 模型列表 -->
    <div class="model-list">
      <table>
        <thead>
          <tr>
            <th>模型名称</th>
            <th>API地址</th>
            <th>状态</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="model in models" :key="model.id">
            <td>{{ model.name }}</td>
            <td>{{ model.api_url }}</td>
            <td>
              <span :class="model.status === 1 ? 'status-enabled' : 'status-disabled'">
                {{ model.status === 1 ? '启用' : '禁用' }}
              </span>
            </td>
            <td>
              <button @click="toggleStatus(model)">
                {{ model.status === 1 ? '禁用' : '启用' }}
              </button>
              <button @click="editModel(model)">编辑</button>
              <button @click="deleteModel(model.id)">删除</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModelConfig',
  data() {
    return {
      models: [],
      newModel: {
        name: '',
        apiUrl: '',
        apiKey: '',
        paramsJson: '{"temperature": 0.3, "max_tokens": 1500}'
      }
    }
  },
  async mounted() {
    await this.loadModels();
  },
  methods: {
    async loadModels() {
      try {
        const data = await getModelConfigs();
        if (data.code === 200) {
          this.models = data.data.list;
        }
      } catch (error) {
        this.$message.error('加载模型配置失败');
      }
    },
    
    async createModel() {
      try {
        const params = JSON.parse(this.newModel.paramsJson);
        const modelData = {
          name: this.newModel.name,
          apiUrl: this.newModel.apiUrl,
          apiKey: this.newModel.apiKey,
          params: params
        };
        
        const data = await createModelConfig(modelData);
        if (data.code === 200) {
          this.$message.success('模型创建成功');
          this.resetForm();
          await this.loadModels();
        }
      } catch (error) {
        this.$message.error('创建失败：' + error.message);
      }
    },
    
    async toggleStatus(model) {
      try {
        const newStatus = model.status === 1 ? 2 : 1;
        const data = await updateModelStatus(model.id, newStatus);
        if (data.code === 200) {
          this.$message.success('状态更新成功');
          await this.loadModels();
        }
      } catch (error) {
        this.$message.error('状态更新失败');
      }
    },
    
    resetForm() {
      this.newModel = {
        name: '',
        apiUrl: '',
        apiKey: '',
        paramsJson: '{"temperature": 0.3, "max_tokens": 1500}'
      };
    }
  }
}
</script>
```
