package repository

import (
	"solve_api/internal/model"

	"gorm.io/gorm"
)

type BalanceLogRepository struct {
	db *gorm.DB
}

// NewBalanceLogRepository 创建余额日志仓库实例
func NewBalanceLogRepository(db *gorm.DB) *BalanceLogRepository {
	return &BalanceLogRepository{db: db}
}

// Create 创建余额日志
func (r *BalanceLogRepository) Create(log *model.BalanceLog) error {
	return r.db.Create(log).Error
}

// GetByUserID 根据用户ID获取余额日志列表
func (r *BalanceLogRepository) GetByUserID(userID uint, offset, limit int) ([]*model.BalanceLog, int64, error) {
	var logs []*model.BalanceLog
	var total int64

	// 获取总数
	if err := r.db.Model(&model.BalanceLog{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Where("user_id = ?", userID).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetByType 根据类型获取余额日志列表
func (r *BalanceLogRepository) GetByType(userID uint, logType int, offset, limit int) ([]*model.BalanceLog, int64, error) {
	var logs []*model.BalanceLog
	var total int64

	// 获取总数
	if err := r.db.Model(&model.BalanceLog{}).
		Where("user_id = ? AND type = ?", userID, logType).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Where("user_id = ? AND type = ?", userID, logType).
		Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetByID 根据ID获取余额日志
func (r *BalanceLogRepository) GetByID(id uint) (*model.BalanceLog, error) {
	var log model.BalanceLog
	err := r.db.First(&log, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &log, nil
}

// List 获取所有余额日志列表（管理员用）
func (r *BalanceLogRepository) List(offset, limit int) ([]*model.BalanceLog, int64, error) {
	var logs []*model.BalanceLog
	var total int64

	// 获取总数
	if err := r.db.Model(&model.BalanceLog{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	err := r.db.Offset(offset).
		Limit(limit).
		Order("created_at DESC").
		Find(&logs).Error
	if err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetUserTotalRecharge 获取用户总充值金额
func (r *BalanceLogRepository) GetUserTotalRecharge(userID uint) (float64, error) {
	var total float64
	err := r.db.Model(&model.BalanceLog{}).
		Where("user_id = ? AND type = ?", userID, model.BalanceLogTypeRecharge).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&total).Error
	return total, err
}

// GetUserTotalConsume 获取用户总消费金额
func (r *BalanceLogRepository) GetUserTotalConsume(userID uint) (float64, error) {
	var total float64
	err := r.db.Model(&model.BalanceLog{}).
		Where("user_id = ? AND type = ?", userID, model.BalanceLogTypeConsume).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&total).Error
	return total, err
}

// GetUserTotalRefund 获取用户总退款金额
func (r *BalanceLogRepository) GetUserTotalRefund(userID uint) (float64, error) {
	var total float64
	err := r.db.Model(&model.BalanceLog{}).
		Where("user_id = ? AND type = ?", userID, model.BalanceLogTypeRefund).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&total).Error
	return total, err
}

// GetRecentLogs 获取最近的余额日志
func (r *BalanceLogRepository) GetRecentLogs(userID uint, limit int) ([]*model.BalanceLog, error) {
	var logs []*model.BalanceLog
	err := r.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Find(&logs).Error
	return logs, err
}
