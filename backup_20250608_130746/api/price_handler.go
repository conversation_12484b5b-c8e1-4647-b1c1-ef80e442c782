package api

import (
	"solve_api/internal/model"
	"solve_api/internal/service"
	"solve_api/internal/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

type PriceHandler struct {
	priceService *service.PriceService
}

// NewPriceHandler 创建价格处理器实例
func NewPriceHandler(priceService *service.PriceService) *PriceHandler {
	return &PriceHandler{
		priceService: priceService,
	}
}

// CreatePriceConfig 创建价格配置
func (h *PriceHandler) CreatePriceConfig(c *gin.Context) {
	var req model.PriceConfigCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	priceConfig, err := h.priceService.CreatePriceConfig(&req)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "价格配置创建成功", priceConfig)
}

// GetPriceConfig 获取价格配置详情
func (h *PriceHandler) GetPriceConfig(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		utils.BadRequest(c, "价格配置ID不能为空")
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "价格配置ID格式错误")
		return
	}

	priceConfig, err := h.priceService.GetPriceConfig(uint(id))
	if err != nil {
		if err.Error() == "价格配置不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取价格配置成功", priceConfig)
}

// UpdatePriceConfig 更新价格配置
func (h *PriceHandler) UpdatePriceConfig(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		utils.BadRequest(c, "价格配置ID不能为空")
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "价格配置ID格式错误")
		return
	}

	var req model.PriceConfigUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	priceConfig, err := h.priceService.UpdatePriceConfig(uint(id), &req)
	if err != nil {
		if err.Error() == "价格配置不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "价格配置更新成功", priceConfig)
}

// DeletePriceConfig 删除价格配置
func (h *PriceHandler) DeletePriceConfig(c *gin.Context) {
	idStr := c.Param("id")
	if idStr == "" {
		utils.BadRequest(c, "价格配置ID不能为空")
		return
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "价格配置ID格式错误")
		return
	}

	err = h.priceService.DeletePriceConfig(uint(id))
	if err != nil {
		if err.Error() == "价格配置不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "价格配置删除成功", nil)
}

// GetPriceConfigList 获取价格配置列表
func (h *PriceHandler) GetPriceConfigList(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	priceConfigs, total, err := h.priceService.GetPriceConfigList(page, pageSize)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	result := gin.H{
		"list":      priceConfigs,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	}

	utils.SuccessWithMessage(c, "获取价格配置列表成功", result)
}

// GetPriceByService 根据服务类型获取价格
func (h *PriceHandler) GetPriceByService(c *gin.Context) {
	serviceTypeStr := c.Param("service_type")
	if serviceTypeStr == "" {
		utils.BadRequest(c, "服务类型不能为空")
		return
	}

	serviceType, err := strconv.Atoi(serviceTypeStr)
	if err != nil {
		utils.BadRequest(c, "服务类型格式错误")
		return
	}

	userIDStr := c.Query("user_id")
	var userID uint = 0
	if userIDStr != "" {
		id, err := strconv.ParseUint(userIDStr, 10, 32)
		if err != nil {
			utils.BadRequest(c, "用户ID格式错误")
			return
		}
		userID = uint(id)
	}

	price, err := h.priceService.GetServicePrice(serviceType, userID)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	result := gin.H{
		"service_type": serviceType,
		"user_id":      userID,
		"price":        price,
	}

	utils.SuccessWithMessage(c, "获取服务价格成功", result)
}

// SetDefaultPrice 设置默认价格
func (h *PriceHandler) SetDefaultPrice(c *gin.Context) {
	serviceTypeStr := c.Param("service_type")
	if serviceTypeStr == "" {
		utils.BadRequest(c, "服务类型不能为空")
		return
	}

	serviceType, err := strconv.Atoi(serviceTypeStr)
	if err != nil {
		utils.BadRequest(c, "服务类型格式错误")
		return
	}

	var req struct {
		Price float64 `json:"price" binding:"required,min=0"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	err = h.priceService.SetDefaultPrice(serviceType, req.Price)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "设置默认价格成功", nil)
}

// SetUserPrice 设置用户定制价格
func (h *PriceHandler) SetUserPrice(c *gin.Context) {
	serviceTypeStr := c.Param("service_type")
	if serviceTypeStr == "" {
		utils.BadRequest(c, "服务类型不能为空")
		return
	}

	serviceType, err := strconv.Atoi(serviceTypeStr)
	if err != nil {
		utils.BadRequest(c, "服务类型格式错误")
		return
	}

	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	var req struct {
		Price float64 `json:"price" binding:"required,min=0"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	err = h.priceService.SetUserPrice(serviceType, uint(userID), req.Price)
	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "设置用户定制价格成功", nil)
}
