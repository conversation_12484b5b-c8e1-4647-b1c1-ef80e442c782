package api

import (
	"solve_api/internal/model"
	"solve_api/internal/service"
	"solve_api/internal/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

type BalanceHandler struct {
	balanceService *service.BalanceService
}

// NewBalanceHandler 创建余额处理器实例
func NewBalanceHandler(balanceService *service.BalanceService) *BalanceHandler {
	return &BalanceHandler{
		balanceService: balanceService,
	}
}

// Recharge 用户充值
// @Summary 用户充值
// @Description 为用户账户充值
// @Tags 余额管理
// @Accept json
// @Produce json
// @Param user_id path uint true "用户ID"
// @Param request body model.UserRechargeRequest true "充值请求参数"
// @Success 200 {object} utils.Response{data=model.BalanceLogResponse} "充值成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "用户不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/user/{user_id}/recharge [post]
func (h *BalanceHandler) Recharge(c *gin.Context) {
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	var req model.UserRechargeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	balanceLog, err := h.balanceService.Recharge(uint(userID), &req)
	if err != nil {
		if err.Error() == "用户不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "充值成功", balanceLog)
}

// GetBalance 获取用户余额
// @Summary 获取用户余额
// @Description 获取用户当前余额
// @Tags 余额管理
// @Accept json
// @Produce json
// @Param user_id path uint true "用户ID"
// @Success 200 {object} utils.Response{data=float64} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "用户不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/user/{user_id}/balance [get]
func (h *BalanceHandler) GetBalance(c *gin.Context) {
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	balance, err := h.balanceService.GetBalance(uint(userID))
	if err != nil {
		if err.Error() == "用户不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	result := gin.H{
		"balance": balance,
	}

	utils.SuccessWithMessage(c, "获取余额成功", result)
}

// GetBalanceLogs 获取余额变动日志
// @Summary 获取余额变动日志
// @Description 分页获取用户余额变动日志
// @Tags 余额管理
// @Accept json
// @Produce json
// @Param user_id path uint true "用户ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param type query int false "日志类型 1:充值 2:消费 3:退款"
// @Success 200 {object} utils.Response{data=[]model.BalanceLogResponse} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/user/{user_id}/balance/logs [get]
func (h *BalanceHandler) GetBalanceLogs(c *gin.Context) {
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	logType, _ := strconv.Atoi(c.Query("type"))

	var logs []*model.BalanceLogResponse
	var total int64

	if logType > 0 {
		logs, total, err = h.balanceService.GetBalanceLogsByType(uint(userID), logType, page, pageSize)
	} else {
		logs, total, err = h.balanceService.GetBalanceLogs(uint(userID), page, pageSize)
	}

	if err != nil {
		utils.ServerError(c, err.Error())
		return
	}

	result := gin.H{
		"list":      logs,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	}

	utils.SuccessWithMessage(c, "获取余额日志成功", result)
}

// GetBalanceStats 获取余额统计
// @Summary 获取余额统计
// @Description 获取用户余额统计信息
// @Tags 余额管理
// @Accept json
// @Produce json
// @Param user_id path uint true "用户ID"
// @Success 200 {object} utils.Response{data=map[string]interface{}} "获取成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "用户不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/user/{user_id}/balance/stats [get]
func (h *BalanceHandler) GetBalanceStats(c *gin.Context) {
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	stats, err := h.balanceService.GetBalanceStats(uint(userID))
	if err != nil {
		if err.Error() == "用户不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "获取余额统计成功", stats)
}

// Refund 退款
// @Summary 退款
// @Description 为用户退款（管理员操作）
// @Tags 余额管理
// @Accept json
// @Produce json
// @Param user_id path uint true "用户ID"
// @Param request body model.UserRechargeRequest true "退款请求参数"
// @Success 200 {object} utils.Response{data=model.BalanceLogResponse} "退款成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "用户不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/admin/user/{user_id}/refund [post]
func (h *BalanceHandler) Refund(c *gin.Context) {
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	var req model.UserRechargeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, utils.FormatValidationError(err))
		return
	}

	balanceLog, err := h.balanceService.Refund(uint(userID), req.Amount, req.Description, 0)
	if err != nil {
		if err.Error() == "用户不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "退款成功", balanceLog)
}

// CheckBalance 检查余额
// @Summary 检查余额
// @Description 检查用户余额是否足够支付指定服务
// @Tags 余额管理
// @Accept json
// @Produce json
// @Param user_id path uint true "用户ID"
// @Param service_type query int true "服务类型"
// @Success 200 {object} utils.Response{data=map[string]interface{}} "检查成功"
// @Failure 400 {object} utils.Response "请求参数错误"
// @Failure 404 {object} utils.Response "用户不存在"
// @Failure 500 {object} utils.Response "服务器内部错误"
// @Router /api/v1/user/{user_id}/balance/check [get]
func (h *BalanceHandler) CheckBalance(c *gin.Context) {
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		utils.BadRequest(c, "用户ID不能为空")
		return
	}

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "用户ID格式错误")
		return
	}

	serviceTypeStr := c.Query("service_type")
	if serviceTypeStr == "" {
		utils.BadRequest(c, "服务类型不能为空")
		return
	}

	serviceType, err := strconv.Atoi(serviceTypeStr)
	if err != nil {
		utils.BadRequest(c, "服务类型格式错误")
		return
	}

	sufficient, price, err := h.balanceService.CheckBalance(uint(userID), serviceType)
	if err != nil {
		if err.Error() == "用户不存在" {
			utils.NotFound(c, err.Error())
			return
		}
		utils.ServerError(c, err.Error())
		return
	}

	result := gin.H{
		"sufficient": sufficient,
		"price":      price,
	}

	utils.SuccessWithMessage(c, "余额检查完成", result)
}
