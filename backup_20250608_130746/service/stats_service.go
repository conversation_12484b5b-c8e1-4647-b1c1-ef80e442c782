package service

import (
	"fmt"
	"solve_api/internal/model"
	"solve_api/internal/repository"
)

type StatsService struct {
	statsRepo    *repository.StatsRepository
	apiLogRepo   *repository.APILogRepository
	userRepo     *repository.UserRepository
	appRepo      *repository.ApplicationRepository
	questionRepo *repository.QuestionRepository
}

// NewStatsService 创建统计服务实例
func NewStatsService(
	statsRepo *repository.StatsRepository,
	apiLogRepo *repository.APILogRepository,
	userRepo *repository.UserRepository,
	appRepo *repository.ApplicationRepository,
	questionRepo *repository.QuestionRepository,
) *StatsService {
	return &StatsService{
		statsRepo:    statsRepo,
		apiLogRepo:   apiLogRepo,
		userRepo:     userRepo,
		appRepo:      appRepo,
		questionRepo: questionRepo,
	}
}

// GenerateSystemStats 生成系统统计数据
func (s *StatsService) GenerateSystemStats(date string) (*model.SystemStatsResponse, error) {
	// 获取或创建系统统计记录
	stats, err := s.statsRepo.GetSystemStatsByDate(date)
	if err != nil {
		return nil, fmt.Errorf("查询系统统计失败: %w", err)
	}

	if stats == nil {
		stats = &model.SystemStats{
			Date: date,
		}
	}

	// 计算各项统计数据
	if err := s.calculateSystemStats(stats, date); err != nil {
		return nil, fmt.Errorf("计算系统统计失败: %w", err)
	}

	// 保存或更新统计数据
	if stats.ID == 0 {
		if err := s.statsRepo.CreateSystemStats(stats); err != nil {
			return nil, fmt.Errorf("创建系统统计失败: %w", err)
		}
	} else {
		if err := s.statsRepo.UpdateSystemStats(stats); err != nil {
			return nil, fmt.Errorf("更新系统统计失败: %w", err)
		}
	}

	return stats.ToResponse(), nil
}

// calculateSystemStats 计算系统统计数据
func (s *StatsService) calculateSystemStats(stats *model.SystemStats, date string) error {
	// 获取总用户数
	totalUsers, err := s.userRepo.GetTotalCount()
	if err != nil {
		return err
	}
	stats.TotalUsers = totalUsers

	// 获取总应用数
	totalApps, err := s.appRepo.GetTotalCount()
	if err != nil {
		return err
	}
	stats.TotalApps = totalApps

	// 获取总题目数
	totalQuestions, err := s.questionRepo.GetTotalCount()
	if err != nil {
		return err
	}
	stats.TotalQuestions = totalQuestions

	// 获取当日API调用统计
	totalCalls, _, errorCalls, err := s.apiLogRepo.GetCallsByDate(date)
	if err != nil {
		return err
	}
	stats.TotalAPICalls = totalCalls

	// 计算错误率
	if totalCalls > 0 {
		stats.ErrorRate = float64(errorCalls) / float64(totalCalls) * 100
	}

	// 获取当日收入
	revenue, err := s.apiLogRepo.GetRevenueByDate(date)
	if err != nil {
		return err
	}
	stats.TotalRevenue = revenue

	// 获取平均响应时间
	avgResponseTime, err := s.apiLogRepo.GetAvgResponseTimeByDate(date)
	if err != nil {
		return err
	}
	stats.AvgResponseTime = avgResponseTime

	// 计算缓存命中率（这里简化处理，实际需要从缓存统计中获取）
	stats.CacheHitRate = 85.0 // 示例值

	// 计算新增用户数（需要根据实际业务逻辑实现）
	stats.NewUsers = 0

	// 计算新增题目数（需要根据实际业务逻辑实现）
	stats.NewQuestions = 0

	// 计算活跃用户数（需要根据实际业务逻辑实现）
	stats.ActiveUsers = 0

	return nil
}

// GenerateUserStats 生成用户统计数据
func (s *StatsService) GenerateUserStats(userID uint, date string) (*model.UserStatsResponse, error) {
	// 获取或创建用户统计记录
	stats, err := s.statsRepo.GetUserStatsByDate(userID, date)
	if err != nil {
		return nil, fmt.Errorf("查询用户统计失败: %w", err)
	}

	if stats == nil {
		stats = &model.UserStats{
			UserID: userID,
			Date:   date,
		}
	}

	// 计算用户统计数据
	if err := s.calculateUserStats(stats, userID, date); err != nil {
		return nil, fmt.Errorf("计算用户统计失败: %w", err)
	}

	// 保存或更新统计数据
	if stats.ID == 0 {
		if err := s.statsRepo.CreateUserStats(stats); err != nil {
			return nil, fmt.Errorf("创建用户统计失败: %w", err)
		}
	} else {
		if err := s.statsRepo.UpdateUserStats(stats); err != nil {
			return nil, fmt.Errorf("更新用户统计失败: %w", err)
		}
	}

	return stats.ToResponse(), nil
}

// calculateUserStats 计算用户统计数据
func (s *StatsService) calculateUserStats(stats *model.UserStats, userID uint, date string) error {
	// 获取用户当日调用统计
	totalCalls, successCalls, errorCalls, totalCost, avgResponseTime, err := s.apiLogRepo.GetUserCallsByDate(userID, date)
	if err != nil {
		return err
	}

	stats.APICalls = totalCalls
	stats.SuccessCalls = successCalls
	stats.ErrorCalls = errorCalls
	stats.TotalCost = totalCost
	stats.AvgResponseTime = avgResponseTime

	return nil
}

// GetSystemStatsRange 获取系统统计数据范围
func (s *StatsService) GetSystemStatsRange(startDate, endDate string) ([]*model.SystemStatsResponse, error) {
	stats, err := s.statsRepo.GetSystemStatsRange(startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("查询系统统计范围失败: %w", err)
	}

	var result []*model.SystemStatsResponse
	for _, stat := range stats {
		result = append(result, stat.ToResponse())
	}

	return result, nil
}

// GetUserStatsRange 获取用户统计数据范围
func (s *StatsService) GetUserStatsRange(userID uint, startDate, endDate string) ([]*model.UserStatsResponse, error) {
	stats, err := s.statsRepo.GetUserStatsRange(userID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("查询用户统计范围失败: %w", err)
	}

	var result []*model.UserStatsResponse
	for _, stat := range stats {
		result = append(result, stat.ToResponse())
	}

	return result, nil
}

// GetSystemStatsTotal 获取系统总统计
func (s *StatsService) GetSystemStatsTotal() (map[string]interface{}, error) {
	return s.statsRepo.GetSystemStatsTotal()
}

// GetUserStatsTotal 获取用户总统计
func (s *StatsService) GetUserStatsTotal(userID uint) (map[string]interface{}, error) {
	return s.statsRepo.GetUserStatsTotal(userID)
}

// GetTopUsers 获取排行榜用户
func (s *StatsService) GetTopUsers(date string, limit int) (map[string]interface{}, error) {
	// 获取API调用次数最多的用户
	topByAPICalls, err := s.statsRepo.GetTopUsersByAPICalls(date, limit)
	if err != nil {
		return nil, fmt.Errorf("获取API调用排行失败: %w", err)
	}

	// 获取费用最高的用户
	topByCost, err := s.statsRepo.GetTopUsersByCost(date, limit)
	if err != nil {
		return nil, fmt.Errorf("获取费用排行失败: %w", err)
	}

	var topByAPICallsResponse []*model.UserStatsResponse
	for _, stat := range topByAPICalls {
		topByAPICallsResponse = append(topByAPICallsResponse, stat.ToResponse())
	}

	var topByCostResponse []*model.UserStatsResponse
	for _, stat := range topByCost {
		topByCostResponse = append(topByCostResponse, stat.ToResponse())
	}

	return map[string]interface{}{
		"top_by_api_calls": topByAPICallsResponse,
		"top_by_cost":      topByCostResponse,
	}, nil
}

// GetRecentStats 获取最近的统计数据
func (s *StatsService) GetRecentStats(days int) (map[string]interface{}, error) {
	// 获取最近的系统统计
	systemStats, err := s.statsRepo.GetRecentSystemStats(days)
	if err != nil {
		return nil, fmt.Errorf("获取最近系统统计失败: %w", err)
	}

	var systemStatsResponse []*model.SystemStatsResponse
	for _, stat := range systemStats {
		systemStatsResponse = append(systemStatsResponse, stat.ToResponse())
	}

	return map[string]interface{}{
		"system_stats": systemStatsResponse,
	}, nil
}

// GenerateDailyStats 生成每日统计（定时任务调用）
func (s *StatsService) GenerateDailyStats() error {
	yesterday := model.GetYesterdayDateString()

	// 生成昨天的系统统计
	if _, err := s.GenerateSystemStats(yesterday); err != nil {
		return fmt.Errorf("生成昨日系统统计失败: %w", err)
	}

	// 获取所有用户并生成统计
	// 这里简化处理，实际应该分批处理大量用户
	users, err := s.userRepo.GetAll()
	if err != nil {
		return fmt.Errorf("获取用户列表失败: %w", err)
	}

	for _, user := range users {
		if _, err := s.GenerateUserStats(user.ID, yesterday); err != nil {
			// 记录错误但不中断处理
			continue
		}
	}

	return nil
}
