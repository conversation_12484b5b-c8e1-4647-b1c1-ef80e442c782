package service

import (
	"context"
	"fmt"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"solve_api/internal/utils"
	"time"

	"github.com/redis/go-redis/v9"
)

type AdminService struct {
	adminRepo  *repository.AdminRepository
	userRepo   *repository.UserRepository
	redis      *redis.Client
	smsService utils.SMSService
}

// NewAdminService 创建管理员服务实例
func NewAdminService(adminRepo *repository.AdminRepository, userRepo *repository.UserRepository, redis *redis.Client, smsService utils.SMSService) *AdminService {
	return &AdminService{
		adminRepo:  adminRepo,
		userRepo:   userRepo,
		redis:      redis,
		smsService: smsService,
	}
}

// Login 管理员登录
func (s *AdminService) Login(req *model.AdminLoginRequest) (*model.AdminResponse, error) {
	// 1. 验证手机号格式
	if !utils.ValidatePhone(req.Phone) {
		return nil, fmt.Errorf("手机号格式不正确")
	}

	// 2. 查找管理员
	admin, err := s.adminRepo.GetByPhone(req.Phone)
	if err != nil {
		return nil, fmt.Errorf("查询管理员失败: %w", err)
	}
	if admin == nil {
		return nil, fmt.Errorf("管理员不存在")
	}

	// 3. 验证密码
	if !utils.CheckPassword(req.Password, admin.Password) {
		return nil, fmt.Errorf("密码错误")
	}

	return admin.ToResponse(), nil
}

// ChangePassword 管理员修改密码（使用原密码）
func (s *AdminService) ChangePassword(adminID uint, req *model.AdminChangePasswordRequest) (*model.AdminResponse, error) {
	// 1. 获取管理员信息
	admin, err := s.adminRepo.GetByID(adminID)
	if err != nil {
		return nil, fmt.Errorf("查询管理员失败: %w", err)
	}
	if admin == nil {
		return nil, fmt.Errorf("管理员不存在")
	}

	// 2. 验证原密码
	if !utils.CheckPassword(req.OldPassword, admin.Password) {
		return nil, fmt.Errorf("原密码错误")
	}

	// 3. 验证新密码强度
	if err := utils.ValidatePassword(req.NewPassword); err != nil {
		return nil, err
	}

	// 4. 加密新密码
	hashedPassword, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %w", err)
	}

	// 5. 更新密码
	if err := s.adminRepo.UpdatePassword(adminID, hashedPassword); err != nil {
		return nil, fmt.Errorf("更新密码失败: %w", err)
	}

	// 6. 重新获取管理员信息
	admin, err = s.adminRepo.GetByID(adminID)
	if err != nil {
		return nil, fmt.Errorf("查询管理员失败: %w", err)
	}

	return admin.ToResponse(), nil
}

// SendForgotPasswordCode 发送忘记密码验证码
func (s *AdminService) SendForgotPasswordCode(req *model.AdminForgotPasswordRequest) error {
	// 1. 验证手机号格式
	if !utils.ValidatePhone(req.Phone) {
		return fmt.Errorf("手机号格式不正确")
	}

	// 2. 验证管理员是否存在
	admin, err := s.adminRepo.GetByPhone(req.Phone)
	if err != nil {
		return fmt.Errorf("查询管理员失败: %w", err)
	}
	if admin == nil {
		return fmt.Errorf("手机号未注册")
	}

	// 3. 检查发送频率限制
	if err := s.checkCodeSendLimit(req.Phone); err != nil {
		return err
	}

	// 4. 生成验证码
	code := utils.GenerateVerificationCode()

	// 5. 存储验证码到Redis（5分钟过期）
	if s.redis != nil {
		ctx := context.Background()
		codeKey := s.getAdminCodeCacheKey(req.Phone)
		if err := s.redis.Set(ctx, codeKey, code, 5*time.Minute).Err(); err != nil {
			return fmt.Errorf("存储验证码失败: %w", err)
		}

		// 6. 设置发送频率限制（60秒内不能重复发送）
		limitKey := s.getAdminCodeLimitKey(req.Phone)
		if err := s.redis.Set(ctx, limitKey, "1", time.Minute).Err(); err != nil {
			return fmt.Errorf("设置发送限制失败: %w", err)
		}
	} else {
		fmt.Printf("⚠️  Redis不可用，管理员验证码: %s\n", code)
	}

	// 7. 发送短信
	if err := s.sendSMS(req.Phone, code); err != nil {
		return fmt.Errorf("发送短信失败: %w", err)
	}

	return nil
}

// ResetPassword 管理员重置密码（使用验证码）
func (s *AdminService) ResetPassword(req *model.AdminResetPasswordRequest) (*model.AdminResponse, error) {
	// 1. 验证手机号格式
	if !utils.ValidatePhone(req.Phone) {
		return nil, fmt.Errorf("手机号格式不正确")
	}

	// 2. 验证管理员是否存在
	admin, err := s.adminRepo.GetByPhone(req.Phone)
	if err != nil {
		return nil, fmt.Errorf("查询管理员失败: %w", err)
	}
	if admin == nil {
		return nil, fmt.Errorf("手机号未注册")
	}

	// 3. 验证验证码
	if err := s.verifyAdminCode(req.Phone, req.Code); err != nil {
		return nil, err
	}

	// 4. 验证新密码强度
	if err := utils.ValidatePassword(req.NewPassword); err != nil {
		return nil, err
	}

	// 5. 加密新密码
	hashedPassword, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %w", err)
	}

	// 6. 更新密码
	if err := s.adminRepo.UpdatePassword(admin.ID, hashedPassword); err != nil {
		return nil, fmt.Errorf("更新密码失败: %w", err)
	}

	// 7. 删除验证码缓存
	s.deleteAdminCodeCache(req.Phone)

	// 8. 重新获取管理员信息
	admin, err = s.adminRepo.GetByID(admin.ID)
	if err != nil {
		return nil, fmt.Errorf("查询管理员失败: %w", err)
	}

	return admin.ToResponse(), nil
}

// ResetUserPassword 管理员重置用户密码
func (s *AdminService) ResetUserPassword(adminID, userID uint, req *model.AdminResetUserPasswordRequest) (*model.UserResponse, error) {
	// 1. 验证管理员权限
	admin, err := s.adminRepo.GetByID(adminID)
	if err != nil {
		return nil, fmt.Errorf("查询管理员失败: %w", err)
	}
	if admin == nil {
		return nil, fmt.Errorf("管理员不存在")
	}

	// 2. 检查权限（只有超级管理员可以重置用户密码）
	if !admin.IsSuperAdmin() {
		return nil, fmt.Errorf("权限不足，只有超级管理员可以重置用户密码")
	}

	// 3. 验证用户是否存在
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 4. 验证新密码强度
	if err := utils.ValidatePassword(req.NewPassword); err != nil {
		return nil, err
	}

	// 5. 加密新密码
	hashedPassword, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		return nil, fmt.Errorf("密码加密失败: %w", err)
	}

	// 6. 更新用户密码
	if err := s.userRepo.UpdatePassword(userID, hashedPassword); err != nil {
		return nil, fmt.Errorf("更新用户密码失败: %w", err)
	}

	// 7. 重新获取用户信息
	user, err = s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %w", err)
	}

	return user.ToResponse(), nil
}

// 辅助方法

// checkCodeSendLimit 检查验证码发送频率限制
func (s *AdminService) checkCodeSendLimit(phone string) error {
	if s.redis == nil {
		return nil
	}

	ctx := context.Background()
	limitKey := s.getAdminCodeLimitKey(phone)

	exists, err := s.redis.Exists(ctx, limitKey).Result()
	if err != nil {
		return fmt.Errorf("检查发送限制失败: %w", err)
	}

	if exists > 0 {
		return fmt.Errorf("验证码发送过于频繁，请稍后再试")
	}

	return nil
}

// verifyAdminCode 验证管理员验证码
func (s *AdminService) verifyAdminCode(phone, code string) error {
	if s.redis == nil {
		// Redis不可用时，使用固定验证码进行测试
		if code == "123456" {
			return nil
		}
		return fmt.Errorf("验证码错误")
	}

	ctx := context.Background()
	codeKey := s.getAdminCodeCacheKey(phone)

	cachedCode, err := s.redis.Get(ctx, codeKey).Result()
	if err != nil {
		if err == redis.Nil {
			return fmt.Errorf("验证码已过期或不存在")
		}
		return fmt.Errorf("验证码验证失败: %w", err)
	}

	if cachedCode != code {
		return fmt.Errorf("验证码错误")
	}

	return nil
}

// deleteAdminCodeCache 删除管理员验证码缓存
func (s *AdminService) deleteAdminCodeCache(phone string) {
	if s.redis == nil {
		return
	}

	ctx := context.Background()
	codeKey := s.getAdminCodeCacheKey(phone)
	s.redis.Del(ctx, codeKey)
}

// getAdminCodeCacheKey 获取管理员验证码缓存键
func (s *AdminService) getAdminCodeCacheKey(phone string) string {
	return fmt.Sprintf("admin_code:%s", phone)
}

// getAdminCodeLimitKey 获取管理员验证码发送限制键
func (s *AdminService) getAdminCodeLimitKey(phone string) string {
	return fmt.Sprintf("admin_code_limit:%s", phone)
}

// sendSMS 发送短信
func (s *AdminService) sendSMS(phone, code string) error {
	return s.smsService.SendCode(phone, code)
}
