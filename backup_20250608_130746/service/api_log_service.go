package service

import (
	"encoding/json"
	"fmt"
	"os"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"time"
)

type APILogService struct {
	apiLogRepo *repository.APILogRepository
}

// NewAPILogService 创建API日志服务实例
func NewAPILogService(apiLogRepo *repository.APILogRepository) *APILogService {
	return &APILogService{
		apiLogRepo: apiLogRepo,
	}
}

// CreateAPILog 创建API日志
func (s *APILogService) CreateAPILog(log *model.APILog) error {
	return s.apiLogRepo.Create(log)
}

// GetAPILog 获取API日志详情
func (s *APILogService) GetAPILog(id uint) (*model.APILogResponse, error) {
	log, err := s.apiLogRepo.GetByID(id)
	if err != nil {
		return nil, fmt.Errorf("查询API日志失败: %w", err)
	}
	if log == nil {
		return nil, fmt.Errorf("API日志不存在")
	}

	return log.ToResponse(), nil
}

// GetAPILogs 获取API日志列表
func (s *APILogService) GetAPILogs(page, pageSize int) ([]*model.APILogResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	logs, total, err := s.apiLogRepo.List(offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询API日志列表失败: %w", err)
	}

	var result []*model.APILogResponse
	for _, log := range logs {
		result = append(result, log.ToResponse())
	}

	return result, total, nil
}

// GetUserAPILogs 获取用户API日志列表
func (s *APILogService) GetUserAPILogs(userID uint, page, pageSize int) ([]*model.APILogResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	logs, total, err := s.apiLogRepo.GetByUserID(userID, offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询用户API日志失败: %w", err)
	}

	var result []*model.APILogResponse
	for _, log := range logs {
		result = append(result, log.ToResponse())
	}

	return result, total, nil
}

// GetAppAPILogs 获取应用API日志列表
func (s *APILogService) GetAppAPILogs(appID uint, page, pageSize int) ([]*model.APILogResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	logs, total, err := s.apiLogRepo.GetByAppID(appID, offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询应用API日志失败: %w", err)
	}

	var result []*model.APILogResponse
	for _, log := range logs {
		result = append(result, log.ToResponse())
	}

	return result, total, nil
}

// GetAPILogsByDateRange 根据日期范围获取API日志
func (s *APILogService) GetAPILogsByDateRange(startDate, endDate time.Time, page, pageSize int) ([]*model.APILogResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	logs, total, err := s.apiLogRepo.GetByDateRange(startDate, endDate, offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询日期范围API日志失败: %w", err)
	}

	var result []*model.APILogResponse
	for _, log := range logs {
		result = append(result, log.ToResponse())
	}

	return result, total, nil
}

// GetUserAPIStats 获取用户API统计
func (s *APILogService) GetUserAPIStats(userID uint) (map[string]interface{}, error) {
	// 获取总调用次数
	totalCalls, err := s.apiLogRepo.GetTotalCallsByUser(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户总调用次数失败: %w", err)
	}

	// 获取成功调用次数
	successCalls, err := s.apiLogRepo.GetSuccessCallsByUser(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户成功调用次数失败: %w", err)
	}

	// 获取错误调用次数
	errorCalls, err := s.apiLogRepo.GetErrorCallsByUser(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户错误调用次数失败: %w", err)
	}

	// 获取总费用
	totalCost, err := s.apiLogRepo.GetTotalCostByUser(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户总费用失败: %w", err)
	}

	// 获取平均响应时间
	avgResponseTime, err := s.apiLogRepo.GetAvgResponseTimeByUser(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户平均响应时间失败: %w", err)
	}

	// 计算成功率
	successRate := float64(0)
	if totalCalls > 0 {
		successRate = float64(successCalls) / float64(totalCalls) * 100
	}

	return map[string]interface{}{
		"total_calls":       totalCalls,
		"success_calls":     successCalls,
		"error_calls":       errorCalls,
		"total_cost":        totalCost,
		"avg_response_time": avgResponseTime,
		"success_rate":      successRate,
	}, nil
}

// GetAppAPIStats 获取应用API统计
func (s *APILogService) GetAppAPIStats(appID uint) (map[string]interface{}, error) {
	// 获取总调用次数
	totalCalls, err := s.apiLogRepo.GetTotalCallsByApp(appID)
	if err != nil {
		return nil, fmt.Errorf("获取应用总调用次数失败: %w", err)
	}

	return map[string]interface{}{
		"total_calls": totalCalls,
	}, nil
}

// CleanOldLogs 清理旧日志
func (s *APILogService) CleanOldLogs(days int) error {
	if days <= 0 {
		days = 30 // 默认保留30天
	}

	return s.apiLogRepo.DeleteOldLogs(days)
}

// GetAPILogsWithFilters 根据过滤条件获取API日志
func (s *APILogService) GetAPILogsWithFilters(filters map[string]interface{}, page, pageSize int) ([]*model.APILogResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	logs, total, err := s.apiLogRepo.GetWithFilters(filters, offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询API日志失败: %w", err)
	}

	var result []*model.APILogResponse
	for _, log := range logs {
		result = append(result, log.ToResponse())
	}

	return result, total, nil
}

// GetAPILogStats 获取API日志统计
func (s *APILogService) GetAPILogStats(filters map[string]interface{}) (map[string]interface{}, error) {
	stats, err := s.apiLogRepo.GetStatsWithFilters(filters)
	if err != nil {
		return nil, fmt.Errorf("获取API日志统计失败: %w", err)
	}

	return stats, nil
}

// ExportAPILogs 导出API日志
func (s *APILogService) ExportAPILogs(filters map[string]interface{}, format string) (string, error) {
	// 获取所有符合条件的日志
	logs, _, err := s.apiLogRepo.GetWithFilters(filters, 0, 0) // 0表示不限制数量
	if err != nil {
		return "", fmt.Errorf("查询API日志失败: %w", err)
	}

	// 生成导出文件
	filename := fmt.Sprintf("api_logs_%s.%s", time.Now().Format("20060102_150405"), format)
	filepath := fmt.Sprintf("exports/%s", filename)

	// 确保导出目录存在
	if err := os.MkdirAll("exports", 0755); err != nil {
		return "", fmt.Errorf("创建导出目录失败: %w", err)
	}

	switch format {
	case "csv":
		err = s.exportToCSV(logs, filepath)
	case "json":
		err = s.exportToJSON(logs, filepath)
	default:
		return "", fmt.Errorf("不支持的导出格式: %s", format)
	}

	if err != nil {
		return "", fmt.Errorf("导出文件失败: %w", err)
	}

	return filepath, nil
}

// exportToCSV 导出为CSV格式
func (s *APILogService) exportToCSV(logs []*model.APILog, filepath string) error {
	file, err := os.Create(filepath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 写入CSV头部
	header := "ID,用户ID,应用ID,请求方法,请求路径,状态码,响应时间(ms),请求大小(bytes),响应大小(bytes),费用(元),客户端IP,创建时间\n"
	if _, err := file.WriteString(header); err != nil {
		return err
	}

	// 写入数据行
	for _, log := range logs {
		line := fmt.Sprintf("%d,%d,%d,%s,%s,%d,%d,%d,%d,%.4f,%s,%s\n",
			log.ID,
			log.UserID,
			log.AppID,
			log.Method,
			log.Path,
			log.StatusCode,
			log.ResponseTime,
			log.RequestSize,
			log.ResponseSize,
			log.Cost,
			log.ClientIP,
			log.CreatedAt.Format("2006-01-02 15:04:05"),
		)
		if _, err := file.WriteString(line); err != nil {
			return err
		}
	}

	return nil
}

// exportToJSON 导出为JSON格式
func (s *APILogService) exportToJSON(logs []*model.APILog, filepath string) error {
	file, err := os.Create(filepath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 转换为响应格式
	var responses []*model.APILogResponse
	for _, log := range logs {
		responses = append(responses, log.ToResponse())
	}

	// 编码为JSON
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	return encoder.Encode(map[string]interface{}{
		"logs":        responses,
		"total":       len(responses),
		"export_time": time.Now().Format("2006-01-02 15:04:05"),
	})
}
