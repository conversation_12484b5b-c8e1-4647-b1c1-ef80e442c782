package service

import (
	"fmt"
	"solve_api/internal/model"
	"solve_api/internal/repository"
)

type ModelConfigService struct {
	modelConfigRepo *repository.ModelConfigRepository
}

// NewModelConfigService 创建模型配置服务实例
func NewModelConfigService(modelConfigRepo *repository.ModelConfigRepository) *ModelConfigService {
	return &ModelConfigService{
		modelConfigRepo: modelConfigRepo,
	}
}

// Create 创建模型配置
func (s *ModelConfigService) Create(req *model.ModelConfigCreateRequest) (*model.ModelConfigResponse, error) {
	// 1. 检查模型名称是否已存在
	exists, err := s.modelConfigRepo.ExistsByName(req.Name)
	if err != nil {
		return nil, fmt.Errorf("检查模型名称失败: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("模型名称已存在")
	}

	// 2. 创建模型配置
	modelConfig := &model.ModelConfig{
		Name:   req.Name,
		ApiURL: req.ApiURL,
		ApiKey: req.ApiKey,
		Status: model.ModelConfigStatusEnabled, // 默认启用
	}

	// 设置参数
	if req.Params != nil {
		if err := modelConfig.SetParamsMap(req.Params); err != nil {
			return nil, fmt.Errorf("设置模型参数失败: %w", err)
		}
	} else {
		// 使用默认参数
		defaultParams := model.GetDefaultModelParams(req.Name)
		if err := modelConfig.SetParamsMap(defaultParams); err != nil {
			return nil, fmt.Errorf("设置默认参数失败: %w", err)
		}
	}

	// 设置状态
	if req.Status > 0 {
		modelConfig.Status = req.Status
	}

	if err := s.modelConfigRepo.Create(modelConfig); err != nil {
		return nil, fmt.Errorf("创建模型配置失败: %w", err)
	}

	return modelConfig.ToResponse(true), nil
}

// 注意：GetByID方法已移除，请使用GetByName方法
// 基于模型名称的唯一性，使用name作为主键更加合理

// GetByName 根据名称获取模型配置
func (s *ModelConfigService) GetByName(name string) (*model.ModelConfigResponse, error) {
	modelConfig, err := s.modelConfigRepo.GetByName(name)
	if err != nil {
		return nil, fmt.Errorf("查询模型配置失败: %w", err)
	}
	if modelConfig == nil {
		return nil, fmt.Errorf("模型配置不存在")
	}

	return modelConfig.ToResponse(true), nil
}

// UpdateByName 根据名称更新模型配置
func (s *ModelConfigService) UpdateByName(name string, req *model.ModelConfigUpdateRequest) (*model.ModelConfigResponse, error) {
	// 1. 获取现有配置
	modelConfig, err := s.modelConfigRepo.GetByName(name)
	if err != nil {
		return nil, fmt.Errorf("查询模型配置失败: %w", err)
	}
	if modelConfig == nil {
		return nil, fmt.Errorf("模型配置不存在")
	}

	// 2. 模型名称不允许修改（因为是主键）
	if req.Name != "" && req.Name != modelConfig.Name {
		return nil, fmt.Errorf("模型名称不允许修改")
	}

	// 3. 更新字段
	if req.ApiURL != "" {
		modelConfig.ApiURL = req.ApiURL
	}
	if req.ApiKey != "" {
		modelConfig.ApiKey = req.ApiKey
	}
	if req.Params != nil {
		if err := modelConfig.SetParamsMap(req.Params); err != nil {
			return nil, fmt.Errorf("设置模型参数失败: %w", err)
		}
	}
	if req.Status > 0 {
		modelConfig.Status = req.Status
	}

	// 4. 保存更新
	if err := s.modelConfigRepo.Update(modelConfig); err != nil {
		return nil, fmt.Errorf("更新模型配置失败: %w", err)
	}

	return modelConfig.ToResponse(true), nil
}

// 注意：删除模型配置功能已移除
// 模型配置不支持删除操作，以保持系统稳定性和兼容性

// GetList 获取模型配置列表
func (s *ModelConfigService) GetList(page, pageSize int) ([]*model.ModelConfigListResponse, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	modelConfigs, total, err := s.modelConfigRepo.List(offset, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("查询模型配置列表失败: %w", err)
	}

	var result []*model.ModelConfigListResponse
	for _, modelConfig := range modelConfigs {
		result = append(result, modelConfig.ToListResponse())
	}

	return result, total, nil
}

// GetEnabled 获取所有启用的模型配置
func (s *ModelConfigService) GetEnabled() ([]*model.ModelConfigListResponse, error) {
	modelConfigs, err := s.modelConfigRepo.GetEnabled()
	if err != nil {
		return nil, fmt.Errorf("查询启用的模型配置失败: %w", err)
	}

	var result []*model.ModelConfigListResponse
	for _, modelConfig := range modelConfigs {
		result = append(result, modelConfig.ToListResponse())
	}

	return result, nil
}

// UpdateStatusByName 根据名称更新模型状态
func (s *ModelConfigService) UpdateStatusByName(name string, req *model.ModelConfigStatusUpdateRequest) (*model.ModelConfigResponse, error) {
	// 1. 检查模型是否存在
	modelConfig, err := s.modelConfigRepo.GetByName(name)
	if err != nil {
		return nil, fmt.Errorf("查询模型配置失败: %w", err)
	}
	if modelConfig == nil {
		return nil, fmt.Errorf("模型配置不存在")
	}

	// 2. 更新状态
	if err := s.modelConfigRepo.UpdateStatusByName(name, req.Status); err != nil {
		return nil, fmt.Errorf("更新模型状态失败: %w", err)
	}

	// 3. 重新获取更新后的配置
	modelConfig, err = s.modelConfigRepo.GetByName(name)
	if err != nil {
		return nil, fmt.Errorf("查询更新后的模型配置失败: %w", err)
	}

	return modelConfig.ToResponse(true), nil
}

// UpdateParamsByName 根据名称更新模型参数
func (s *ModelConfigService) UpdateParamsByName(name string, req *model.ModelConfigParamsUpdateRequest) (*model.ModelConfigResponse, error) {
	// 1. 获取现有配置
	modelConfig, err := s.modelConfigRepo.GetByName(name)
	if err != nil {
		return nil, fmt.Errorf("查询模型配置失败: %w", err)
	}
	if modelConfig == nil {
		return nil, fmt.Errorf("模型配置不存在")
	}

	// 2. 更新参数
	if err := modelConfig.SetParamsMap(req.Params); err != nil {
		return nil, fmt.Errorf("设置模型参数失败: %w", err)
	}

	// 3. 保存更新
	if err := s.modelConfigRepo.Update(modelConfig); err != nil {
		return nil, fmt.Errorf("更新模型配置失败: %w", err)
	}

	return modelConfig.ToResponse(true), nil
}

// UpdateApiKeyByName 根据名称更新模型API密钥
func (s *ModelConfigService) UpdateApiKeyByName(name string, req *model.ModelConfigApiKeyUpdateRequest) (*model.ModelConfigResponse, error) {
	// 1. 获取现有配置
	modelConfig, err := s.modelConfigRepo.GetByName(name)
	if err != nil {
		return nil, fmt.Errorf("查询模型配置失败: %w", err)
	}
	if modelConfig == nil {
		return nil, fmt.Errorf("模型配置不存在")
	}

	// 2. 更新API密钥
	modelConfig.ApiKey = req.ApiKey

	// 3. 更新API Secret（如果提供）
	if req.ApiSecret != "" {
		modelConfig.ApiSecret = req.ApiSecret
	}

	// 4. 保存更新
	if err := s.modelConfigRepo.Update(modelConfig); err != nil {
		return nil, fmt.Errorf("更新模型配置失败: %w", err)
	}

	return modelConfig.ToResponse(true), nil
}

// GetFixedModels 获取固定的两个模型配置
func (s *ModelConfigService) GetFixedModels() (*model.FixedModelsResponse, error) {
	// 获取Qwen模型
	qwenModel, err := s.modelConfigRepo.GetByName(model.ModelNameQwenVLPlus)
	if err != nil {
		return nil, fmt.Errorf("查询Qwen模型失败: %w", err)
	}

	// 获取DeepSeek模型
	deepseekModel, err := s.modelConfigRepo.GetByName(model.ModelNameDeepseekChat)
	if err != nil {
		return nil, fmt.Errorf("查询DeepSeek模型失败: %w", err)
	}

	response := &model.FixedModelsResponse{}

	if qwenModel != nil {
		response.Qwen = qwenModel.ToResponse(true)
	}

	if deepseekModel != nil {
		response.DeepSeek = deepseekModel.ToResponse(true)
	}

	return response, nil
}

// GetEnabledByName 根据名称获取启用的模型配置
func (s *ModelConfigService) GetEnabledByName(name string) (*model.ModelConfig, error) {
	modelConfig, err := s.modelConfigRepo.GetEnabledByName(name)
	if err != nil {
		return nil, fmt.Errorf("查询启用的模型配置失败: %w", err)
	}
	if modelConfig == nil {
		return nil, fmt.Errorf("模型配置不存在或未启用")
	}

	return modelConfig, nil
}
