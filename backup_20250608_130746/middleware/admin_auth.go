package middleware

import (
	"solve_api/internal/database"
	"solve_api/internal/model"
	"solve_api/internal/repository"
	"solve_api/internal/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

// AdminAuth 管理员认证中间件
func AdminAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 这里可以添加JWT token验证逻辑
		// 目前简化处理，通过路径参数获取管理员ID
		adminIDStr := c.Param("admin_id")
		if adminIDStr == "" {
			utils.Unauthorized(c, "缺少管理员ID")
			c.Abort()
			return
		}

		adminID, err := strconv.ParseUint(adminIDStr, 10, 32)
		if err != nil {
			utils.BadRequest(c, "管理员ID格式错误")
			c.Abort()
			return
		}

		// 验证管理员是否存在
		adminRepo := repository.NewAdminRepository(database.GetDB())
		admin, err := adminRepo.GetByID(uint(adminID))
		if err != nil {
			utils.ServerError(c, "查询管理员信息失败")
			c.Abort()
			return
		}

		if admin == nil {
			utils.Unauthorized(c, "管理员不存在")
			c.Abort()
			return
		}

		// 将管理员信息存储到上下文中
		c.Set("admin", admin)
		c.Set("admin_id", admin.ID)
		c.Set("admin_role", admin.Role)

		c.Next()
	}
}

// AdminPermission 管理员权限检查中间件
func AdminPermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从上下文获取管理员信息
		admin, exists := GetAdminFromContext(c)
		if !exists {
			utils.Unauthorized(c, "未找到管理员信息")
			c.Abort()
			return
		}

		// 检查权限
		if !admin.HasPermission(permission) {
			utils.Forbidden(c, "权限不足")
			c.Abort()
			return
		}

		c.Next()
	}
}

// SuperAdminOnly 仅超级管理员可访问
func SuperAdminOnly() gin.HandlerFunc {
	return func(c *gin.Context) {
		admin, exists := GetAdminFromContext(c)
		if !exists {
			utils.Unauthorized(c, "未找到管理员信息")
			c.Abort()
			return
		}

		if !admin.IsSuperAdmin() {
			utils.Forbidden(c, "仅超级管理员可访问")
			c.Abort()
			return
		}

		c.Next()
	}
}

// GetAdminFromContext 从上下文获取管理员信息
func GetAdminFromContext(c *gin.Context) (*model.Admin, bool) {
	admin, exists := c.Get("admin")
	if !exists {
		return nil, false
	}
	return admin.(*model.Admin), true
}

// GetAdminIDFromContext 从上下文获取管理员ID
func GetAdminIDFromContext(c *gin.Context) (uint, bool) {
	adminID, exists := c.Get("admin_id")
	if !exists {
		return 0, false
	}
	return adminID.(uint), true
}

// GetAdminRoleFromContext 从上下文获取管理员角色
func GetAdminRoleFromContext(c *gin.Context) (int, bool) {
	adminRole, exists := c.Get("admin_role")
	if !exists {
		return 0, false
	}
	return adminRole.(int), true
}
