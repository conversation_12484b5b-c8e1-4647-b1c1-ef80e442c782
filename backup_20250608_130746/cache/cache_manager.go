package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// CacheManager 统一缓存管理器
type CacheManager struct {
	redis *redis.Client
	mysql *gorm.DB
	ctx   context.Context
}

// NewCacheManager 创建缓存管理器实例
func NewCacheManager(redis *redis.Client, mysql *gorm.DB) *CacheManager {
	return &CacheManager{
		redis: redis,
		mysql: mysql,
		ctx:   context.Background(),
	}
}

// CacheOptions 缓存选项
type CacheOptions struct {
	TTL            time.Duration // 缓存过期时间
	EnableFallback bool          // 是否启用MySQL降级
	Prefix         string        // 缓存键前缀
}

// DefaultCacheOptions 默认缓存选项
func DefaultCacheOptions() *CacheOptions {
	return &CacheOptions{
		TTL:            7 * 24 * time.Hour, // 默认7天
		EnableFallback: true,
		Prefix:         "cache:",
	}
}

// Set 设置缓存（支持自动序列化）
func (c *CacheManager) Set(key string, value interface{}, options ...*CacheOptions) error {
	opts := DefaultCacheOptions()
	if len(options) > 0 && options[0] != nil {
		opts = options[0]
	}

	// 生成完整的缓存键
	fullKey := c.generateKey(key, opts.Prefix)

	// 序列化数据
	data, err := c.serialize(value)
	if err != nil {
		return fmt.Errorf("序列化失败: %w", err)
	}

	// 优先写入Redis
	if c.redis != nil {
		if err := c.redis.Set(c.ctx, fullKey, data, opts.TTL).Err(); err != nil {
			// Redis写入失败，如果启用降级则写入MySQL
			if opts.EnableFallback && c.mysql != nil {
				return c.setToMySQL(fullKey, data, opts.TTL)
			}
			return fmt.Errorf("Redis写入失败: %w", err)
		}
		return nil
	}

	// Redis不可用，降级到MySQL
	if opts.EnableFallback && c.mysql != nil {
		return c.setToMySQL(fullKey, data, opts.TTL)
	}

	return fmt.Errorf("缓存服务不可用")
}

// Get 获取缓存（支持自动反序列化）
func (c *CacheManager) Get(key string, dest interface{}, options ...*CacheOptions) error {
	opts := DefaultCacheOptions()
	if len(options) > 0 && options[0] != nil {
		opts = options[0]
	}

	// 生成完整的缓存键
	fullKey := c.generateKey(key, opts.Prefix)

	// 优先从Redis获取
	if c.redis != nil {
		data, err := c.redis.Get(c.ctx, fullKey).Result()
		if err == nil {
			return c.deserialize(data, dest)
		}

		// Redis未命中或出错，如果启用降级则从MySQL获取
		if err != redis.Nil && opts.EnableFallback && c.mysql != nil {
			return c.getFromMySQL(fullKey, dest)
		}

		if err == redis.Nil {
			return ErrCacheNotFound
		}
		return fmt.Errorf("Redis读取失败: %w", err)
	}

	// Redis不可用，降级到MySQL
	if opts.EnableFallback && c.mysql != nil {
		return c.getFromMySQL(fullKey, dest)
	}

	return fmt.Errorf("缓存服务不可用")
}

// Delete 删除缓存
func (c *CacheManager) Delete(key string, options ...*CacheOptions) error {
	opts := DefaultCacheOptions()
	if len(options) > 0 && options[0] != nil {
		opts = options[0]
	}

	fullKey := c.generateKey(key, opts.Prefix)

	var redisErr, mysqlErr error

	// 从Redis删除
	if c.redis != nil {
		redisErr = c.redis.Del(c.ctx, fullKey).Err()
	}

	// 从MySQL删除（如果启用降级）
	if opts.EnableFallback && c.mysql != nil {
		mysqlErr = c.deleteFromMySQL(fullKey)
	}

	// 如果都失败了，返回错误
	if redisErr != nil && mysqlErr != nil {
		return fmt.Errorf("删除缓存失败 - Redis: %v, MySQL: %v", redisErr, mysqlErr)
	}

	return nil
}

// Exists 检查缓存是否存在
func (c *CacheManager) Exists(key string, options ...*CacheOptions) (bool, error) {
	opts := DefaultCacheOptions()
	if len(options) > 0 && options[0] != nil {
		opts = options[0]
	}

	fullKey := c.generateKey(key, opts.Prefix)

	// 优先检查Redis
	if c.redis != nil {
		count, err := c.redis.Exists(c.ctx, fullKey).Result()
		if err == nil {
			return count > 0, nil
		}

		// Redis出错，如果启用降级则检查MySQL
		if opts.EnableFallback && c.mysql != nil {
			return c.existsInMySQL(fullKey)
		}
		return false, fmt.Errorf("Redis检查失败: %w", err)
	}

	// Redis不可用，降级到MySQL
	if opts.EnableFallback && c.mysql != nil {
		return c.existsInMySQL(fullKey)
	}

	return false, fmt.Errorf("缓存服务不可用")
}

// BatchSet 批量设置缓存
func (c *CacheManager) BatchSet(data map[string]interface{}, options ...*CacheOptions) error {
	opts := DefaultCacheOptions()
	if len(options) > 0 && options[0] != nil {
		opts = options[0]
	}

	if c.redis != nil {
		pipe := c.redis.Pipeline()

		for key, value := range data {
			fullKey := c.generateKey(key, opts.Prefix)
			serializedData, err := c.serialize(value)
			if err != nil {
				continue // 跳过序列化失败的数据
			}
			pipe.Set(c.ctx, fullKey, serializedData, opts.TTL)
		}

		_, err := pipe.Exec(c.ctx)
		if err != nil && opts.EnableFallback && c.mysql != nil {
			// Redis批量操作失败，降级到MySQL
			return c.batchSetToMySQL(data, opts)
		}
		return err
	}

	// Redis不可用，降级到MySQL
	if opts.EnableFallback && c.mysql != nil {
		return c.batchSetToMySQL(data, opts)
	}

	return fmt.Errorf("缓存服务不可用")
}

// BatchGet 批量获取缓存
func (c *CacheManager) BatchGet(keys []string, options ...*CacheOptions) (map[string]interface{}, error) {
	opts := DefaultCacheOptions()
	if len(options) > 0 && options[0] != nil {
		opts = options[0]
	}

	result := make(map[string]interface{})

	if c.redis != nil {
		// 生成完整的缓存键
		fullKeys := make([]string, len(keys))
		for i, key := range keys {
			fullKeys[i] = c.generateKey(key, opts.Prefix)
		}

		// 批量获取
		values, err := c.redis.MGet(c.ctx, fullKeys...).Result()
		if err != nil && opts.EnableFallback && c.mysql != nil {
			return c.batchGetFromMySQL(keys, opts)
		}

		// 处理结果
		for i, value := range values {
			if value != nil {
				var data interface{}
				if err := c.deserialize(value.(string), &data); err == nil {
					result[keys[i]] = data
				}
			}
		}

		return result, err
	}

	// Redis不可用，降级到MySQL
	if opts.EnableFallback && c.mysql != nil {
		return c.batchGetFromMySQL(keys, opts)
	}

	return nil, fmt.Errorf("缓存服务不可用")
}

// Clear 清空指定前缀的所有缓存
func (c *CacheManager) Clear(prefix string) error {
	if c.redis != nil {
		pattern := c.generateKey("*", prefix)
		keys, err := c.redis.Keys(c.ctx, pattern).Result()
		if err != nil {
			return fmt.Errorf("获取缓存键失败: %w", err)
		}

		if len(keys) > 0 {
			return c.redis.Del(c.ctx, keys...).Err()
		}
	}

	// 同时清理MySQL缓存表（如果存在）
	if c.mysql != nil {
		return c.clearMySQLCache(prefix)
	}

	return nil
}

// GetStats 获取缓存统计信息
func (c *CacheManager) GetStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	if c.redis != nil {
		info, err := c.redis.Info(c.ctx, "memory").Result()
		if err == nil {
			stats["redis_info"] = info
		}

		dbSize, err := c.redis.DBSize(c.ctx).Result()
		if err == nil {
			stats["redis_keys"] = dbSize
		}

		stats["redis_available"] = true
	} else {
		stats["redis_available"] = false
	}

	if c.mysql != nil {
		stats["mysql_available"] = true
		// 可以添加MySQL缓存表的统计信息
	} else {
		stats["mysql_available"] = false
	}

	return stats, nil
}

// 内部方法

// generateKey 生成完整的缓存键
func (c *CacheManager) generateKey(key, prefix string) string {
	if prefix == "" {
		return key
	}
	return prefix + key
}

// serialize 序列化数据
func (c *CacheManager) serialize(value interface{}) (string, error) {
	if str, ok := value.(string); ok {
		return str, nil
	}

	data, err := json.Marshal(value)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// deserialize 反序列化数据
func (c *CacheManager) deserialize(data string, dest interface{}) error {
	if strPtr, ok := dest.(*string); ok {
		*strPtr = data
		return nil
	}

	return json.Unmarshal([]byte(data), dest)
}

// MySQL降级相关方法（简化实现）
func (c *CacheManager) setToMySQL(key, data string, ttl time.Duration) error {
	// 这里可以实现MySQL缓存表的写入逻辑
	// 为了简化，暂时返回nil
	return nil
}

func (c *CacheManager) getFromMySQL(key string, dest interface{}) error {
	// 这里可以实现MySQL缓存表的读取逻辑
	return ErrCacheNotFound
}

func (c *CacheManager) deleteFromMySQL(key string) error {
	// 这里可以实现MySQL缓存表的删除逻辑
	return nil
}

func (c *CacheManager) existsInMySQL(key string) (bool, error) {
	// 这里可以实现MySQL缓存表的存在性检查
	return false, nil
}

func (c *CacheManager) batchSetToMySQL(data map[string]interface{}, opts *CacheOptions) error {
	// 这里可以实现MySQL缓存表的批量写入逻辑
	return nil
}

func (c *CacheManager) batchGetFromMySQL(keys []string, opts *CacheOptions) (map[string]interface{}, error) {
	// 这里可以实现MySQL缓存表的批量读取逻辑
	return make(map[string]interface{}), nil
}

func (c *CacheManager) clearMySQLCache(prefix string) error {
	// 这里可以实现MySQL缓存表的清理逻辑
	return nil
}

// 错误定义
var (
	ErrCacheNotFound = fmt.Errorf("缓存未找到")
)
