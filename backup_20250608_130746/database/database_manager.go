package database

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"solve_api/internal/cache"
	"solve_api/internal/config"

	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// DatabaseManager 统一数据库管理器
type DatabaseManager struct {
	mysql        *gorm.DB
	redis        *redis.Client
	cacheManager *cache.CacheManager
	configCache  *cache.ConfigCache

	// 连接状态
	mysqlConnected bool
	redisConnected bool

	// 统计信息
	stats *DatabaseStats
	mutex sync.RWMutex
}

// DatabaseStats 数据库统计信息
type DatabaseStats struct {
	MySQLQueries    int64
	RedisOperations int64
	CacheHits       int64
	CacheMisses     int64
	Errors          int64
	LastUpdate      time.Time
}

var (
	dbManager *DatabaseManager
	once      sync.Once
)

// GetDatabaseManager 获取数据库管理器单例
func GetDatabaseManager() *DatabaseManager {
	once.Do(func() {
		dbManager = &DatabaseManager{
			stats: &DatabaseStats{
				LastUpdate: time.Now(),
			},
		}
	})
	return dbManager
}

// Initialize 初始化数据库管理器
func (dm *DatabaseManager) Initialize(cfg *config.Config) error {
	var mysqlErr, redisErr error

	// 初始化MySQL
	if mysqlErr = dm.initMySQL(&cfg.Database.MySQL); mysqlErr != nil {
		log.Printf("MySQL初始化失败: %v", mysqlErr)
		dm.mysqlConnected = false
	} else {
		dm.mysqlConnected = true
		log.Println("MySQL连接成功")
	}

	// 初始化Redis
	if redisErr = dm.initRedis(&cfg.Redis); redisErr != nil {
		log.Printf("Redis初始化失败: %v", redisErr)
		dm.redisConnected = false
	} else {
		dm.redisConnected = true
		log.Println("Redis连接成功")
	}

	// 初始化缓存管理器
	dm.cacheManager = cache.NewCacheManager(dm.redis, dm.mysql)

	// 初始化配置缓存
	if dm.mysql != nil {
		dm.configCache = cache.NewConfigCache(dm.mysql)
	}

	// 启动健康检查
	go dm.startHealthCheck()

	// 如果MySQL和Redis都失败，返回错误
	if mysqlErr != nil && redisErr != nil {
		return fmt.Errorf("数据库初始化失败 - MySQL: %v, Redis: %v", mysqlErr, redisErr)
	}

	return nil
}

// GetMySQL 获取MySQL连接
func (dm *DatabaseManager) GetMySQL() *gorm.DB {
	dm.incrementStat("mysql_queries")
	return dm.mysql
}

// GetRedis 获取Redis连接
func (dm *DatabaseManager) GetRedis() *redis.Client {
	dm.incrementStat("redis_operations")
	return dm.redis
}

// GetCacheManager 获取缓存管理器
func (dm *DatabaseManager) GetCacheManager() *cache.CacheManager {
	return dm.cacheManager
}

// GetConfigCache 获取配置缓存
func (dm *DatabaseManager) GetConfigCache() *cache.ConfigCache {
	return dm.configCache
}

// IsHealthy 检查数据库健康状态
func (dm *DatabaseManager) IsHealthy() map[string]bool {
	return map[string]bool{
		"mysql": dm.mysqlConnected,
		"redis": dm.redisConnected,
	}
}

// GetStats 获取统计信息
func (dm *DatabaseManager) GetStats() *DatabaseStats {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()

	// 创建副本避免并发问题
	return &DatabaseStats{
		MySQLQueries:    dm.stats.MySQLQueries,
		RedisOperations: dm.stats.RedisOperations,
		CacheHits:       dm.stats.CacheHits,
		CacheMisses:     dm.stats.CacheMisses,
		Errors:          dm.stats.Errors,
		LastUpdate:      dm.stats.LastUpdate,
	}
}

// ResetStats 重置统计信息
func (dm *DatabaseManager) ResetStats() {
	dm.mutex.Lock()
	defer dm.mutex.Unlock()

	dm.stats = &DatabaseStats{
		LastUpdate: time.Now(),
	}
}

// Close 关闭所有数据库连接
func (dm *DatabaseManager) Close() error {
	var mysqlErr, redisErr error

	// 关闭MySQL连接
	if dm.mysql != nil {
		if sqlDB, err := dm.mysql.DB(); err == nil {
			mysqlErr = sqlDB.Close()
		}
	}

	// 关闭Redis连接
	if dm.redis != nil {
		redisErr = dm.redis.Close()
	}

	// 返回第一个错误
	if mysqlErr != nil {
		return mysqlErr
	}
	return redisErr
}

// Transaction 执行数据库事务
func (dm *DatabaseManager) Transaction(fn func(*gorm.DB) error) error {
	if dm.mysql == nil {
		return fmt.Errorf("MySQL连接不可用")
	}

	dm.incrementStat("mysql_queries")
	return dm.mysql.Transaction(fn)
}

// WithCache 使用缓存执行操作
func (dm *DatabaseManager) WithCache(key string, dest interface{}, fn func() error, ttl ...time.Duration) error {
	if dm.cacheManager == nil {
		return fn() // 缓存不可用，直接执行函数
	}

	// 尝试从缓存获取
	err := dm.cacheManager.Get(key, dest)
	if err == nil {
		dm.incrementStat("cache_hits")
		return nil
	}

	if err != cache.ErrCacheNotFound {
		dm.incrementStat("errors")
		log.Printf("缓存读取错误: %v", err)
	}

	// 缓存未命中，执行函数
	dm.incrementStat("cache_misses")
	if err := fn(); err != nil {
		return err
	}

	// 将结果存入缓存
	cacheTTL := 5 * time.Minute // 默认5分钟
	if len(ttl) > 0 {
		cacheTTL = ttl[0]
	}

	options := &cache.CacheOptions{TTL: cacheTTL}
	if err := dm.cacheManager.Set(key, dest, options); err != nil {
		log.Printf("缓存写入错误: %v", err)
		dm.incrementStat("errors")
	}

	return nil
}

// 内部方法

// initMySQL 初始化MySQL连接
func (dm *DatabaseManager) initMySQL(cfg *config.MySQLConfig) error {
	if DB != nil {
		dm.mysql = DB
		return nil
	}

	return InitMySQL(cfg)
}

// initRedis 初始化Redis连接
func (dm *DatabaseManager) initRedis(cfg *config.RedisConfig) error {
	if RDB != nil {
		dm.redis = RDB
		return nil
	}

	return InitRedis(cfg)
}

// startHealthCheck 启动健康检查
func (dm *DatabaseManager) startHealthCheck() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for range ticker.C {
		dm.checkHealth()
	}
}

// checkHealth 检查数据库健康状态
func (dm *DatabaseManager) checkHealth() {
	// 检查MySQL
	if dm.mysql != nil {
		if sqlDB, err := dm.mysql.DB(); err == nil {
			if err := sqlDB.Ping(); err != nil {
				dm.mysqlConnected = false
				log.Printf("MySQL健康检查失败: %v", err)
			} else {
				dm.mysqlConnected = true
			}
		}
	}

	// 检查Redis
	if dm.redis != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if _, err := dm.redis.Ping(ctx).Result(); err != nil {
			dm.redisConnected = false
			log.Printf("Redis健康检查失败: %v", err)
		} else {
			dm.redisConnected = true
		}
	}

	// 更新统计信息
	dm.mutex.Lock()
	dm.stats.LastUpdate = time.Now()
	dm.mutex.Unlock()
}

// incrementStat 增加统计计数
func (dm *DatabaseManager) incrementStat(statType string) {
	dm.mutex.Lock()
	defer dm.mutex.Unlock()

	switch statType {
	case "mysql_queries":
		dm.stats.MySQLQueries++
	case "redis_operations":
		dm.stats.RedisOperations++
	case "cache_hits":
		dm.stats.CacheHits++
	case "cache_misses":
		dm.stats.CacheMisses++
	case "errors":
		dm.stats.Errors++
	}
}

// 全局函数，保持向后兼容

// GetDB 获取MySQL连接（向后兼容）
func GetDB() *gorm.DB {
	if dbManager != nil {
		return dbManager.GetMySQL()
	}
	return DB
}

// GetRedis 获取Redis连接（向后兼容）
func GetRedis() *redis.Client {
	if dbManager != nil {
		return dbManager.GetRedis()
	}
	return RDB
}

// GetCacheManager 获取缓存管理器
func GetCacheManager() *cache.CacheManager {
	if dbManager != nil {
		return dbManager.GetCacheManager()
	}
	return nil
}

// GetConfigCache 获取配置缓存
func GetConfigCache() *cache.ConfigCache {
	if dbManager != nil {
		return dbManager.GetConfigCache()
	}
	return nil
}

// GetDatabaseStats 获取数据库统计信息
func GetDatabaseStats() *DatabaseStats {
	if dbManager != nil {
		return dbManager.GetStats()
	}
	return &DatabaseStats{}
}
