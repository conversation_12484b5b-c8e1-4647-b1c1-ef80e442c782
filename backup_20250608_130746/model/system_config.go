package model

import (
	"time"
)

// SystemConfig 系统配置表
type SystemConfig struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	Key         string    `gorm:"uniqueIndex;size:50;not null" json:"key"`
	Value       string    `gorm:"type:text" json:"value"`
	Description string    `gorm:"size:255" json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (SystemConfig) TableName() string {
	return "system_configs"
}

// 系统配置键常量
const (
	ConfigKeyInviteCode = "invite_code" // 邀请码
	ConfigKeyRateLimit  = "rate_limit"  // 限流配置
	ConfigKeyCacheTTL   = "cache_ttl"   // 缓存TTL
)

// SystemConfigRequest 系统配置请求
type SystemConfigRequest struct {
	Key         string `json:"key" binding:"required" example:"invite_code"`
	Value       string `json:"value" binding:"required" example:"SOLVE2024"`
	Description string `json:"description" example:"系统邀请码"`
}

// SystemConfigResponse 系统配置响应
type SystemConfigResponse struct {
	ID          uint      `json:"id"`
	Key         string    `json:"key"`
	Value       string    `json:"value"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (sc *SystemConfig) ToResponse() *SystemConfigResponse {
	return &SystemConfigResponse{
		ID:          sc.ID,
		Key:         sc.Key,
		Value:       sc.Value,
		Description: sc.Description,
		CreatedAt:   sc.CreatedAt,
		UpdatedAt:   sc.UpdatedAt,
	}
}
