package model

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// Application 应用表
type Application struct {
	ID              uint           `gorm:"primaryKey" json:"id"`
	UserID          uint           `gorm:"index;not null" json:"user_id"`
	Name            string         `gorm:"size:50;not null" json:"name"`
	Type            int            `gorm:"not null;comment:'业务类型 1:拍照搜题'" json:"type"`
	AppKey          string         `gorm:"uniqueIndex;size:32;not null" json:"app_key"`
	SecretKey       string         `gorm:"size:64;not null" json:"secret_key"`
	Status          int            `gorm:"default:1;comment:'1:正常 2:冻结'" json:"status"`
	RateLimitQPS    int            `gorm:"default:10;comment:'QPS限制(每秒请求数)'" json:"rate_limit_qps"`
	RateLimitBurst  int            `gorm:"default:20;comment:'突发请求数限制'" json:"rate_limit_burst"`
	RateLimitWindow int            `gorm:"default:60;comment:'限流窗口时间(秒)'" json:"rate_limit_window"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (Application) TableName() string {
	return "applications"
}

// ApplicationType 应用类型常量
const (
	ApplicationTypePhotoSearch = 1 // 拍照搜题
)

// ApplicationStatus 应用状态常量
const (
	ApplicationStatusNormal = 1 // 正常
	ApplicationStatusFrozen = 2 // 冻结
)

// IsNormal 检查应用状态是否正常
func (a *Application) IsNormal() bool {
	return a.Status == ApplicationStatusNormal
}

// IsFrozen 检查应用是否被冻结
func (a *Application) IsFrozen() bool {
	return a.Status == ApplicationStatusFrozen
}

// ApplicationCreateRequest 创建应用请求
type ApplicationCreateRequest struct {
	Name string `json:"name" binding:"required,min=1,max=50" example:"我的搜题应用"`
	Type int    `json:"type" binding:"required,oneof=1" example:"1"`
}

// ApplicationUpdateRequest 更新应用请求
type ApplicationUpdateRequest struct {
	Name string `json:"name" binding:"required,min=1,max=50" example:"新的应用名称"`
}

// ApplicationStatusUpdateRequest 更新应用状态请求
type ApplicationStatusUpdateRequest struct {
	Status int `json:"status" binding:"required,oneof=1 2" example:"1"`
}

// ApplicationRateLimitUpdateRequest 更新应用限流配置请求
type ApplicationRateLimitUpdateRequest struct {
	RateLimitQPS    int `json:"rate_limit_qps" binding:"required,min=1,max=1000" example:"10"`
	RateLimitBurst  int `json:"rate_limit_burst" binding:"required,min=1,max=2000" example:"20"`
	RateLimitWindow int `json:"rate_limit_window" binding:"required,min=1,max=3600" example:"60"`
}

// ApplicationResponse 应用信息响应
type ApplicationResponse struct {
	ID              uint      `json:"id"`
	UserID          uint      `json:"user_id"`
	Name            string    `json:"name"`
	Type            int       `json:"type"`
	AppKey          string    `json:"app_key"`
	SecretKey       string    `json:"secret_key"`
	Status          int       `json:"status"`
	RateLimitQPS    int       `json:"rate_limit_qps"`
	RateLimitBurst  int       `json:"rate_limit_burst"`
	RateLimitWindow int       `json:"rate_limit_window"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// ApplicationListResponse 应用列表响应
type ApplicationListResponse struct {
	ID              uint      `json:"id"`
	Name            string    `json:"name"`
	Type            int       `json:"type"`
	AppKey          string    `json:"app_key"`
	Status          int       `json:"status"`
	RateLimitQPS    int       `json:"rate_limit_qps"`
	RateLimitBurst  int       `json:"rate_limit_burst"`
	RateLimitWindow int       `json:"rate_limit_window"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// ToResponse 转换为详细响应格式
func (a *Application) ToResponse() *ApplicationResponse {
	return &ApplicationResponse{
		ID:              a.ID,
		UserID:          a.UserID,
		Name:            a.Name,
		Type:            a.Type,
		AppKey:          a.AppKey,
		SecretKey:       a.SecretKey,
		Status:          a.Status,
		RateLimitQPS:    a.RateLimitQPS,
		RateLimitBurst:  a.RateLimitBurst,
		RateLimitWindow: a.RateLimitWindow,
		CreatedAt:       a.CreatedAt,
		UpdatedAt:       a.UpdatedAt,
	}
}

// ToListResponse 转换为列表响应格式（不包含SecretKey）
func (a *Application) ToListResponse() *ApplicationListResponse {
	return &ApplicationListResponse{
		ID:              a.ID,
		Name:            a.Name,
		Type:            a.Type,
		AppKey:          a.AppKey,
		Status:          a.Status,
		RateLimitQPS:    a.RateLimitQPS,
		RateLimitBurst:  a.RateLimitBurst,
		RateLimitWindow: a.RateLimitWindow,
		CreatedAt:       a.CreatedAt,
		UpdatedAt:       a.UpdatedAt,
	}
}

// GetTypeString 获取应用类型字符串
func (a *Application) GetTypeString() string {
	switch a.Type {
	case ApplicationTypePhotoSearch:
		return "拍照搜题"
	default:
		return "未知类型"
	}
}

// GetStatusString 获取应用状态字符串
func (a *Application) GetStatusString() string {
	switch a.Status {
	case ApplicationStatusNormal:
		return "正常"
	case ApplicationStatusFrozen:
		return "冻结"
	default:
		return "未知状态"
	}
}

// GetMaxRequestsPerWindow 获取每个窗口期内的最大请求数
func (a *Application) GetMaxRequestsPerWindow() int64 {
	return int64(a.RateLimitQPS * a.RateLimitWindow)
}

// GetRateLimitKey 获取应用的限流Redis键
func (a *Application) GetRateLimitKey() string {
	return fmt.Sprintf("app_rate_limit:%s", a.AppKey)
}

// ValidateRateLimitConfig 验证限流配置的合理性
func (a *Application) ValidateRateLimitConfig() error {
	if a.RateLimitQPS <= 0 {
		return fmt.Errorf("QPS限制必须大于0")
	}
	if a.RateLimitBurst <= 0 {
		return fmt.Errorf("突发请求数限制必须大于0")
	}
	if a.RateLimitWindow <= 0 {
		return fmt.Errorf("限流窗口时间必须大于0")
	}
	if a.RateLimitBurst < a.RateLimitQPS {
		return fmt.Errorf("突发请求数不能小于QPS限制")
	}
	return nil
}
