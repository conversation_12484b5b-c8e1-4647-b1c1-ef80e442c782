# API搜题测试工具使用指南

## 🎯 概述

我们创建了一个专门的HTML测试工具，用于测试完整的API搜题流程。该工具可以提交图片URL，并实时显示Qwen图像识别和DeepSeek题目解答的返回信息。

## 🌐 访问方式

- **测试界面**: http://localhost:3000/test.html
- **管理界面**: http://localhost:3000/admin.html

## 🔧 功能特性

### 📸 图片搜题测试
- ✅ **图片URL输入**: 支持任意图片URL
- ✅ **图片预览**: 实时预览要测试的图片
- ✅ **API密钥支持**: 可选的API认证
- ✅ **一键测试**: 自动执行完整搜题流程

### 📊 实时结果展示
- ✅ **双面板显示**: Qwen和DeepSeek结果并排显示
- ✅ **状态跟踪**: 实时显示每个步骤的执行状态
- ✅ **性能统计**: 显示响应时间和状态码
- ✅ **JSON格式化**: 美观的JSON结果展示

### 🎨 用户体验
- ✅ **响应式设计**: 支持桌面和移动设备
- ✅ **加载动画**: 优雅的加载状态提示
- ✅ **错误处理**: 详细的错误信息显示
- ✅ **操作便捷**: 支持键盘快捷键

## 🚀 使用流程

### 1. 基础测试
```
1. 打开测试界面: http://localhost:3000/test.html
2. 输入图片URL (默认已填入测试图片)
3. 点击"预览图片"确认图片可访问
4. 点击"开始搜题测试"执行完整流程
5. 查看Qwen识别结果和DeepSeek解答结果
```

### 2. 自定义测试
```
1. 替换图片URL为您要测试的图片
2. (可选) 输入API密钥进行认证
3. 执行测试并分析结果
4. 使用"清空结果"重置界面
```

## 📋 测试流程详解

### 第一步: Qwen图像识别
```
请求: POST /api/v1/api/search
Body: {
  "image_url": "图片URL"
}

响应: {
  "code": 200,
  "message": "识别成功",
  "data": {
    "question_info": {
      "question_type": "单选题",
      "question_text": "题目内容",
      "options": {
        "A": "选项A",
        "B": "选项B",
        "C": "选项C",
        "D": "选项D"
      }
    }
  }
}
```

### 第二步: DeepSeek题目解答
```
请求: POST /api/v1/api/search
Body: {
  "question_info": "Qwen识别的结果",
  "image_url": "图片URL"
}

响应: {
  "code": 200,
  "message": "解答成功",
  "data": {
    "analysis": "解题分析",
    "steps": ["步骤1", "步骤2"],
    "answer": "最终答案",
    "explanation": "详细解释"
  }
}
```

## 🎨 界面说明

### 测试表单区域
- **图片URL**: 输入要测试的图片地址
- **API密钥**: 可选的认证信息
- **操作按钮**: 
  - 🚀 开始搜题测试
  - 🗑️ 清空结果
  - 👁️ 预览图片

### 结果展示区域

#### Qwen-VL-Plus面板 (橙色)
- **状态指示**: 待测试/识别中/识别成功/识别失败
- **性能统计**: 响应时间、状态码
- **结果展示**: 格式化的JSON识别结果

#### DeepSeek-Chat面板 (绿色)
- **状态指示**: 待测试/解答中/解答成功/解答失败
- **性能统计**: 响应时间、状态码
- **结果展示**: 格式化的JSON解答结果

## 🔍 测试示例

### 默认测试图片
```
URL: http://solve.igmdns.com/img/23.jpg
类型: 数学选择题
预期: Qwen识别题目结构，DeepSeek提供解答
```

### 自定义测试
```
1. 数学题: 测试公式识别和计算过程
2. 语文题: 测试文字识别和理解分析
3. 英语题: 测试单词识别和语法解析
4. 物理题: 测试图表识别和原理解释
```

## 📊 性能监控

### 响应时间指标
- **Qwen识别**: 通常 1-3 秒
- **DeepSeek解答**: 通常 2-5 秒
- **总流程**: 通常 3-8 秒

### 状态码说明
- **200**: 请求成功
- **400**: 请求参数错误
- **401**: 认证失败
- **500**: 服务器内部错误
- **ERROR**: 网络连接错误

## 🛠️ 故障排除

### 常见问题

1. **图片加载失败**
   - 检查图片URL是否正确
   - 确认图片可以公开访问
   - 尝试使用HTTPS协议的图片

2. **API请求失败**
   - 确认API服务(8080端口)正在运行
   - 检查网络连接
   - 验证API密钥是否正确

3. **识别结果异常**
   - 检查图片质量和清晰度
   - 确认图片包含可识别的题目内容
   - 查看模型配置是否正确

### 调试建议
```
1. 使用浏览器开发者工具查看网络请求
2. 检查控制台是否有JavaScript错误
3. 对比不同图片的识别效果
4. 调整模型参数优化识别准确性
```

## 🎉 总结

这个API测试工具提供了：

- **完整流程测试**: 从图片输入到最终解答的全流程
- **实时状态监控**: 每个步骤的执行状态和性能指标
- **友好的用户界面**: 直观的操作和结果展示
- **灵活的测试选项**: 支持自定义图片和认证方式

通过这个工具，您可以方便地测试和验证API搜题功能的完整性和准确性，为系统优化提供有力支持！
