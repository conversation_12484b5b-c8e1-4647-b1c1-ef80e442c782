package main

import (
	"encoding/json"
	"fmt"
	"solve_api/internal/model"
)

func main() {
	// 模拟Qwen返回的数据
	qwenData := `{
		"类型": "单选题",
		"题目": "20、驾驶机动车在高速公路发生故障,需要停车排除故障时,以下做法先后顺序正确的是？①放置警告标志,转移乘车人员至安全处,迅速报警②开启危险报警闪光灯③将车辆移至不妨碍交通的位置④等待救援",
		"A": "③②①④",
		"B": "①②③④",
		"C": "②③①④",
		"D": "④③①②"
	}`

	// 解析JSON
	var rawData map[string]interface{}
	if err := json.Unmarshal([]byte(qwenData), &rawData); err != nil {
		fmt.Printf("JSON解析失败: %v\n", err)
		return
	}

	// 转换为QuestionStructure
	structure := &model.QuestionStructure{
		QuestionType: rawData["类型"].(string),
		QuestionText: rawData["题目"].(string),
		Options: map[string]string{
			"A": rawData["A"].(string),
			"B": rawData["B"].(string),
			"C": rawData["C"].(string),
			"D": rawData["D"].(string),
		},
	}

	fmt.Printf("原始数据:\n")
	fmt.Printf("题目类型: %s\n", structure.QuestionType)
	fmt.Printf("题目内容: %s\n", structure.QuestionText)
	fmt.Printf("选项A: %s\n", structure.Options["A"])
	fmt.Printf("选项B: %s\n", structure.Options["B"])
	fmt.Printf("选项C: %s\n", structure.Options["C"])
	fmt.Printf("选项D: %s\n", structure.Options["D"])

	// 测试预处理
	fmt.Printf("\n=== 开始预处理 ===\n")
	preprocessed, err := model.PreprocessQwenResult(structure)
	if err != nil {
		fmt.Printf("预处理失败: %v\n", err)
		return
	}

	fmt.Printf("\n预处理结果:\n")
	fmt.Printf("是否有效: %v\n", preprocessed.IsValid)
	fmt.Printf("错误信息: %s\n", preprocessed.ErrorMessage)
	fmt.Printf("题目类型: %s\n", preprocessed.QuestionType)
	fmt.Printf("题目内容: %s\n", preprocessed.QuestionText)
	fmt.Printf("选项A: %s\n", preprocessed.OptionsA)
	fmt.Printf("选项B: %s\n", preprocessed.OptionsB)
	fmt.Printf("选项C: %s\n", preprocessed.OptionsC)
	fmt.Printf("选项D: %s\n", preprocessed.OptionsD)

	// 测试缓存键生成
	cacheKey := model.GenerateCacheKeyFromPreprocessed(preprocessed)
	fmt.Printf("\n缓存键: %s\n", cacheKey)
}
